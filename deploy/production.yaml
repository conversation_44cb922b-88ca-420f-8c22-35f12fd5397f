apiVersion: apps/v1
kind: Deployment
metadata:
  name: hegui-frontend
  namespace: prj-icp
spec:
  replicas: 4
  selector:
    matchLabels:
      app: hegui-frontend
  template:
    metadata:
      annotations:
        cni.networking.kun/ipv4Enabled: "true"
      labels:
        app: hegui-frontend
    spec:
      containers:
        - name: hegui-frontend
          image: hub.ucloudadmin.com/hegui_front/hegui-frontend:{{ .TAG }}
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: 128Mi
              cpu: 200m
            limits:
              memory: 512Mi
              cpu: 1000m
          ports:
            - containerPort: 80
              name: http
          env:
            - name: VERSION
              value: v1

---
apiVersion: v1
kind: Service
metadata:
  name: hegui-frontend
  namespace: prj-icp
spec:
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: hegui-frontend
  type: ClusterIP