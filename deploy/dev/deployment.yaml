apiVersion: apps/v1
kind: Deployment
metadata:
  name: hegui-frontend-gray
  namespace: prj-icp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hegui-frontend-gray
  template:
    metadata:
      annotations:
        cni.networking.kun/ipv4Enabled: "true"
      labels:
        app: hegui-frontend-gray
    spec:
      containers:
        - name: hegui-frontend-gray
          image: hub.ucloudadmin.com/hegui_front/hegui-frontend
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: 128Mi
              cpu: 200m
            limits:
              memory: 512Mi
              cpu: 1000m
          ports:
            - containerPort: 80
              name: http
          env:
            - name: VERSION
              value: v1
