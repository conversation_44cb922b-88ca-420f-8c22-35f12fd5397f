FROM uhub.service.ucloud.cn/org_bdks4e/node:12.22.1 as build
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
COPY package.json ./
COPY package-lock.json ./
RUN npm install  --legacy-peer-deps  --registry=https://registry.npmmirror.com
COPY . ./
RUN npm run build
FROM hub.ucloudadmin.com/public/nginx:latest
COPY --from=build /app/build /usr/share/nginx/html
ADD  ./deploy /usr/share/nginx/html/build/
#ADD ./build/ /usr/share/nginx/html
ADD default.conf /etc/nginx/conf.d/default.conf
# 设置时间区
ENV TZ=Asia/Shanghai
RUN ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime  && \
    echo ${TZ} > /etc/timezone
EXPOSE 80
ENTRYPOINT nginx -g "daemon off;"
