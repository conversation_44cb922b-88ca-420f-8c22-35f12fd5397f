# 全局变量
variables:
  KUN_NAMESPACE: prj-icp
  KUN_IMAGE_PULL_SECRET_NAMES: "xiongqianhua"
  KUN_IMAGE_PUSH_SECRET: "xiongqianhua"
  CD_PROJECT: prj-icp
  KUBERNETES_CPU_REQUEST: "3000m"
  KUBERNETES_CPU_LIMIT: "6000m"
  KUBERNETES_MEMORY_REQUEST: "1024Mi"
  KUBERNETES_MEMORY_LIMIT: "4Gi"
  IMAGE_NAME: "hub.ucloudadmin.com/hegui_front/hegui-frontend"

stages:
  - BuildImage
  - PreDeploy
  - ProductionDeploy

before_script:
  # 首先我们需要为我们的镜像生成一个 tag，规则是：如果有 git tag，就使用 git tag，如果没有的话，就使用 git commit sha
  - IMAGE_TAG=${CI_COMMIT_SHA} && if [[ -n "$CI_COMMIT_TAG" ]]; then IMAGE_TAG=$CI_COMMIT_TAG ; fi

docker-image:
  tags:
    - uaek-c1
  stage: BuildImage
  image: hub.ucloudadmin.com/public/uaek-kaniko-executor:v1.3.0
  script:
    - if [[ -n "$CI_COMMIT_TAG" ]]; then cd $CI_PROJECT_DIR/envConfigs && sed -i 's/local/production/g' env.js; fi
    - if [[ "$CI_COMMIT_BRANCH" == dev ]]; then cd $CI_PROJECT_DIR/envConfigs && sed -i 's/local/test/g' env.js; fi
    - /kaniko/executor --snapshotMode=time --use-new-run -c $CI_PROJECT_DIR -f Dockerfile -d $IMAGE_NAME:$IMAGE_TAG
  only:
    - tags
    - dev
preDeploy:
  stage: PreDeploy
  tags:
    - uaek-c1
  variables:
    CD_CLUSTER: "uae-c1"
    cluster: staging
    CD_USERNAME: $KUN_USERNAME
    CD_PASSWORD: $KUN_PASSWORD
    CD_FILE: deploy/output/pre.yaml
    CD_RESOURCESET: hegui-frontend-gray
    CD_CREATE_JOB: "true"
  image: hub.ucloudadmin.com/uaek/uaek-ciclient:latest
  script:
    - cd $CI_PROJECT_DIR/deploy/dev && kustomize edit set image $IMAGE_NAME=$IMAGE_NAME:$IMAGE_TAG
    - cd $CI_PROJECT_DIR && mkdir deploy/output && kustomize build deploy/dev > ${CD_FILE}
    - cd $CI_PROJECT_DIR && /root/ciclient -version=$IMAGE_TAG
  only:
    - dev
production:
  stage: ProductionDeploy
  tags:
    - uaek-c1
  variables:
    CD_CLUSTER: "uae-c1"
    CD_RESOURCESET: hegui-frontend
    CD_USERNAME: $KUN_USERNAME
    CD_PASSWORD: $KUN_PASSWORD
    CD_CREATE_JOB: "true"
    cluster: staging
    CD_FILE: "deploy/output/production.yaml"
  image: hub.ucloudadmin.com/uaek/uaek-ciclient:latest
  script:
    - cd $CI_PROJECT_DIR/deploy/prod && kustomize edit set image $IMAGE_NAME=$IMAGE_NAME:$IMAGE_TAG
    - cd $CI_PROJECT_DIR && mkdir deploy/output && kustomize build deploy/prod > ${CD_FILE}
    - cd $CI_PROJECT_DIR && /root/ciclient -version=$IMAGE_TAG
  only:
    - tags
  when: manual
