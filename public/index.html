<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000">
    <title>合规运营平台</title>
    <script>!function(e){function t(a){if(i[a])return i[a].exports;var n=i[a]={exports:{},id:a,loaded:!1};return e[a].call(n.exports,n,n.exports,t),n.loaded=!0,n.exports}var i={};return t.m=e,t.c=i,t.p="",t(0)}([function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=window;t["default"]=i.flex=function(normal,e,t){var a=e||100,n=t||1,r=i.document,o=navigator.userAgent,d=o.match(/Android[\S\s]+AppleWebkit\/(\d{3})/i),l=o.match(/U3\/((\d+|\.){5,})/i),c=l&&parseInt(l[1].split(".").join(""),10)>=80,p=navigator.appVersion.match(/(iphone|ipad|ipod)/gi),s=i.devicePixelRatio||1;p||d&&d[1]>534||c||(s=1);var u=normal?1:1/s,m=r.querySelector('meta[name="viewport"]');m||(m=r.createElement("meta"),m.setAttribute("name","viewport"),r.head.appendChild(m)),m.setAttribute("content","width=device-width,user-scalable=no,initial-scale="+u+",maximum-scale="+u+",minimum-scale="+u),r.documentElement.style.fontSize=normal?"50px": a/2*s*n+"px"},e.exports=t["default"]}]);  flex(false,100, 1);</script>
    <link rel="shortcut icon" href="./favicon.ico">
    <script type="text/javascript" src="https://api.map.baidu.com/getscript?v=3.0&ak=UYijDvRGbN868GZFUGQPF6Pb268AILg3&s=1"></script>
    <script src="https://fastly.jsdelivr.net/npm/echarts@5.4.3/dist/extension/bmap.min.js"></script>
  </head>
  <body>
    <style>
      #loader-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100vw;
        height: 100vh;
        background-color: #f4f7f9;
      }
      #loader {
        position: relative;
        width: 128px;
        height: 128px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .load_15 {
        width: 40px;
        height: 40px;
        display: inline-block;
      }
      .load_15 .sk-cube {
        width: 33%;
        height: 33%;
        background-color: #1e90ff;
        float: left;
        -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
        animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
      }
      .load_15 .sk-cube1 {
        -webkit-animation-delay: 0.2s;
        animation-delay: 0.2s;
      }
      .load_15 .sk-cube2 {
        -webkit-animation-delay: 0.3s;
        animation-delay: 0.3s;
      }
      .load_15 .sk-cube3 {
        -webkit-animation-delay: 0.4s;
        animation-delay: 0.4s;
      }
      .load_15 .sk-cube4 {
        -webkit-animation-delay: 0.1s;
        animation-delay: 0.1s;
      }
      .load_15 .sk-cube5 {
        -webkit-animation-delay: 0.2s;
        animation-delay: 0.2s;
      }
      .load_15 .sk-cube6 {
        -webkit-animation-delay: 0.3s;
        animation-delay: 0.3s;
      }
      .load_15 .sk-cube7 {
        -webkit-animation-delay: 0s;
        animation-delay: 0s;
      }
      .load_15 .sk-cube8 {
        -webkit-animation-delay: 0.1s;
        animation-delay: 0.1s;
      }
      .load_15 .sk-cube9 {
        -webkit-animation-delay: 0.2s;
        animation-delay: 0.2s;
      }
      @-webkit-keyframes sk-cubeGridScaleDelay {
        0%,
        70%,
        100% {
          -webkit-transform: scale3D(1, 1, 1);
          transform: scale3D(1, 1, 1);
        }
        35% {
          -webkit-transform: scale3D(0, 0, 1);
          transform: scale3D(0, 0, 1);
        }
      }
      @keyframes sk-cubeGridScaleDelay {
        0%,
        70%,
        100% {
          -webkit-transform: scale3D(1, 1, 1);
          transform: scale3D(1, 1, 1);
        }
        35% {
          -webkit-transform: scale3D(0, 0, 1);
          transform: scale3D(0, 0, 1);
        }
      }
      .bouncing-text {
        font-size: 24px;
        font-weight: bold;
        display: inline-block;
        color: #1e90ff;
      }

      .bouncing-text span {
        display: inline-block;
        animation: bounce 0.4s infinite alternate;
      }

      @keyframes bounce {
        0% {
          transform: translateY(0);
        }
        100% {
          transform: translateY(-20px);
        }
      }

      .bouncing-text span:nth-child(1) {
        animation-delay: 0s;
      }

      .bouncing-text span:nth-child(2) {
        animation-delay: 0.1s;
      }

      .bouncing-text span:nth-child(3) {
        animation-delay: 0.2s;
      }

      .bouncing-text span:nth-child(4) {
        animation-delay: 0.3s;
      }

      .bouncing-text span:nth-child(5) {
        animation-delay: 0.4s;
      }
      /* 依此类推，为每个字符设置不同的动画延迟 */
    </style>
    <div id="root">
      <div id="loader-wrapper">
        <div id="loader">
          <div class="bouncing-text">
            <span>U</span><span>C</span><span>L</span><span>O</span
            ><span>U</span><span>D</span>
          </div>
          <div class="load_15">
            <div class="sk-cube sk-cube1"></div>
            <div class="sk-cube sk-cube2"></div>
            <div class="sk-cube sk-cube3"></div>
            <div class="sk-cube sk-cube4"></div>
            <div class="sk-cube sk-cube5"></div>
            <div class="sk-cube sk-cube6"></div>
            <div class="sk-cube sk-cube7"></div>
            <div class="sk-cube sk-cube8"></div>
            <div class="sk-cube sk-cube9"></div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
