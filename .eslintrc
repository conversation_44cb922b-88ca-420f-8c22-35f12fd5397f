{
  "env": {
      "browser": true,
      "es2021": true,
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
  ],
  "parser": "babel-eslint",
  "parserOptions": {
      "ecmaFeatures": {
          "jsx": true
      },
      "ecmaVersion": 12,
      "sourceType": "module"
  },
  "plugins": [
      "react",
      "react-native"
  ],
  "settings": {
    "react": {
      "version": "999.999.999"
    }
  },
  "rules": {
      "react/prop-types": 0, //允许在react组件定义中缺少props验证
      "no-mixed-spaces-and-tabs":0,//允许代码中出现空格和tab键混合的情况
      "eqeqeq": 2, //必须使用 === 和 !==
      "no-empty-function": 2, //禁止空函数
      //"no-multi-spaces": 2, //禁止使用多个空格
      // "no-trailing-spaces": 2, //禁止禁用行尾空格
      // "space-infix-ops": 2, // 要求操作符周围有空格
      // "space-in-parens": 2, //强制在圆括号内使用一致的空格
      //"no-var":2, //要求使用 let 或 const 而不是 var,
       "no-unused-vars": 2, //禁止出现未使用过的变量
       "no-redeclare": 2, //禁止重复声明变量
       "no-debugger": 2, //禁止使用debugger
  },
  "globals": {
      "object": true,
      "require": true
  },
}
