# 架构使用手册

开发环境项目运行：npm run dev
生产环境项目编译：npm run build

生产环境配置：
hegui-frontend/config/webpack.prod.js

开发环境配置：
hegui-frontend/config/webpack.dev.js

复用配置：
hegui-frontend/config/webpack.base.js

路径配置：
hegui-frontend/config/paths.js

在打包发送到生产环境之前需要手动设置此环境的请求路径是灰度 test 还是线上 production，如果是本地开发就 local，文件位置：envConfigs/env.js。

# CI/CD 版本发布

## 参考.gitlab-ci.yml 文件，灰度在 dev 分支，线上在 master 分支，发布时注意 envConfigs/env.js 的配置，dev 分支修改为 test，master 分支修改为 production

# CI/CD 鲲平台线上地址https://kun.ucloudadmin.com/deploy/resourceset

线上地址：https://hegui.ucloudadmin.com/ 鲲平台线上资源集：hegui-frontend

灰度地址：https://hegui-gray.ucloudadmin.com/ 鲲平台灰度资源集：hegui-frontend-gray

镜像仓库：hub.ucloudadmin.com/hegui_front/hegui-frontend

# 增加 MenuList（非子菜单）

1. 在 src/layouts/menuList 中编辑新菜单
2. 在 utils/Permission.json 中编辑 hegui.tag, 仿照新增 tag，tag_name 和 menuList 的 title 相同即可

# 权限配置
本地开发鉴权不走 uauth 可看 utils/requst ssoAuthApi
layouts/menuList 设置路由路径和菜单名称
遍历sso.GetPermission获取到的tag，tag_name作为连接键匹配menuList中的title
