import React from 'react';
import {
    createRoot
} from 'react-dom/client';
import { BrowserRouter as Router } from 'react-router-dom';
import '@babel/polyfill';
//import 'antd/dist/antd.css'
import App from './App';
import { createStore, compose, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';//引入异步中间件
import { Provider } from 'react-redux';
import reducer from './reducer';
import '@/styles/base.scss';
import ErrorBoundary from './components/ErrorBoundary';
const env = require('../envConfigs/env').default;

const win = window;

const middleware = [];
if( env !== 'production' ) {
    //用于检查reducer是否改变的store的数据 react16已经弃用
    middleware.push(require('redux-immutable-state-invariant').default());
}
middleware.push(thunk);
const reduxEnhancers = compose(
    applyMiddleware(...middleware),
    (win && win.devToolsExtension ? win.devToolsExtension() : f => f)
);
const store = createStore(reducer, {}, reduxEnhancers);


const pageRoute = (
  <ErrorBoundary>
    <Provider store={store}>
      <Router>
        <App />
      </Router>
    </Provider>
  </ErrorBoundary>
)
createRoot(document.getElementById('root')).render(pageRoute)
