import React, { PureComponent, Fragment } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { Table, Tooltip, Popconfirm, notification } from 'antd';
import { policeCopy } from '../../utils/request'

const statusList = {
  "Finished": "已完成",
  "Error": "出错",
  "Wait": "等待",
  "Doing": "执行中",
  "Terminated": "已终止"
}

class copyTable extends PureComponent {
  constructor(props) {
    super(props)
    this.props = props
    this.state = { isDowning: {}};
  }

  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter);
  }

  terminate(options = {}) {
    let self = this
    //重发消息
    let action = options.Action
    delete options.Action
    self.setState({ loading: true })

    policeCopy(action, options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '正在结束中' : resp.Message || resp.RetCode + "发送失败"

        notification.open({
          message: message,
        });

        self.setState({
          loading: false
        })
      })
      .then(
        this.props.fetchInfo
      )
      .catch(err => {
        // 报错
        notification['error']({
          message: '结束任务失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  delete(options = {}) {
    let self = this
    //重发消息
    let action = options.Action
    delete options.Action
    self.setState({ loading: true })

    policeCopy(action, options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '删除成功' : resp.Message || resp.RetCode + "失败"

        notification.open({
          message: message,
        });

        self.setState({
          loading: false
        })
        window.location.reload();
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '删除任务失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  render() {
    const { data, pagination, loading } = this.props;
    const terminateAction = this.props.taskName==='GetCopyTaskList'?'TerminateCopyTask':'TerminateBackupTask'
    const deleteAction = this.props.taskName==='GetCopyTaskList'?'DeleteCopyTask':'DeleteBackupTask'
    const columns = [
      {
        title: '需求方',
        dataIndex: 'Requestor',
      }, {
        title: '提交人',
        dataIndex: 'Committer',
      },
      {
        title: '公司名称列表',
        dataIndex: 'CompanyNameList',
        key: "Params",
        render: (value) => {
          if (value && value.length !== 1) {
            return (
              <Fragment>
                <Tooltip title={value.join(';')} >
                  <span>{value[0] + '..'}</span>
                </Tooltip>
              </Fragment>
            )
          } else {
            return (
              <Fragment>
                <span>{value || ''}</span>
              </Fragment>
            )
          }
        }
      },
      {
        title: '数据盘总容量',
        dataIndex: 'DiskCapacity',
      },
      {
        title: '目标盘总容量',
        dataIndex: 'TargetDiskCapacity',
      },
      {
        title: '创建时间',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        defaultSortOrder: 'ascend',
      },
      // {
      //   title: '磁盘列表',
      //   dataIndex: 'DiskCopyTaskList',
      // },
      {
        title: '进度',
        dataIndex: 'Progress',
      },
      {
        title: '状态',
        dataIndex: 'Status',
        render: val => <span>{statusList[val]}</span>
      },
      {
        title: '通知详情',
        render: (val, row) => {
          let urlAddress = "/PoliceCopy/BatchDetail/" + row.Id +"?taskName=" + this.props.taskName
          return (
            <Fragment>
              {
                <Link to={urlAddress} target="_blank">详情 </Link>
              }
              {/* 如果是已终止，不允许再点 */}
              {
                row.Status !== "Terminated" ?
                  <Popconfirm title="是否确认终止任务？" onConfirm={() => this.terminate({ Action: terminateAction, Id: row.Id })}>
                    <a href="#!">终止</a>
                  </Popconfirm>
                  :
                  <p> 终止</p>
              }
            </Fragment>
          )
        },
      },
      {
        title: '删除',
        dataIndex: 'delete',
        render: (val, row) => {

          return (
            <Fragment>
                    {
                  <Popconfirm title="是否确认删除任务？" onConfirm={() => this.delete({ Action: deleteAction, Id: row.Id })}>
                    <a href="#!">删除</a>
                  </Popconfirm>
              }
            </Fragment>
          )
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'copyTable'}>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    );
  }
}

export default copyTable;
