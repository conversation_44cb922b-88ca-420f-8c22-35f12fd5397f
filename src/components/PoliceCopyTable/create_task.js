import React, { PureComponent } from 'react';
import { Table, Button, Modal, notification } from 'antd';
import _ from 'lodash'
import { policeCopy } from '../../utils/request'

class copyTable extends PureComponent {
  constructor(props) {
    super(props)
    this.props = props
    this.state = {
      visible: false,
      isDowning: {},
      selectValue: [],
      IsBackup:props.IsBackup,
      taskName:props.taskName,
      TaskNameType:props.taskName==="创建备份任务"?'生成备份':'生成任务'
    };
  }

  showModal = (DiskInfo) => {
    this.setState({
      visible: true,
      diskInfo: DiskInfo,
    });
  }

  handleOk = e => {
    console.log(e);
    this.setState({
      visible: false,
    });
  };

  handleCancel = e => {
    console.log(e);
    this.setState({
      visible: false,
    });
  };


  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter);
  }
  submitCase = () => {
    let self = this ;
    // 请求，生成任务CreateCopyTask
    console.log(JSON.stringify(this.state), JSON.stringify(this.props))

    if (this.state.selectValue.length === 0) {
      notification['error']({
        message: '无可执行的任务',
        description: "可选择的任务列表为空"
      })
      return;
    }

    let selectValue = this.state.selectValue

    // 取选择到的磁盘列表去重
    let targetDiskList = []
    selectValue.forEach(hostelement => {
      hostelement.DiskList.forEach(element => {
        // 确定是否存在
        if (_.findIndex(targetDiskList, { 'Id': element.TargetDiskId }) === -1) {
          targetDiskList.push(
            {
              "Id": element.TargetDiskId,
              "ZoneId": element.ZoneId,
              "IP": element.TargetDiskIP,
              "Path": element.TargetDiskPath,
              "Capacity": element.TargetDiskCapacity
            }
          )
        }

      })
    });


    let option ={
      "Requestor": selectValue[0].Requestor,
      "HostIdList": selectValue.map(item => item.HostId),
      "TargetDiskList": targetDiskList,
    }
    let requestAction = 'CreateBackupTask'
    if(self.state.taskName !== '创建备份任务'){
      requestAction = 'CreateCopyTask'
      option["IsBackup"] = self.props.IsBackup
    }
    policeCopy(requestAction, option)
      .then(resp => {
        let message = resp.RetCode === 0 ? '正在生成任务中' : resp.Message || resp.RetCode + "生成失败"
        notification.open({
          message: message,
        });

        let taskId = resp.CopyTask.Id

        window.location.href = 'BatchDetail/' + taskId;    // console.log(this.state.selectValue)

      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败失败',
          description: err.message || '内部错误'
        })
        return;
      })


    // 转跳，直接转到相产ID的任务中
  }

  render() {
    const { data, pagination, loading } = this.props;

    const diskList = [{
      title: '数据盘Id',
      dataIndex: 'DiskId',
      width: '12%',
      key: 'DiskId',
    }, {
      title: '资源名称',
      dataIndex: 'DiskName',
      width: '12%',
      key: 'DiskName',
    },
    {
      title: '资源类型',
      dataIndex: 'Type',
      width: '12%',
      key: 'Type',
    },
    {
      title: '容量',
      dataIndex: 'Capacity',
      width: '12%',
      key: 'Capacity',
    },
    {
      title: '目标盘IP',
      dataIndex: 'TargetDiskIP',
      width: '12%',
      key: 'TargetDiskIP',
    },
    {
      title: '目标盘地址',
      dataIndex: 'TargetDiskPath',
      width: '12%',
      key: 'TargetDiskPath',
    },
    {
      title: '目标盘',
      dataIndex: 'TargetDiskCapacity',
      width: '12%',
      key: 'TargetDiskCapacity',
    }]

    const columns = [
      {
        title: '公司Id',
        dataIndex: 'CompanyId',
        key: 'CompanyId',
        width: '12%',

      },
      {
        title: '云主机Id',
        dataIndex: 'HostId',
        key: 'HostId',
        width: '12%',
      },
      {
        title: '容量',
        dataIndex: 'Capacity',
        width: '12%',
        key: 'Capacity',
      },
      {
        title: '详情',
        render: (val, row) => {
          return (
            <div>
              <Button onClick={() => this.showModal(row.DiskList)}>查看</Button>
            </div>);
        }
      }
    ]

    const rowSelection = {
      onChange: (selectedRowKeys, selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        this.setState({ selectValue: selectedRows })
        console.log(this.state, 'this.state3')

      },
      onSelect: (record, selected, selectedRows) => {
        console.log(record, selected, selectedRows);
        this.setState({ selectValue: selectedRows })
        console.log(this.state, 'this.state2')

      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        console.log(selected, selectedRows, changeRows);
        this.setState({ selectValue: selectedRows })
        console.log(this.state, 'this.state1')
      },
    };


    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };
    // const [checkStrictly, setCheckStrictly] = React.useState(false);

    return (
      <div className={'copyTable'}>
        <Modal
          title="封禁域名列表"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          footer={null}
        >
          <Table dataSource={this.state.diskInfo} columns={diskList} />
        </Modal>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          expandable={{
            expandedRowRender: record => <p style={{ margin: 0 }}>{record.Description}</p>,
            rowExpandable: record => record.name !== 'Not Expandable',
          }}
          rowSelection={{ ...rowSelection }}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />

        <Button type="primary" onClick={this.submitCase} style={{ margin: 16 }}>
         {this.state.TaskNameType}
        </Button>
      </div>
    );
  }
}

export default copyTable;
