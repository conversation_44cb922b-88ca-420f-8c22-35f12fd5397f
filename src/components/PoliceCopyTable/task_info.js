import React, { PureComponent, Fragment, Button } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { Table, Tooltip, Popconfirm, notification } from 'antd';
import styles from './index.less';
import { policeCopy } from '../../utils/request'

const statusList = {
  "Finished": "已结束",
  "Error": "出错",
  "Wait": "等待中",
  "Doing": "发送完成",
  "Terminated": "已终止"
}

class copyTable extends PureComponent {
  constructor(props) {
    super(props)
    this.props = props
    this.state = { isDowning: {} };
  }

  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter);
  }

  terminate(options = {}) {
    let self = this
    //重发消息
    let action = options.Action
    delete options.Action
    self.setState({ loading: true })
    policeCopy(action, options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '正在发送信息中' : resp.Message || resp.RetCode + "发送失败"

        notification.open({
          message: message,
        });

        self.setState({
          loading: false
        })
      })
      .then(
        this.props.fetchInfo
      )
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  render() {
    const { data, pagination, loading } = this.props;

    const columns = [
      {
        title: '需求方',
        dataIndex: 'Requestor',
      }, {
        title: '提交人',
        dataIndex: 'Committer',
      },
      {
        title: '公司名称列表',
        dataIndex: 'CompanyNameList',
        key: "Params",
        render: (value) => {
          if (value.length != 1) {
            return (
              <Fragment>
                <Tooltip title={value.join(';')} >
                  <span>{value[0] + '..'}</span>
                </Tooltip>
              </Fragment>
            )
          } else {
            return (
              <Fragment>
                <span>{value}</span>
              </Fragment>
            )
          }
        }
      },
      {
        title: '数据盘总容量',
        dataIndex: 'DiskCapacity',
      },
      {
        title: '目标盘总容量',
        dataIndex: 'TargetDiskCapacity',
      },
      {
        title: '创建时间',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        defaultSortOrder: 'ascend',
      },
      // {
      //   title: '磁盘列表',
      //   dataIndex: 'DiskCopyTaskList',
      // },
      {
        title: '进度',
        dataIndex: 'Progress',
      },
      {
        title: '状态',
        dataIndex: 'Status',
        render: val => <span>{statusList[val]}</span>
      },
      {
        title: '通知详情',
        render: (val, row, index) => {
          let urlAddress = "/PoliceReCopy/BatchDetail/" + row.Id
          return (
            <Fragment>
              {
                <Link to={urlAddress}>详情 </Link>
              }

              {/* 如果是已终止，不允许再点 */}
              {
                row.Status !== "Terminated" ?
                  <Popconfirm title="是否确认终止任务？" onConfirm={() => this.terminate({ Action: 'TerminateCopyTask', Id: row.Id })}>
                    <a href="#">终止</a>
                  </Popconfirm>
                  :
                  <p> 终止</p>
              }

            </Fragment>
          )
        },
      }
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'copyTable'}>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    );
  }
}

export default copyTable;
