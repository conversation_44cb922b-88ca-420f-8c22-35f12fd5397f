import React, { Component } from "react";
import { Button, message } from "antd";
import Viewer from "react-viewer";
import request from "../../utils/request";

class ButtonImageViewer extends Component {
  state = {
    modalVisible: false,
    name: this.props.name || "图片",
    imgUrl: "",
    style: {
      ...this.props.style,
      display: "inline-block",
    },
  };
  constructor(props) {
    super(props);
    this.GetPictureUrl(this.props.src);
  }
  showPicture = () => {
    this.setState({ modalVisible: true });
  };
  hidePicture = () => {
    this.setState({ modalVisible: false });
  };
  GetPictureUrl = async (name) => {
    let res = await request("GetPictureUrl", { FileName: name });
    if (res.RetCode === 0) {
      this.setState({ imgUrl: res.URL });
    } else {
      message.error("接口请求出错！");
    }
  };
  render() {
    const { style, name, modalVisible, imgUrl } = this.state;

    return imgUrl.includes("pdf") ? (
      <a href={imgUrl} target="_blank" rel="noreferrer">
        <Button >图片</Button>
      </a>
    ) : (
      <div style={style}>
        <Button onClick={this.showPicture}>图片</Button>
        <Viewer
          visible={modalVisible}
          onClose={this.hidePicture}
          images={[{ src: imgUrl, alt: name }]}
        />
      </div>
    );
  }
}

export default ButtonImageViewer;
