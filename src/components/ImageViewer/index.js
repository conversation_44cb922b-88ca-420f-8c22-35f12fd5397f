import React, { Component } from 'react';
import Viewer from 'react-viewer';

class ImageViewer extends Component {
	state = { 
		modalVisible: false,
		name: this.props.name || '图片',
		style: {
			...this.props.style,
			display: "inline-block"
		}
	}
	showPicture = () => {
		this.setState({ modalVisible: true })
	}
	hidePicture = () => {
		this.setState({ modalVisible: false })
	}
	render() {
		const { style, name, modalVisible } = this.state
		return (
			<div style={style}>
				<div style={{ textAlign: 'center' }}>{name}</div>
				<img alt={this.state.name} onClick={this.showPicture} src={this.props.src} style={{ width: 100, height: 100, cursor: 'pointer' }} />
				<Viewer
					visible={modalVisible}
					onClose={this.hidePicture}
					images={[{ src: this.props.src, alt: name }]}
				/>
			</div>
		)
	}
}

export default ImageViewer