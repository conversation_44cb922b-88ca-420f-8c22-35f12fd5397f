import React, { PureComponent, Fragment } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Table } from 'antd'

class ExclusiveCloudTable extends PureComponent {
	handleTableChange(pagination, filters, sorter) {
		this.props.onChange(pagination, filters, sorter)
	}

	render() {
		const authStateMap = {
			NOT_CERTIFIED: '未认证',
			CERTIFIED: '认证通过',
			LEGAL_NOT_PASSED: '法务审核未通过',
			_LEGAL_IN_AUDIT: '法务审核中',
			LEGAL_PASSED: '法务审核通过',
			FINANCE_NOT_PASSED: '财务审核未通过',
      _FINANCE_IN_AUDIT: '财务审核中',
			FINANCE_PASSED: '财务审核通过'
		}
		const { data, pagination, loading, formValues } = this.props
		const columns = [
			{
				title: '公司Id',
				dataIndex: 'CompanyID'
			},
			{
				title: '公司名/用户名',
				dataIndex: 'CompanyName',
				render: (val, record) => {
					if(record.AuthType === 1){
						return record.UserName || '暂无'
					}
					return val || '暂无'
        		},
			},
			{
				title: '创建时间',
				dataIndex: 'CreateTime',
				render: (val) => <span>{val ? moment((val * 1000)).format('YYYY-MM-DD HH:mm:ss') : '暂无'}</span>
			},
			{
				title: '认证状态',
				dataIndex: 'AuthState',
				render: val => authStateMap[val] || '暂无'
			},
			{
				title: '操作',
				dataIndex: 'Id',
				render: (_val, row) => {
					let url = `/exclusiveCloudAuditDetail/${row.CompanyID}/${row.AuthState}`
          if(formValues.TargetEnv){
            url = url + '/' + formValues.TargetEnv
          }
					let action = '详情';
					let path = {
						pathname: url
					}
					return (
						<Fragment>
							<Link to={{ ...path }} target='_blank'>{action}</Link>
						</Fragment>
					)
				},
			},
		]

		const paginationProps = {
			showSizeChanger: true,
			showQuickJumper: true,
			...pagination,
		}

		return (
			<div className={'ExclusiveCloudTable'}>
				<Table
					loading={loading}
					rowKey={record => { return (record.CompanyID + record.CreateTime) }}
					dataSource={data}
					columns={columns}
					pagination={paginationProps}
					onChange={this.handleTableChange.bind(this)}
				/>
			</div>
		)
	}
}

export default ExclusiveCloudTable
