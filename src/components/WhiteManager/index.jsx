import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Modal,
  Row,
  Col,
  Input,
  Card,
  Table,
  notification
} from "antd";
import api from "../../utils/request";
const WhiteManager = () => {
  const [open, setOpen] = useState(false);
  const [whiteList, setWhiteList] = useState([]);
  const [inputId, setInputId] = useState("");
  const [addLoadings, setAddLoadings] = useState(false);
  const [listLoadings, setListLoadings] = useState([]);
  useEffect(() => {
    getWhiteList();
  }, []);
  const showModal = () => {
    setOpen(true);
  };
  const handleOk = () => {
    setOpen(false);
  };
  const handleCancel = () => {
    setOpen(false);
  };
  const pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
    onChange: (page,pageSize) => {
      pagination.current = page;
      pagination.pageSize = pageSize;
    },
  };
  const handleListDelete = (e,i) => {
    let options = {
      CompanyId: Number(e.CompanyId),
    };
    let loadings = [...listLoadings]
    loadings[i] = true
    setListLoadings(loadings)
    api("RemoveRiskFilterCompany", options)
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode !== 0) {
          message = resp.Message || resp.RetCode + "请求失败";
        } else {
          getWhiteList();
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      }).finally(() =>{
        loadings[i] = false
        setListLoadings(loadings)
      });
  };

  const columns = [
    {
      title: "公司Id",
      dataIndex: "CompanyId",
      key: "CompanyId",
    },
    {
      title: "公司名称",
      dataIndex: "CompanyName",
      key: "CompanyName",
    },
    {
      title: "事业部",
      dataIndex: "BU",
      key: "BU",
    },
    {
      title: "操作",
      dataIndex: "Operator",
      key: "Operator",
      render: (val, record) => {
        return (
          <Button
            loading = {listLoadings[val]}
            type="primary"
            onClick={() => {
              handleListDelete(record, val);
            }}
          >
            删除
          </Button>
        );
      },
    },
  ];

  const getWhiteList = () => {
    api("GetRiskFilterCompanyList")
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode === 0) {
          setWhiteList(resp.Rows);
          return;
        } else {
          message = resp.Message || resp.RetCode + "请求失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  const GetCompanyInfo = () => {
    let options = {
      CompanyId: Number(inputId),
    };
    return api("GetCompanyInfo", options)
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode !== 0) {
          message = resp.Message || resp.RetCode + "请求失败";
        } else {
          return resp.CompanyInfo;
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  const addWhiteList = async () => {
    let companyInfo = await GetCompanyInfo();
    let options = {
      CompanyId: companyInfo.CompanyId,
      CompanyName: companyInfo.CompanyName,
      BU: companyInfo.BU,
    };
    setAddLoadings(true)
    api("AddRiskFilterCompany", options)
      .then((resp) => {
        let message = "请求成功";
        setInputId("");
        if (resp.RetCode !== 0) {
          message = resp.Message || resp.RetCode + "请求失败";
        } else {
          getWhiteList();
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      }).finally(() =>{
        setAddLoadings(false)
      });
  };

  return (
    <>
      <Button type="primary" onClick={showModal}>
        加白管理
      </Button>
      <Modal
        title="风险客户预警-加白管理"
        visible={open}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
      >
        <Row type="flex" align="middle" gutter={16}>
          <Col span={2} style={{ fontSize: 15, color: "#111" }}>
            新增加白:
          </Col>
          <Col span={20}>
            <Input
              placeholder="请输入公司ID"
              value={inputId}
              onChange={(e) => {
                setInputId(e.target.value);
              }}
            />
          </Col>
          <Col span={2}>
            <Button type="primary" onClick={addWhiteList} loading={addLoadings}>
              新增
            </Button>
          </Col>
        </Row>
        <Card title="已加白列表" style={{ marginTop: 15 }}>
          <Table
            dataSource={whiteList}
            columns={columns}
            pagination={pagination}
          />
        </Card>
      </Modal>
    </>
  );
};
export default WhiteManager;
