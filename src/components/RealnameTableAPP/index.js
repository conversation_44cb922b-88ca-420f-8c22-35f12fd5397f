import React, { PureComponent, Fragment } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Table } from 'antd'
const FlowStatus = {
	Prepare:'准备',
	Init:'初始化',
	Abort:'主动结束（客户）',
	Manual:'待人工审核',
	ManualRejected:'人工审核驳回',
	ManualResolve:'人工审核通过',
	Finish:'完成'
}
class RealnameTableAPP extends PureComponent {
	handleTableChange(pagination, filters, sorter) {
		this.props.onChange(pagination, filters, sorter)
	}

	render() {
		const { data, pagination, loading} = this.props

		const columns = [
			{
				title: '公司Id',
				dataIndex: 'CompanyId',
			},
			{
				title: '用户名',
				dataIndex: 'IdCardName',
				render: (val) => val || '暂无',
			},
			{
				title: '认证类型',
				dataIndex: 'AuthType',
				render: (val) => <span>{val || '暂无'}</span>,
			},
			{
				title: '创建时间',
				dataIndex: 'CreateTime',
				render: (val) => <span>{ val ? moment((val * 1000)).format('YYYY-MM-DD HH:mm:ss') : '暂无'}</span>
			},
			{
				title: '认证状态',
				dataIndex: 'FlowStatus',
				render:val => FlowStatus[val]
			},	{
				title: '认证方式',
				dataIndex: 'KYCType',
				render: (val) => val || '暂无',
			},
			{
				title: '操作',
				dataIndex: 'Id',
				render: (val, row) => {
					let url = '/APPAuditDetails/' + row.FlowId+'&'+ row.Id
					let action = row.FlowStatus.indexOf("Manual") > -1 ? '审核':'详情';
					let path = {
						pathname: url,
						state:row
					}
					return (
						<Fragment>
							<Link to={{...path}} target='_blank'>{action}</Link>
						</Fragment>
					)
				},
			},
		]

		const paginationProps = {
			showSizeChanger: true,
			showQuickJumper: true,
			...pagination,
		}

		return (
			<div className={'RealnameTableAPP'}>
				<Table
					loading={loading}
					rowKey={record => { return (record.CompanyId+record.CreateTime) }}
					dataSource={data}
					columns={columns}
					pagination={paginationProps}
					onChange={this.handleTableChange.bind(this)}
				/>
			</div>
		)
	}
}

export default RealnameTableAPP
