import React, { PureComponent,Fragment } from 'react'
import moment from 'moment'
import { Table, notification  } from 'antd'
import { Link } from 'react-router-dom'
import request from '../../utils/request'



class HistoryTable extends PureComponent {
  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter)
  }

  

  update(options = {}) {
    let self = this
    //重发消息
    let action = options.Action
    delete options.Action
    self.setState({ loading: true })

    request(action, options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '设置中' : resp.Message || resp.RetCode + "设置失败"
        notification.open({
          message: '发送中',
          description: message,
        });
        self.setState({
          loading: false
        })
      })
      .then(
        this.props.fetchInfo
      )
      .catch(err => {
        // 报错
        notification['error']({
          message: '设置失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  render() {
    // console.log(this.props)
    const { data, pagination, loading } = this.props
    const columns = [
      {
        title: '公司Id',
        fixed:'left',
        dataIndex: 'CompanyId',
      },
      {
        title: '公司名',
        dataIndex: 'CompanyName',
      },
  
      {
        title: '客户经理',
        dataIndex: 'Manager',
        render: (val) => {
          return val === '' ? '-' : val
        }
      },
      {
        title: '认证状态',
        filters: [{
          text: '已认证',
          value: '已认证',
        },
        {
          text: '未认证',
          value: '未认证',
        },
        {
          text: '证照通过',
          value: '证照通过',
        },
        {
          text: '证照驳回',
          value: '证照驳回',
        },
        {
          text: '财务驳回',
          value: '财务驳回',
        }],
        onFilter: (value, record) =>{
          // console.log(record.AuditState,record.CompanyId)
          return record.AuditState === value
        } ,
				render: (val, row) => {
					let url = '/realname/detail/' + row.CompanyId
					let path = {
						pathname: url,
						state: row,
					}
					return (
						<Fragment>
							<Link to={path}>{row.AuditState}</Link>
						</Fragment>
					)
				},
			},
      {
        title: '当前权限',
        dataIndex: 'BuyPermission',
      },
      {
        title: '操作',
        dataIndex: 'Result',
      },
      {
        title: '操作人',
        dataIndex: 'Operator',
      },
      {
        title: '操作时间',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '操作结果',
        dataIndex: 'Remark',
        render:val=><span title={val}>{val && val.length>40?`${val.substring(0,40)}...`:val}</span>
      },
      // {
      //   title: '操作',
      //   render: (val, row) => {
      //     if (row.AuditState === '已认证') {
      //       return ''
      //     } else if (row.BuyPermission === "有购买权限") {
      //       return (
      //         <span>
      //           <Popconfirm title="是否关闭全部机房购买权限？" onConfirm={() => this.update({ Action: "UpdateBuyPermissionBit", CompanyId: row.CompanyId, CanBuy: 'false' })}>
      //             <Button >
      //               关闭购买权限
      //             </Button>
      //           </Popconfirm>
      //         </span>
      //       )
      //     } else {
      //       return (
      //         <span>
      //           <Popconfirm title="是否打开全部机房购买权限？"  onConfirm={() => this.update({ Action: "UpdateBuyPermissionBit", CompanyId: row.CompanyId, CanBuy: 'true' })}>
      //             <Button>打开购买权限</Button>
      //           </Popconfirm>
      //         </span>
      //       )
      //     }
      //   },
      // },
    ]

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    }

    console.log(data)
    return (
      <div className={'HistoryTable'}>
        <Table
          loading={loading}
          scroll={{x:1400}}
          rowKey={record => record.Id }
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    )
  }
}

export default HistoryTable
