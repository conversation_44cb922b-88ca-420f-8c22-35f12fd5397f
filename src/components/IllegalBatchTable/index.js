import React, { PureComponent, Fragment } from 'react';
import { Link } from 'react-router-dom';
import { Table, Button, Modal } from 'antd';

class BatchTable extends PureComponent {
 

  state = {
    loading: false,
    iconLoading: false,
    visible: false,
  };


  handleTableChange  = (pagination, filters, sorter) => {
    this.props.onChange(pagination, filters, sorter);
  }
  showModal = (data) => {
    this.setState({
      visible: true,
      blockInfo: data,
    });
  }

  handleOk = (e) => {
    console.log(e);
    this.setState({
      visible: false,
    });
  }

  handleCancel = (e) => {
    console.log(e);
    this.setState({
      visible: false,
    });
  }

  render() {
    const { data, pagination, loading } = this.props;

    const blockInfoColumns = [{
      title: 'Ip',
      dataIndex: 'Ip',
    }, {
      title: '网址',
      dataIndex: 'Url'
    }, {
      title: '敏感信息',
      dataIndex: 'IllegalDetail',
    }];

    const columns = [
      {
        title: '组织Id',
        dataIndex: 'OrgId',
      },
      {
        title: '公司名',
        dataIndex: 'CompanyName',
      },
      {
        title: '客户等级',
        dataIndex: 'VipLevel',
      }, {
        title: '客户经理',
        dataIndex: 'Manager',
      }, {
        title: '所属BU',
        dataIndex: 'BU',
      }, {
        title: '发送状态',
        dataIndex: 'Status',
        filters: [{
          text: '发送中',
          value: '发送中',
        }, {
          text: '发送失败',
          value: '发送失败',
        }, {
          text: '发送成功',
          value: '发送成功',
        }, {
          text: '待发送',
          value: '待发送',
        }, {
          text: '禁止发送',
          value: '禁止发送',
        }, {
          text: '超时未响应',
          value: '超时未响应',
        }],
        onFilter: (value, record) => record.Status.indexOf(value) === 0,
      }, 
      {
        title: '客户渠道',
        dataIndex: 'Channel'
      },
      {
        title: '封禁信息',
        render: (val, row) => {
          let data = row.BlockInfo
          return (
            <div>
              <Button onClick={()=>{
                this.showModal(data)
              }}>查看</Button>
            </div>);
        }
      },
      {
        title: '通知详情',
        render: (val, row) => {
          let urlAddress = "/IllegalMessage/NotifyInfodetail/" + row.OrgId + "|" + this.props.batchId
          return (
            <Fragment>
              { 
                !row.NotifyInfo || row.NotifyInfo.length === 0 ? '处理'
                : <Link to={urlAddress}>处理</Link>
              }
            </Fragment>
          )
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'BatchTable'}>
        <Modal
          title="封禁域名列表"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          footer={null}
        >
          <Table dataSource={this.state.blockInfo} columns={blockInfoColumns} style={{wordBreak:'break-all'}}/>
        </Modal>
        <Table
          loading={loading}
          rowKey={record => record.BatchId}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    );
  }
}

export default BatchTable;