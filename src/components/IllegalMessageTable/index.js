import React, { PureComponent, Fragment } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { Table, notification, Button, Icon } from 'antd';
// import XLSX from 'xlsx';
import request from '../../utils/request'

class messageTable extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { isDowning: {} };
  }
  // state = {}
  handleTableChange = (pagination, filters, sorter) => {
    this.props.onChange(pagination, filters, sorter);
  }

  render() {
    const { data, pagination, loading } = this.props;

    const columns = [
      {
        title: '批次Id',
        dataIndex: 'Id',
      }, {
        title: '批次备注',
        dataIndex: 'Description',
      },
      {
        title: '创建时间',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        defaultSortOrder: 'ascend',
      },
      // {
      //   title: '批次备注',
      //   dataIndex: 'Description',
      // },
      {
        title: '批次状态',
        dataIndex: 'Status',
      },
      {
        title: '批次详情',
        render: (val, row) => {
          let url = "/IllegalMessage/batchdetail/" + row.Id
          // if (row.Status === "解析中") {
          //   return (
          //     <Fragment>
          //       <p>文件解析中</p>
          //     </Fragment>
          //   )
          // } else {
            return (
              <Fragment>
                <Link to={url} target="_blank">查看</Link>
              </Fragment>

            )
          // }
        },
      },
      {
        title: '全量导出',
        render: (val, row) => {
          if (row.Status === "解析中") {
            return (
              <Fragment>
                <Button type="download">{
                  '解析中'
                } </Button>
              </Fragment>
            )
          } else {
            return (
              <Fragment>
                <Button type="download" onClick={() => this.exportFile(row)}><Icon type="download" />{
                  this.state.isDowning[row.Id] ? '下载中' : '下载'
                } </Button>
              </Fragment>
            )
          }
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'messageTable'}>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    );
  }
  exportFile(row) {
    // 设置初始状态，并防止重复点击
    if (row.isDowning) return;
    initStatus.call(this, row);

    // 获取生成xlsx需要的数据
    return fetchData(row.Id)
      // 生成下载xlsx文档
      .then(generateXlsxFromRecord)
      // 错误通知
      .catch(errNotify)
      // 回复初始状态
      .then(() => recoverStartus.call(this, row));


    function errNotify(err) {
      notification['error']({
        message: '导出失败',
        description: err.message || '内部错误'
      })
    }

    function initStatus(row) {
      // row.isDowning = true;
      this.setState(() => ({
        isDowning: {
          [row.Id]: true
        }
      }))
      notification.open({
        message: '导出中',
        description: `如果数据量较大，导出时间可能会稍长`,
        duration: 2
      });
    }

    function recoverStartus(row) {
      this.setState(() => ({
        isDowning: {
          [row.Id]: false
        }
      }))
      // delete row.isDowning;
    }

    function fetchData(id) {
      return request('ExportIllegalRecordList', {
        "BatchId": id,
      }).then(
        resp => resp.RetCode === 0 ? resp.RecordList : Promise.reject(resp)
      )
    }


    async function generateXlsxFromRecord(recordList) {
      if (recordList.length === 0) {
        return notification['error']({
          message: '导出失败',
          description: '数量为0，无法导出'
        })
      }

      var data = recordList.reduce((prev, item) => {
        prev.push([
          item.CompanyId || '',
          item.CompanyName || '',
          item.BU || '',
          item.VipLevel || '',
          item.Manager || '',
          item.IP,
          item.Detail,
          item.Url,
          item.Remark,
        ]);
        return prev;
      }, [["客户ID", "客户名称", "事业部", "客户级别", "客户经理", "IP", "敏感信息", "网址","备注"]]);
      const XLSX = await import('xlsx');
      var filename = row.Description.replace(/\.[^.]+$/, '.xlsx');
      var ws_name = "sheet1";
      var wb = XLSX.utils.book_new(), ws = XLSX.utils.aoa_to_sheet(data);

      // 设定宽度
      ws['!cols'] = [10, 20, 10, 10, 25, 20, 10, 60].map(item => ({ width: item }))
      /* add worksheet to workbook */
      XLSX.utils.book_append_sheet(wb, ws, ws_name);

      /* write workbook */
      XLSX.writeFile(wb, filename);
    }
  }
}

export default messageTable;
