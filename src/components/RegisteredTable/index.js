import React, { PureComponent } from 'react';
import { notification, Table, But<PERSON>, Popconfirm } from 'antd';
import request from '../../utils/request'
import moment from 'moment';
const resourceMap = {
  1:'工信部',
  2:'敖盾',
  3:'ucloud',
  4:'今日在我司已备案的',
  5:'其它代理商'
}
class BatchTable extends PureComponent {
  constructor(props){
    super(props);
    this.state = {
      loading: false,
      iconLoading: false,
      visible: false,
      selectedRowKeys: [],
      selectedIds: [],
    };
  }

  showModal = (ids) => {
    request('UpdateRegisteredDomain', { Ids:ids })
      .then(result => {

        if (result.RetCode !== 0) {
          // 报错
          notification['error']({
            message: '发送请求失败',
            description: result.Message || '内部错误'
          })
          return
        }
        this.props.deleteRows(ids)
        this.onSelectedRowKeysChange([],[])
        return
      }).catch(e =>{
        notification['error']({
          message: '发送请求失败',
          description: e.Message || '网络错误'
        })
      })
  }
  batchIgnoreList = (ids)=> {
    request('DelRegisteredDomainList', { Ids:ids })
    .then(result => {
      if (result.RetCode !== 0) {
        // 报错
        notification['error']({
          message: '处理失败',
          description: result.Message || '内部错误'
        })
        return
      }
      this.onSelectedRowKeysChange([],[])
      this.props.fetch();
    }).catch(e =>{
      notification['error']({
        message: '发送请求失败',
        description: e.Message || '网络错误'
      })
    })
  }
  onSelectedRowKeysChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedIds: selectedRows.map(item=>item.id) });
  }
  handleTableChange = (pagination, filters, sorter) => {
    this.props.onChange(pagination, filters, sorter);
  }
  render() {
    const { data, pagination, loading } = this.props;
    const { selectedRowKeys } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectedRowKeysChange
    };

    const hasSelected = selectedRowKeys.length > 0;

    const columns = [
      /*  {
        title: "ID",
        dataIndex: "id",
      },  */ 
      {
        title: 'IP',
        dataIndex: 'ip',
      }, 
      {
        title: '域名',
        dataIndex: 'domain',
      }, 
      {
        title: '解封',
        render: (val, row) => {
          return (
            <div> 
              <Popconfirm placement="topLeft" title={"确认你已经在其他系统解封该域名"} onConfirm={() => this.showModal([row.id])} okText="确认" cancelText="取消">
                <Button>解封</Button>
              </Popconfirm>
            </div>);
        }
      }, 
      {
        title: '来源',
        dataIndex: 'resource',
        render:(value)=> value ? resourceMap[value] : '未知'
      },
      {
        title: '备注',
        dataIndex: 'remark',
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        render:(value)=>value?moment(value*1000).format('YYYY-MM-DD HH:mm:ss'):''
      }, 
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'BatchTable'}>
        <div style={{ marginBottom: 16 }}>
        <Popconfirm placement="top" title={"请再次确认你的勾选是否正确"} onConfirm={() => this.showModal(this.state.selectedIds)} okText="确认" cancelText="取消">        
          <Button
            type="primary"
            disabled={!hasSelected}
            loading={loading}
          >
            批量解封
          </Button>
          </Popconfirm>       
          <Popconfirm placement="top" title={"请再次确认你的勾选是否正确"} onConfirm={() => this.batchIgnoreList(this.state.selectedIds)} okText="确认" cancelText="取消">        
          <Button
            type="primary"
            disabled={!hasSelected}
            loading={loading}
            style={{ marginLeft: 16 }}
          >
            批量忽略
          </Button>
          </Popconfirm>         
          <span style={{ marginLeft: 8 }}>
            {hasSelected ? `你已经选中 ${selectedRowKeys.length} 个` : ''}
          </span>
          </div>
        <Table
          loading={loading}
          rowKey={record => record.id}
          dataSource={data}
          rowSelection={rowSelection}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    );
  }
}

export default BatchTable;