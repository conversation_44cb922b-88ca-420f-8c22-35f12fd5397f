import React, { PureComponent, Fragment } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { Table, Tooltip, notification, Icon } from 'antd';
import { DataSearchApi } from '../../utils/request'

class DataSearchComponent extends PureComponent {
  constructor(props) {
    super(props)
    this.props = props
    this.state = {
      data:[],
      isDowning: {}
    };
  }

  handleTableChange = (pagination, filters, sorter) =>{
    this.props.onChange(pagination, filters, sorter);
  }

  terminate(options = {}) {
    let self = this
    //重发消息
    let action = options.Action
    delete options.Action
    self.setState({ loading: true })

    DataSearchApi(action, options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '正在结束中' : resp.Message || resp.RetCode + "发送失败"
        notification.open({
          message: message,
        });
        self.setState({
          loading: false
        })
      })
      .then(
        this.props.fetchInfo
      )
      .catch(err => {
        // 报错
        notification['error']({
          message: '结束任务失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  render() {
    let  { data, pagination, loading } = this.props;
    const columns1 = [
      {
        title: '公司ID',
        dataIndex: 'CompanyId',
      },
      {
        title: '公司名称',
        dataIndex: 'CompanyName',
        render: (val, row) => {
          return (
            <span>{row.CompanyName || row.UserName}</span>
          )
        },
      },
      {
        title: '所属渠道',
        dataIndex: 'Channel',
      },
      {
        title: '认证类型',
        dataIndex: 'AuthType',
      },
      {
        title: '违规类型',
        dataIndex: 'Tags',
        render: (val) => {
         if(val&&val.length>1){
          return ( <Fragment>
                <Tooltip title={val.join(';')} >
                  <span>{val[0] + '..'}</span>
                </Tooltip>
              </Fragment>)
          }else{
            return <Fragment><span>{val || ""}</span></Fragment>
          }
        },
      },
      {
        title: '登录查询/域名访问查询/充值记录查询',
        render: (val, row) => {
            return  ( <Fragment>
              {row.AccessInfoStatus === 0 ? <Icon type="clock-circle" style={{'paddingRight': '5px'}}/>: <Icon type="check-circle" style={{ 'paddingRight': '5px', color: '#32CD32' }}/>}
              {row.DomainInfoStatus === 0 ? <Icon type="clock-circle" style={{'paddingRight': '5px'}}/>: <Icon type="check-circle" style={{ 'paddingRight': '5px', color: '#32CD32' }}/>}
              {row.InpourDetailStatus === 0 ? <Icon type="clock-circle" />: <Icon type="check-circle" style={{ color: '#32CD32' }}/>}
          </Fragment>)
        }
      },
      {
        title: '更新时间',
        dataIndex: 'UpdateAtHG',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '操作',
        render: (val, row) => {
          let urlAddress = "/DataSearch/DataSeachDetail?CompanyId=" + row.CompanyId
          return (
            <Fragment>
              {
                <Link to={urlAddress} target="_blank">更多</Link>
              }
            </Fragment>
          )
        },
      }
    ];
    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };
    console.log("paginationProps",paginationProps)
    return (
      <div className={'copyTable'}>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns1}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    );
  }
}

export default DataSearchComponent;
