import React from 'react';
import { Form, Select,Row,Col, Input, Button,Popconfirm } from 'antd';

class FormWrapper extends React.Component {
    handleSubmit = e => {
        e.preventDefault();
        this.props.form.validateFields((err, values) => {
            if (!err) {
                console.log(values)
                this.props.AddGlobalWhiteWord(values)
            }
        });
    };

    render() {
        const { getFieldDecorator } = this.props.form;
        return (
            <Form labelCol={{ span: 5 }} wrapperCol={{ span: 12 }} onSubmit={this.handleSubmit}>
                <Form.Item label="敏感词" >
                    {getFieldDecorator('Words', {
                        rules: [{ required: true, message: '请输入敏感词！' }],
                    })(
                        <Select mode="tags" />
                    )}
                </Form.Item>
                <Form.Item label="备注">
                    {getFieldDecorator('Remark', {
                        rules: [{ required: false }],
                    })(<Input />)}
                </Form.Item>
                <Form.Item wrapperCol={{ span: 12, offset: 5 }}>
                    <Row>
                        <Col span={10}>
                            <Popconfirm placement="top" title={'确定加白吗？'} onConfirm={this.handleSubmit} okText="Yes" cancelText="No">
                                <Button type="primary" >确定</Button>
                            </Popconfirm>
                        </Col>
                        <Col span={6}>
                            <Button onClick={this.props.cancel}>取消</Button>                   
                        </Col>
                    </Row>
                </Form.Item>
            </Form>
        );
    }
}

const WrappedApp = Form.create({ name: 'coordinated' })(FormWrapper);

export default WrappedApp