import React, { PureComponent, Fragment } from 'react';
import { Link } from 'react-router-dom';
import { Table, Modal } from 'antd';
const STATUS = [{
  text: "待发送",
  value: 0
}, {
  text: "发送中",
  value: 1
}, {
  text: "发送成功",
  value: 2
}, {
  text: "发送失败",
  value: 3
}, {
  text: "禁止发送",
  value: 6
}, {
  text: "超时未响应",
  value: 7
}]
class BatchTable extends PureComponent {
  state = {
    loading: false,
    iconLoading: false,
    visible: false,
  };
  handleTableChange = (pagination, filters, sorter) => {
    console.log(pagination, filters, sorter)
    this.props.onChange(pagination, filters, sorter);
  }
  showModal = (BatchInfo) => {
    this.setState({
      visible: true,
      blockInfo: BatchInfo,
    });
  }
  handleOk = (e) => {
    console.log(e);
    this.setState({
      visible: false,
    });
  }
  handleCancel = (e) => {
    console.log(e);
    this.setState({
      visible: false,
    });
  }
  render() {
    const { data, pagination, loading } = this.props;
    const blockInfoColumns = [{
      title: 'IP',
      dataIndex: 'IP',
    }, {
      title: '域名',
      dataIndex: 'Domain',
    }];
    const columns = [
      {
        title: '组织Id',
        dataIndex: 'OrgId',
      },
      {
        title: '公司名',
        dataIndex: 'CompanyName',
      },
      {
        title: '客户等级',
        dataIndex: 'VipLevel',
      }, {
        title: '客户经理',
        dataIndex: 'Manager',
      },{
        title: '所属BU',
        dataIndex: 'BU',
      }, {
        title: '发送状态',
        dataIndex: 'NotifyStatus',
        filters: STATUS,
        filtered: true,
        render: (val) => {
          let obj = STATUS.filter((item) => {
            return item.value === val
          })
          return obj[0]?obj[0].text :""
        }
      },
      {
        title: '客户渠道',
        dataIndex: 'Channel'
      },
      {
        title: '通知详情',
        render: (val, row) => {
          let urlAddress = "/message/NotifyInfodetail/" + row.OrgId + "|" + this.props.batchId
          return (
            <Fragment>
              { 
                !row.NotifyInfo || row.NotifyInfo.length === 0 ? '处理'
                : <Link to={urlAddress}>处理</Link>
              }
            </Fragment>
          )
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'BatchTable'}>
        <Modal
          title="封禁域名列表"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          footer={null}
        >
          <Table dataSource={this.state.blockInfo} columns={blockInfoColumns} />
        </Modal>
        <Table
          loading={loading}
          rowKey={record => record.BatchId}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    );
  }
}

export default BatchTable;