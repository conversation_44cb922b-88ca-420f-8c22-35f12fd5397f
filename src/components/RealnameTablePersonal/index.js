import React, { PureComponent, Fragment } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Table } from 'antd'
class RealnameTablePersonal extends PureComponent {
	handleTableChange(pagination, filters, sorter) {
		this.props.onChange(pagination, filters, sorter)
	}

	render() {
		const { data, pagination, loading, channelList={} } = this.props

		const columns = [
			{
				title: '公司Id',
				dataIndex: 'CompanyId',
			},
			{
				title: '用户名',
				dataIndex: 'UserName',
				render: (val, row) => row.UserName || '暂无',
			},
			{
				title: '认证类型',
				dataIndex: 'AuthType',
				render: (val, row) => <span>{row.AuthType || '暂无'}</span>,
			},
			{
				title: '创建时间',
				dataIndex: 'Created',
				render: (val) => <span>{ moment((val * 1000)).format('YYYY-MM-DD HH:mm:ss')}</span>
			},
			{
				title: '客户经理',
				dataIndex: 'Manager',
				render: (val) => {
					return val === '' ? '-' : val
				}
			},
			{
				title: '认证状态',
				dataIndex: 'AuthStatus',
			},
			{
				title: '渠道',
				dataIndex: 'ChannelID',
				render: (val) => channelList[val]
			},
			{
				title: '证件号码',
				dataIndex: 'IdentityNo',
				render: (val, row) => row.IdentityNo  || "暂无",
			},
			{
				title: '操作',
				render: (val, row) => {
					let url = '/realnamePersonal/detail/' + row.CompanyId
					let action = row.AuthStatus.indexOf("已认证") > -1 ? '详情' : '审核';
					let path = {
						pathname: url,
						state: {
							...row,
							form: this.props.formValues,
							pagination: this.props.pagination,
							channel: channelList[row.ChannelId]
						}
					}
					return (
						<Fragment>
							<Link to={{...path}} target='_blank'>{action}</Link>
						</Fragment>
					)
				},
			},
		]

		const paginationProps = {
			showSizeChanger: true,
			showQuickJumper: true,
			...pagination,
		}

		return (
			<div className={'realnameTable'}>
				<Table
					loading={loading}
					rowKey={record => { return (record.CompanyId + record.AuthType) }}
					dataSource={data}
					columns={columns}
					pagination={paginationProps}
					onChange={this.handleTableChange.bind(this)}
				/>
			</div>
		)
	}
}

export default RealnameTablePersonal
