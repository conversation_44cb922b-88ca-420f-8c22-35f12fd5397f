import React, { PureComponent } from 'react'
import moment from 'moment'
import { Table, Button, Popconfirm } from 'antd'


class IPBlockTable extends PureComponent {
  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter)
  }



  render() {
    const { data, pagination, loading,updateInfo} = this.props
    // console.log(data)
    const columns = [
      {
        title: '公司Id',
        dataIndex: 'CompanyId',
      },
      {
        title: 'IP',
        dataIndex: 'Ip',
      },
      {
        title: '渠道',
        dataIndex: 'Channel',
      },  {
        title: '违禁类型',
        dataIndex: 'ViolationType',
      },  {
        title: '公司名',
        dataIndex: 'CompanyName',
      },{
        title: '状态',
        dataIndex: 'Status',
      },
      {
        title: '操作时间',
        dataIndex: 'Created',
        render: val => <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      // {
      //   title: '客户经理',
      //   dataIndex: 'Manager',
      //   render: (val) => {
      //     return val === '' ? '-' : val
      //   }
      // },
     {
        title: '备注',
        dataIndex: 'Remark',
      },
      {
        title: '操作',
        render: (val, row) => {
          if (row.Status === "已封禁") {
            return (
              <span>
                <Popconfirm title="是否解封？" 
                  onConfirm={() => 
                    updateInfo({ 
                      Action: "UpdateBlockIPInfo", 
                      IP: row.Ip, 
                      Channel:row.Channel,
                    Status:0 },
                    1)
                  }
                >
                  <Button >
                  解封
                  </Button>
                </Popconfirm>
              </span>
            )
          } else {
            return (
              <span>
                <Popconfirm title="是否再次封禁"  
                  onConfirm={() =>  updateInfo({ 
                    Action: "UpdateBlockIPInfo", 
                    IP: row.Ip, 
                    Channel:row.Channel,
                  Status:1 },
                    1)
                  }
                >
                  <Button >
                  封禁
                  </Button>
                  {
                    // canDisabledIp?<Button style={{marginLeft:5}}>通知客户封存Ip</Button>:'' 
                  }
                </Popconfirm>
              </span>
            )
          }
        },
      },
    ]

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    }

    return (
      <div className={'IPBlockTable'}>
        <Table
          loading={loading}
          // rowKey={record => { return (record.CompanyId + record.AuthType) }}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    )
  }
}

export default IPBlockTable
