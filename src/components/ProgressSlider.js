import React from 'react'

import { Card, Icon, Slider } from 'antd';

class ProgressSlider extends React.Component {

    render () {
        const { companyProgress, registerProgress, showRegisterProgress = true } = this.props
        const register_preColor = registerProgress >= 50 ? '' : '#1890ff';
        const register_nextColor = registerProgress >= 50 ? '#1890ff' : '';
        const company_preColor = companyProgress >= 50 ? '' : '#1890ff';
        const company_nextColor = companyProgress >= 50 ? '#1890ff' : '';
        const marks = {
            0:'0%',
            50:'50%',
            100:'100%'
        }
        return (
            <Card title="解析进度" style={{ marginBottom: 24 }} bordered={false} >
                <h4>公司信息解析进度</h4><br/>
                <div className="icon-wrapper">
                    <Icon style={{ color: company_preColor }} type="frown-o" />
                    <Slider min={0} max={100} 
                        disabled={true} 
                        marks={marks} 
                        step={0.01}
                        value={companyProgress} 
                        tooltipVisible={true}
                        tipFormatter={(value)=>`${value}%`}
                    /><br/>
                    <Icon style={{ color: company_nextColor }} type="smile-o" />
                </div>
                {
                    showRegisterProgress? <div><h4>备案信息解析进度</h4><br/></div> : null
                }
                {
                    showRegisterProgress ? 
                    <div className="icon-wrapper">
                        <Icon style={{ color: register_preColor }} type="frown-o" />
                        <Slider min={0} max={100} 
                            disabled={true} 
                            marks={marks} 
                            step={0.01}
                            value={registerProgress} 
                            tooltipVisible={true}
                            tipFormatter={(value)=>`${value}%`}
                        />
                        <Icon style={{ color: register_nextColor }} type="smile-o" />
                    </div>
                    : null
                }
            </Card>
        )
    }

}
export default ProgressSlider