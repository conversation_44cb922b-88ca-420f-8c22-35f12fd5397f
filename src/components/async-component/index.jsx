import React, { lazy, Suspense } from 'react';

const asyncComponent = importComponent => {
  const Component = lazy(importComponent);
  const WrappedComponent = props => (
    <Suspense
      fallback={
        <div style={{ minHeight: '79vh' }}>
          loading...
        </div>
      }
    >
      <Component {...props} />
    </Suspense>
  );

  WrappedComponent.displayName = `AsyncComponent(${Component.displayName || 'Component'})`;

  return WrappedComponent;
};

export default asyncComponent;
