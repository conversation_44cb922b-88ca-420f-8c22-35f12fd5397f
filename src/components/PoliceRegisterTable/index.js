import React, { PureComponent, Fragment } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { Table } from 'antd';
class messageTable extends PureComponent {
  constructor(props) {
    super(props)
    this.props = props
    this.state = { isDowning: {} };
  }
  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter);
  }

  render() {
    const { data, pagination, loading } = this.props;

    const columns = [
      {
        title: '批次Id',
        dataIndex: 'Id',
      }, {
        title: '批次备注',
        dataIndex: 'Description',
      },
      {
        title: '创建时间',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        defaultSortOrder: 'ascend',
      },
      {
        title: '类型',
        dataIndex: 'TypeCH',
      },
      {
        title: '批次状态',
        dataIndex: 'StatusCH',
      },
      {
        title: '批次详情',
        render: (val, row) => {

          let url = "/PoliceRegister/BatchDetail/" + row.Id + '/' + row.Type
          return (
            <Fragment>
              <Link to={url} target="_blank">查看</Link>
            </Fragment>

          )
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'messageTable'}>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    );
  }
}

export default messageTable;
