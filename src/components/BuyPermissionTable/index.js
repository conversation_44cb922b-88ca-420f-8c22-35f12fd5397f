import React, { PureComponent, Fragment } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Table, Button, Popconfirm } from 'antd'
// import request from '../../utils/request'


class BuyPermissionTable extends PureComponent {
  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter)
  }

  // update(options = {}) {
  //   let self = this
  //   //重发消息
  //   let action = options.Action
  //   delete options.Action
  //   self.setState({ loading: true })

  //   request(action, options)
  //     .then(resp => {
  //       let message = resp.RetCode === 0 ? '设置中' : resp.Message || resp.RetCode + "设置失败"

  //       notification.open({
  //         message: '发送中',
  //         description: message,
  //       });

  //       self.setState({
  //         loading: false
  //       })
  //     })
  //     .then(
  //       this.props.fetchInfo
  //     )
  //     .catch(err => {
  //       // 报错
  //       notification['error']({
  //         message: '设置失败',
  //         description: err.message || '内部错误'
  //       })
  //       return;
  //     })
  // }

  render() {
    const { data, pagination, loading,updateInfo} = this.props
    // console.log(data)
    const columns = [
      {
        title: '公司Id',
        dataIndex: 'CompanyId',
      },
      {
        title: '事业部',
        dataIndex: 'BU',
      },
      {
        title: '公司名/用户名',
        dataIndex: 'CompanyName',
      },
      {
        title: '创建时间',
        dataIndex: 'Created',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '客户经理',
        dataIndex: 'Manager',
        render: (val) => {
          return val === '' ? '-' : val
        }
      },
      {
        title: '认证状态',
				render: (val, row) => {
					let url = '/realname/detail/' + row.CompanyId
					let path = {
						pathname: url,
						state: row,
					}
					return (
						<Fragment>
							<Link to={path}>{row.AuditState}</Link>
						</Fragment>
					)
				},      },
      {
        title: '购买权限',
        dataIndex: 'BuyPermission',
      },
      {
        title: '操作',
        render: (val, row) => {
          if (row.BuyPermission === "有购买权限") {
            return (
              <span>
                <Popconfirm title="是否关闭全部机房购买权限？" 
                  onConfirm={() =>  {
                      if(!loading) {
                        updateInfo({ 
                          Action: "UpdateBuyPermissionBit", 
                          CompanyId: row.CompanyId, 
                          ChannelId:row.ChannelId?row.ChannelId+'':'1',
                          CanBuy: 'false',
                          UserEmail:row.UserEmail,
                          Manager:row.Manager,
                          UpdateType:1 },
                        1)
                      }
                    }
                  }
                >
                  <Button >
                    关闭购买权限
                  </Button>
                </Popconfirm>
              </span>
            )
          } else {
            return (
              <span>
                <Popconfirm title="是否打开全部机房购买权限？"  
                  onConfirm={() => {
                    if (!loading) {
                      updateInfo({ 
                        Action: "UpdateBuyPermissionBit", 
                        CompanyId: row.CompanyId, 
                        ChannelId:row.ChannelId?row.ChannelId+'':'1',
                        CanBuy: 'true',
                        UserEmail:row.UserEmail,
                        Manager:row.Manager },
                        1)
                    }
                  }
                  }
                >
                  <Button>
                    打开购买权限
                  </Button>
                  {
                    // canDisabledIp?<Button style={{marginLeft:5}}>通知客户封存Ip</Button>:'' 
                  }
                </Popconfirm>
              </span>
            )
          }
        },
      },
    ]

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    }

    return (
      <div className={'BuyPermissionTable'}>
        <Table
          loading={loading}
          // rowKey={record => { return (record.CompanyId + record.AuthType) }}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    )
  }
}

export default BuyPermissionTable
