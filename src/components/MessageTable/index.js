import React, { PureComponent, Fragment } from 'react';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { Table, notification, Button, Icon } from 'antd';
// import XLSX from 'xlsx';
import request from '../../utils/request'

class messageTable extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { isDowning: {} };
  }
  // state = {}
  handleTableChange = (pagination, filters, sorter) => {
    this.props.onChange(pagination, filters, sorter);
  }

  render() {
    const { data, pagination, loading } = this.props;

    const columns = [
      {
        title: '批次Id',
        key:'1',
        dataIndex: 'Id',
      }, {
        title: '批次备注',
        key:'2',
        dataIndex: 'Description',
      },
      {
        title: '创建时间',
        key:'3',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
      },
      // {
      //   title: '批次备注',
      //   dataIndex: 'Description',
      // },
      {
        title: '批次状态',
        key:'4',
        dataIndex: 'Status',
      },
      {
        title: '批次详情',
        key:'5',
        render: (val, row) => {
          let url = "/message/batchdetail/" + row.Id
          // if (row.Status === "解析中") {
          //   return (
          //     <Fragment>
          //       <p>文件解析中</p>
          //     </Fragment>
          //   )
          // } else {
            return (
              <Fragment>
                <Link to={url} target="_blank">查看</Link>
              </Fragment>

            )
          // }


        },
      },
      {
        title: '全量导出',
        key:'6',
        render: (val, row) => {
          if (row.Status === "解析中") {
            return (
              <Fragment>
                <Button type="download">{
                  '解析中'
                } </Button>
              </Fragment>
            )
          } else {
            return (
              <Fragment>
                <Button type="download" onClick={() => this.exportFile(row)}><Icon type="download" />{
                  this.state.isDowning[row.Id] ? '下载中' : '下载'
                } </Button>
              </Fragment>
            )
          }
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'messageTable'}>
        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    );
  }
  exportFile(row) {
    // 设置初始状态，并防止重复点击
    if (this.state.isDowning[row.Id]) return;
    initStatus.call(this, row);

    // 获取生成xlsx需要的数据
    return fetchData(row.Id)
      // 生成下载xlsx文档
      .then(generateXlsxFromRecord)
      // 错误通知
      .catch(errNotify)
      // 回复初始状态
      .then(() => recoverStartus.call(this, row));


    function errNotify(err) {
      notification['error']({
        message: '导出失败',
        description: err.message || '内部错误'
      })
    }

    function initStatus(row) {
      // row.isDowning = true;
      this.setState(() => ({
        isDowning: {
          [row.Id]: true
        }
      }))
      notification.open({
        message: '导出中',
        description: `如果数据量较大，导出时间可能会稍长`,
        duration: 2
      });
    }

    function recoverStartus(row) {
      this.setState(() => ({
        isDowning: {
          [row.Id]: false
        }
      }))
    }

    function fetchData(id) {
      return request('ExportRecordListFile', {
        "BatchId": id,
      }).then(
        resp => resp.RetCode === 0 ? resp.RecordList : Promise.reject(resp)
      )
    }


    async function generateXlsxFromRecord(recordList) {
      if (recordList.length === 0) {
        return notification['error']({
          message: '导出失败',
          description: '数量为0，无法导出'
        })
      }

      // TODU:需要集中放置
      const BEIAN_DICT = {
        0: '未查询',
        // 未备案不显示
        1 : '已备案',
        // 2: '未备案',
      }
      var data = recordList.reduce((prev, item) => {
        prev.push([
          item.IP,
          item.Domain,
          item.CompanyId || '',
          item.CompanyName || '',
          item.BU || '',
          item.VipLevel || '',
          item.Manager || '',
          item.MainEmail || '',
          item.MainPhone? `   ${item.MainPhone}   ` :'',
          BEIAN_DICT[item.RegisterStatus] || "",
          item.Remark,
        ]);
        return prev;
      }, [["IP", "域名", "客户ID", "客户名称", "事业部", "客户级别", "客户经理", "主账号邮箱", "主账号手机号", "备案情况（未备案不显示）","备注"]]);
      const XLSX = await import('xlsx');
      var filename = row.Description.replace(/\.[^.]+$/, '.xlsx');
      var ws_name = "sheet1";
      var wb = XLSX.utils.book_new(), ws = XLSX.utils.aoa_to_sheet(data);

      // 设定宽度
      ws['!cols'] = [15, 20, 10, 20, 25, 5, 25].map(item => ({ width: item }))
      /* add worksheet to workbook */
      XLSX.utils.book_append_sheet(wb, ws, ws_name);

      /* write workbook */
      XLSX.writeFile(wb, filename);
    }
  }
}

export default messageTable;
