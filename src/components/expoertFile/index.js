import { notification} from 'antd';
async function generateXlsxFromRecord(dataList,culumnWidthArray,fileName) {
    try {
      const XLSX = await import('xlsx');
      let ws_name = "sheet1";
      let wb = XLSX.utils.book_new(),
      ws = XLSX.utils.aoa_to_sheet(dataList);
      // 设定宽度
      ws['!cols'] = culumnWidthArray.map(item => ({ width: item }))
      /* add worksheet to workbook */
      XLSX.utils.book_append_sheet(wb, ws, ws_name);
      /* write workbook */
      XLSX.writeFile(wb, fileName);
    }catch(e){
        console.log(e)
    }
  }
function exportFile(dataList,culumnWidthArray,fileName) {
	  // 生成下载xlsx文档
    return generateXlsxFromRecord(dataList,culumnWidthArray,fileName)
      // 回复初始状态
      .then(initStatus())
       // 错误通知
      .catch(errNotify)
    function errNotify(err) {
      notification['error']({
        message: '导出失败',
        description: err.message || '内部错误'
      })
    }

    function initStatus() {
      notification.open({
        message: '导出中',
        description: `如果数据量较大，导出时间可能会稍长`,
        duration: 2
      });
    }
}
export default exportFile
