import React, { PureComponent } from 'react';
import moment from 'moment'
import { Table, Button, notification, Modal, Divider } from 'antd';
import request from '../../utils/request'

class BatchTable extends PureComponent {

  constructor() {
    super()

    this.state = {
      loading: false,
      iconLoading: false,
      visible: false,

      // 域名的list列表
      domains: [],
      // 控制域名的弹出框是否显示
      domainShow: false,

      // 短信内容和短信预览
      smsInfo: {
      },
      smsShow: false,

      // 邮件内容和邮件预览
      emailInfo: {
      },
      needRetry: false,
      emailShow: false,
    };
  }
  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter);
  }

  closeAllModel() {
    this.setState({
      domainShow: false,
      smsShow: false,
      emailShow: false,
    })
  }

  async showDomain(CompanyName) {
    let options = {
      CompanyName,
      BatchId: this.props.batchId,
      // 目前是取到所有的值
      Limit:1000,
    }

    this.closeAllModel()

    try {
      let data = await request('GetPoliceRegisterDomainListById', options)

      if (data.RetCode !== 0) {
        throw Error(data.Message)
      }

      this.setState({
        domains: data.List,
        domainShow: true,
      })
    } catch (e) {

      notification['error']({
        message: e.message || '网络错误',
        description: '获取列表失败'
      })
    }

  }

  async showSmsContent(recordId) {
    let options = {
      recordId,
      subCategory: 'sms',
      type: this.props.Type,
    }

    this.closeAllModel()

    //如果命中缓存缓存
    if (
      options.recordId === this.state.smsInfo.RecordId &&
      options.type === this.state.smsInfo.Type
    ) {
      this.setState({
        smsShow: true,
      })
      return
    }

    try {
      let data = await request('GetPoliceRegisterContent', options)

      if (data.RetCode !== 0) throw Error(data.Message)

      this.setState({
        smsShow: true,
        smsInfo: {
          RecordId: recordId,
          Type: options.Type,
          Content: data.Content,
          Status: data.Status,
          Contact: data.Contact,
          Time: data.Time === 0 ? '未更新' : moment(data.Time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        },
        // 3代表发送失败，4代表接受失败
        needRetry: [3,4].includes(data.Status),
      });
    } catch (e) {

      notification['error']({
        message: e.message || '网络错误',
        description: '获取短信内容失败'
      })
    }

  }

  async showEmailContent(recordId){
    let options = {
      recordId,
      subCategory: 'email',
      type: this.props.Type,
    }

    this.closeAllModel()

    //如果命中缓存缓存
    if (
      options.recordId === this.state.emailInfo.RecordId &&
      options.type === this.state.emailInfo.Type
    ) {
      this.setState({
        emailShow: true,
      })
      return
    }

    try {
      let data = await request('GetPoliceRegisterContent', options)

      if (data.RetCode !== 0) throw Error(data.Message)

      this.setState({
        emailShow: true,
        emailInfo: {
          RecordId: recordId,
          Type: options.Type,
          Content: data.Content,
          Status: data.Status,
          Contact: data.Contact,
          Time: data.Time === 0 ? '未更新' : moment(data.Time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        },
        // 3代表发送失败，4代表接受失败
        needRetry: [3,4].includes(data.Status),
      });
    } catch (e) {

      notification['error']({
        message: e.message || '网络错误',
        description: '获取邮件内容失败'
      })
    }
  }

  async retryNotifySms() {
    let options = {
      recordId: this.state.smsInfo.RecordId,
      type:'sms',
    }

    try{
      let data = await request('SendPoliceRegisterRecord', options)
      if(data.RetCode !==0 ) throw Error(data.Message)
      
      let smsInfo = this.state.smsInfo
      this.setState({
        ...smsInfo,
        needRetry: false,
      })
      notification['open']({
        message: '正在发送中',
        description: '手动刷新页面状态会更新'
      })
    }catch(e){

      notification['error']({
        message: e.message || '网络错误',
        description: '发送失败'
      })
    }
  }

  async retryNotifyEmail() {
    let options = {
      recordId: this.state.emailInfo.RecordId,
      type:'email',
    }

    try{
      let data = await request('SendPoliceRegisterRecord', options)
      if(data.RetCode !==0 ) throw Error(data.Message)
      
      let emailInfo = this.state.emailInfo
      this.setState({
        ...emailInfo,
        needRetry: false,
      })


      notification['open']({
        message: '正在发送中',
        description: '手动刷新页面状态会更新'
      })
    }catch(e){
      notification['error']({
        message: e.message || '网络错误',
        description: '发送失败'
      })
    }
  }

  render() {
    const { data, pagination, loading } = this.props;

    const DomainColumns = [{
      title: '域名',
      dataIndex: 'Domain',
    }];

    const columns = [
      {
        title: '公司名',
        dataIndex: 'CompanyName',
      },
      {
        title: '域名',
        render: (val, row) => {
          return (
            <div>
              <Button onClick={() => this.showDomain(row.CompanyName)}>查看</Button>
            </div>
          );
        }
      },
      {
        title: '省份',
        dataIndex: 'Province',
      },
      {
        title: '短信预览',
        render: (val, row) => {
          return (
            <div>
              <Button onClick={() => this.showSmsContent(row.Id)}>预览</Button>
            </div>
          );
        }
      },
      {
        title: '邮件预览',
        render: (val, row) => {
          return (
            <div>
              <Button onClick={() => this.showEmailContent(row.Id)}>预览</Button>
            </div>
          );
        }
      },
      {
        title: '短信发送状态',
        dataIndex: 'SmsStatus',
        filters: [{
          text: '发送中',
          value: '发送中',
        }, {
          text: '发送失败',
          value: '发送失败',
        }, {
          text: '发送成功',
          value: '发送成功',
        }, {
          text: '待发送',
          value: '待发送',
        }/* , {
          text: '禁止发送',
          value: '禁止发送',
        } */],
      },
      {
        title: '邮件发送状态',
        dataIndex: 'EmailStatus',
        filters: [{
          text: '发送中',
          value: '发送中',
        }, {
          text: '发送失败',
          value: '发送失败',
        }, {
          text: '发送成功',
          value: '发送成功',
        }, {
          text: '待发送',
          value: '待发送',
        }/* , {
          text: '禁止发送',
          value: '禁止发送',
        } */],
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'BatchTable'}>
        <Modal
          title="域名列表"
          visible={this.state.domainShow}
          onOk={this.closeAllModel.bind(this)}
          onCancel={this.closeAllModel.bind(this)}
          footer={null}
        >
          {<Table
            dataSource={this.state.domains}
            columns={DomainColumns}
            rowKey={record => record.Id}
          />}
        </Modal>
        <Modal
          title="短信预览"
          visible={this.state.smsShow}
          onCancel={this.closeAllModel.bind(this)}
          onOk={this.closeAllModel.bind(this)}
          footer={null}
        >
          <div className="markdown" dangerouslySetInnerHTML={{ __html: this.state.smsInfo.Content }}>
          </div>

          <Divider />
          <p>
            手机号: {this.state.smsInfo.Contact} <br />
            发送时间: {this.state.smsInfo.Time} <br />
          </p>
          {this.state.needRetry ?
            <Button type="primary" onClick={this.retryNotifySms.bind(this)}>手动重发</Button> : ''
          }
        </Modal>



        <Modal
          title="邮件预览"
          visible={this.state.emailShow}
          width={800}
          onCancel={this.closeAllModel.bind(this)}
          onOk={this.closeAllModel.bind(this)}
          footer={null}
        >
          {/* <p> */}
            {/* 引入获取的邮件信息做为html展示 */}
            <div className="markdown" dangerouslySetInnerHTML={{ __html: this.state.emailInfo.Content }}></div>
          {/* </p> */}

          <Divider />
          <p>
            邮箱地址: {this.state.emailInfo.Contact} <br />
            发送时间: {this.state.emailInfo.Time} <br />
          </p>
          {this.state.needRetry ?
            <Button type="primary" onClick={this.retryNotifyEmail.bind(this)}>手动重发</Button> : ''
          }
        </Modal>

        <Table
          loading={loading}
          rowKey={record => record.Id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    );
  }
}

export default BatchTable;