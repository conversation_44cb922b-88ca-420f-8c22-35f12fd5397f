import React, { Component } from 'react';
import StaticField from './StaticField';
import { Row, Col } from 'antd';
import _ from 'lodash'
const uuid = require('uuid-v4');

class StaticFieldBoard extends Component {
	render() {
		let item = []
		let count = 0;
		let childrens = []
		let uid = uuid()

		_.each(this.props.data, (value, index) => {
			if (!childrens[parseInt(count / 3, 10)]) {
				childrens[parseInt(count / 3, 10)] = []
			}
			
			let key = uid + index
			
			childrens[parseInt(count / 3, 10)].push(
				<Col key={key} span={8}>
					<StaticField label={index} text={value} />
				</Col>
			)
			
			count++
		}) 

		let rowCount = parseInt(count / 3, 10) + 1

		for (var i = 0; i < rowCount; i++) {
			let key = uid + i.toString()

			item.push(
				<Row key={key} style={{marginBottom: 10}}>
					{childrens[i]}
				</Row>
			)
		}
		
		return (
			<div>{item}</div>
		)
	}
}

export default StaticFieldBoard;
