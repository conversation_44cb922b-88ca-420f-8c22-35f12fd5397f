import React from "react"
import { <PERSON>,<PERSON><PERSON>,<PERSON>confirm,Modal,Input,notification } from "antd"
import {Link} from 'react-router-dom'
import moment from 'moment'
import request from '../../utils/request'

 const NeedUpdateBuyPermissionTable = ({data=[],loading=false,pagination,fetchInfo,updateInfo}) => {
    let ipVal = '';
    const showModal = (record) => {
        Modal.confirm({
            title:'请输入Ip',
            content:(<Input onChange={(e)=>ipVal=e.target.value}/>),
            okText:'确认',
            cancelText:'取消',
            onOk:()=>{
                request('UpdateDisabledIpNotify',{CompanyId:record.CompanyId,Ip:ipVal})
                .then(res=>{
                    if(res.RetCode===0){
                        fetchInfo()
                        // rs()
                    }
                }).catch(err=>{
                    notification['error']({
                        message: '发送失败',
                        description: err.message || '内部错误'
                    })
                })
            }
        })
    }
    const columns = [
        {
            title: '公司Id',
            fixed:'left',
            dataIndex: 'CompanyId',
        },
        {
            title: '事业部',
            dataIndex: 'BU',
        },
        {
            title: '公司名/用户名',
            dataIndex: 'CompanyName',
        },
        {
            title: '客户经理',
            dataIndex: 'Manager',
            render: (val) => {
                return val === '' ? '-' : val
            }
        },
        {
            title: '认证状态',
            render: (val, row) => {
                let url = '/realname/detail/' + row.CompanyId
                let path = {
                    pathname: url,
                    state: row,
                }
                return (
                    <Link to={path}>{row.AuditState}</Link>
                )
            },      
        },
        {
            title: '当前权限',
            dataIndex: 'BuyPermission',
        },
        {
            title:'最近一次操作',
            dataIndex:'lastOperate'
        },
        {
            title:'最近一次操作时间',
            dataIndex: 'lastOperateTime',
            render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        },
        {
            title: '最近一次操作人',
            dataIndex: 'lastOperator',
        },
        {
            title: '最近一次操作结果',
            dataIndex: 'lastRemark',
            width:150,
            render:(val)=><div>{
                val && val.length<40?val:<span title={val}>{val.substr(0,40)+'...'}</span>
            }</div>
        },
        {
            title: '创建时间',
            dataIndex: 'Created',
            render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        },
        {
            title: '操作',
            fixed:'right',
            render: (val, row) => {
              if (row.BuyPermission === "有购买权限") {
                return (
                    <span>
                        <Popconfirm title="是否关闭全部机房购买权限？" 
                            onConfirm={() => 
                                updateInfo({ Action: "UpdateBuyPermissionBit", 
                                    CompanyId: row.CompanyId, 
                                    CanBuy: 'false',
                                    ChannelId:row.ChannelId?row.ChannelId+'':'1',
                                    UserEmail:row.UserEmail,
                                    Manager:row.Manager,
                                    UpdateType:0 
                                },0)
                            }
                        >
                            <Button>关闭购买权限</Button>
                        </Popconfirm>
                    </span>
                )
            } else {
                return (
                    <span>
                        <Button style={{marginLeft:5}} onClick={showModal.bind(this,row)}>通知客户封存Ip</Button>
                    </span>
                )
            }
        },
    }]
    return (
        <Table
            loading={loading}
            scroll={{x:1600}}
            // rowKey={record => { return (record.CompanyId + record.AuthType) }}
            dataSource={data}
            columns={columns}
            pagination={pagination}
            onChange={(pagination)=>{
                const {current,pageSize}=pagination;
                fetchInfo({Offset:(current-1)*pageSize,Limit:pageSize})
            }}
        />
    )
}
export default NeedUpdateBuyPermissionTable