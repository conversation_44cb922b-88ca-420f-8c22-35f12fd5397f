/*
 * @Author: qiming.xie <EMAIL>
 * @Date: 2025-05-28 17:31:47
 * @LastEditors: qiming.xie <EMAIL>
 * @LastEditTime: 2025-05-28 18:04:28
 * @FilePath: /hegui-frontend/src/components/RealnameReexamTable/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { PureComponent, Fragment } from "react";
import moment from "moment";
import { Link } from "react-router-dom";
import { Table, Tag, Icon } from "antd";
class RealnameTableAPP extends PureComponent {
  handleTableChange(pagination, filters, sorter) {
    this.props.onChange(pagination, filters, sorter);
  }
  render() {
    const {
      data,
      pagination,
      loading,
      isFilter,
      enToCn,
      channelList = {},
    } = this.props;

    const columns = [
      {
        title: "公司Id",
        dataIndex: "CompanyId",
        ...(isFilter && { width: "200px" }),
        render: (val, row) => {
          //创建超过7天没有复审
          const now = moment().unix();
          const flag =
            isFilter &&
            moment.unix(now).diff(moment.unix(row.CreateTime), "days") > 7;
          return (
            <>
              <span style={{ display: "inline-block", minWidth: "60px" }}>
                {val}
              </span>
              {flag && (
                <Tag color="volcano" style={{ marginLeft: "10px" }}>
                  超7日未处理
                </Tag>
              )}
            </>
          );
        },
      },
      {
        title: "公司名称",
        dataIndex: "CompanyName",
        render: (val) => val || "暂无",
      },
      {
        title: "渠道",
        dataIndex: "ChannelId",
        render: (channel) => {
          return channelList[channel];
        },
      },
      {
        title: "认证状态",
        dataIndex: "FlowStatus",
        render: (val) => {
          let isFinished = [
            "流程已完成",
            "流程已完成待复核",
            "判断为异常",
          ].includes(enToCn[val]);
          return (
            <span>
              <span style={{ marginRight: "6px" }}>
                {" "}
                {enToCn[val] || "暂无"}
              </span>
              {isFinished ? (
                <Icon
                  type="check-circle"
                  theme="twoTone"
                  twoToneColor="#52c41a"
                />
              ) : (
                ""
              )}
            </span>
          );
        },
      },
      {
        title: "认证方式",
        dataIndex: "CompanyKYCType",
        render: (val) => {
          const type = {
            CompanyRemit: "对公打款",
            UserLegalPersonLive: "法人活体",
            UserRecharge: "用户充值",
          };
          return type[val] || "暂无";
        },
      },
      {
        title: "创建时间",
        dataIndex: "CreateTime",
        sorter: true,
        // sorter: (a, b) => b.CreateTime - a.CreateTime,
        render: (val) => (
          <span>
            {val ? moment(val * 1000).format("YYYY-MM-DD HH:mm:ss") : "暂无"}
          </span>
        ),
      },
      {
        title: "操作",
        dataIndex: "Id",
        render: (val, row) => {
          let url = "/RealNameReexamDetail/" + row.FlowId + "&" + row.Id;
          let action =
            row?.FlowStatus?.indexOf("LicenseAuditPending") > -1
              ? "审核"
              : "详情";
          let path = {
            pathname: url,
            state: row,
          };
          return (
            <Fragment>
              <Link to={{ ...path }} target="_blank">
                {action}
              </Link>
            </Fragment>
          );
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={"RealnameTableAPP"}>
        <Table
          loading={loading}
          rowKey="Id"
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange.bind(this)}
        />
      </div>
    );
  }
}

export default RealnameTableAPP;
