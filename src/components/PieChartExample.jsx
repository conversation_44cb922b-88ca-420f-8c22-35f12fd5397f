import React from 'react';
import {
    <PERSON>,
    <PERSON>eo<PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    Coord
} from "bizcharts";
import { Card, Row, Col } from 'antd';

// 示例数据
const pieData = [
    { type: '攻击', value: 13395, percentage: 52.4 },
    { type: '其他', value: 11678, percentage: 45.68 },
    { type: '垃圾邮件', value: 419, percentage: 1.64 },
    { type: '侵权', value: 66, percentage: 0.26 },
    { type: '滥用', value: 5, percentage: 0.02 },
    { type: '数据库信息暴露', value: 1, percentage: 0.00 }
];

const PieChartExample = () => {
    return (
        <div style={{ padding: '20px' }}>
            <Row gutter={16}>
                {/* 基础饼图 */}
                <Col span={8}>
                    <Card title="基础饼图">
                        <Chart
                            data={pieData}
                            height={300}
                            forceFit
                            padding={[20, 20, 20, 20]}
                        >
                            <Coord type="theta" radius={0.8} />
                            <Axis name="percentage" />
                            <Legend position="bottom" />
                            <Tooltip
                                showTitle={false}
                                itemTpl='<li><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}封 ({percentage}%)</li>'
                            />
                            <Geom
                                type="intervalStack"
                                position="percentage"
                                color={['type', ['#f5222d', '#8c8c8c', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                tooltip={[
                                    'type*value*percentage',
                                    (type, value, percentage) => ({
                                        name: type,
                                        value: value,
                                        percentage: percentage.toFixed(1)
                                    })
                                ]}
                                style={{
                                    lineWidth: 1,
                                    stroke: '#fff',
                                }}
                            />
                        </Chart>
                    </Card>
                </Col>

                {/* 环形饼图 */}
                <Col span={8}>
                    <Card title="环形饼图">
                        <Chart
                            data={pieData}
                            height={300}
                            forceFit
                            padding={[20, 20, 20, 20]}
                        >
                            <Coord type="theta" radius={0.8} innerRadius={0.4} />
                            <Axis name="percentage" />
                            <Legend position="right" />
                            <Tooltip
                                showTitle={false}
                                itemTpl='<li><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}封 ({percentage}%)</li>'
                            />
                            <Geom
                                type="intervalStack"
                                position="percentage"
                                color={['type', ['#f5222d', '#8c8c8c', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                tooltip={[
                                    'type*value*percentage',
                                    (type, value, percentage) => ({
                                        name: type,
                                        value: value,
                                        percentage: percentage.toFixed(1)
                                    })
                                ]}
                                style={{
                                    lineWidth: 2,
                                    stroke: '#fff',
                                }}
                            />
                        </Chart>
                    </Card>
                </Col>

                {/* 带标签的饼图 */}
                <Col span={8}>
                    <Card title="带标签的饼图">
                        <Chart
                            data={pieData}
                            height={300}
                            forceFit
                            padding={[20, 20, 20, 20]}
                        >
                            <Coord type="theta" radius={0.8} />
                            <Axis name="percentage" />
                            <Legend position="bottom" />
                            <Tooltip
                                showTitle={false}
                                itemTpl='<li><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}封 ({percentage}%)</li>'
                            />
                            <Geom
                                type="intervalStack"
                                position="percentage"
                                color={['type', ['#f5222d', '#8c8c8c', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                tooltip={[
                                    'type*value*percentage',
                                    (type, value, percentage) => ({
                                        name: type,
                                        value: value,
                                        percentage: percentage.toFixed(1)
                                    })
                                ]}
                                style={{
                                    lineWidth: 1,
                                    stroke: '#fff',
                                }}
                                label={[
                                    'percentage',
                                    {
                                        formatter: (val, item) => {
                                            return item.point.type + ': ' + val + '%';
                                        },
                                        offset: 20,
                                        textStyle: {
                                            fontSize: 12,
                                            fill: '#666'
                                        }
                                    }
                                ]}
                            />
                        </Chart>
                    </Card>
                </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 20 }}>
                {/* 玫瑰图 */}
                <Col span={12}>
                    <Card title="玫瑰图（南丁格尔图）">
                        <Chart
                            data={pieData}
                            height={300}
                            forceFit
                            padding={[20, 20, 20, 20]}
                        >
                            <Coord type="polar" />
                            <Axis name="type" />
                            <Axis name="value" />
                            <Legend position="right" />
                            <Tooltip />
                            <Geom
                                type="interval"
                                position="type*value"
                                color={['type', ['#f5222d', '#8c8c8c', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                tooltip={[
                                    'type*value*percentage',
                                    (type, value, percentage) => ({
                                        name: type,
                                        value: value + '封',
                                        percentage: percentage.toFixed(1) + '%'
                                    })
                                ]}
                                style={{
                                    lineWidth: 1,
                                    stroke: '#fff',
                                }}
                            />
                        </Chart>
                    </Card>
                </Col>

                {/* 半圆饼图 */}
                <Col span={12}>
                    <Card title="半圆饼图">
                        <Chart
                            data={pieData}
                            height={300}
                            forceFit
                            padding={[20, 20, 20, 20]}
                        >
                            <Coord type="theta" radius={0.8} startAngle={-Math.PI} endAngle={0} />
                            <Axis name="percentage" />
                            <Legend position="bottom" />
                            <Tooltip
                                showTitle={false}
                                itemTpl='<li><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}封 ({percentage}%)</li>'
                            />
                            <Geom
                                type="intervalStack"
                                position="percentage"
                                color={['type', ['#f5222d', '#8c8c8c', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                tooltip={[
                                    'type*value*percentage',
                                    (type, value, percentage) => ({
                                        name: type,
                                        value: value,
                                        percentage: percentage.toFixed(1)
                                    })
                                ]}
                                style={{
                                    lineWidth: 1,
                                    stroke: '#fff',
                                }}
                            />
                        </Chart>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default PieChartExample;
