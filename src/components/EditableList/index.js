import React from 'react'
import {But<PERSON>} from 'antd'
const EditableList = (props)=>{
  let { renderItem, dataSource=[], addition, itemDeletion } = props
  return (
    dataSource.map((val,index)=>{
      return (
        <div key={val.id} style={{display:'flex',alignItems:'start'}}>
          {renderItem(val)}
          <Button onClick={()=>{
            itemDeletion.onDelete(val)
          }} disabled={itemDeletion.getDisabledOfItem(val)} >-</Button>
          {
            index===dataSource.length-1 ?
            <Button onClick={addition.onAdd} >+</Button>:''
          }
        </div>
      )
    })
  )
}

export default EditableList
