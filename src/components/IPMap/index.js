import React, { useRef, useEffect, useState } from "react";
import {
  Menu,
  Dropdown,
  Card,
  Divider,
  DatePicker,
  Icon,
  notification,
} from "antd";
import moment from "moment";
import request from "../../utils/request";
import * as echarts from "echarts";
import "echarts/extension/bmap/bmap";
import "./index.less";
import worldCityGeo from "./worldCityGeo";
let mapInstance = null;
let bmap = null;
export default function IPMap() {
  const [openRisk, setIsOpenRisk] = useState(true);
  const [riskType, setRiskType] = useState(0);
  const [date, setDate] = useState(moment());
  const [menuVisiable, setMenuVisiable] = useState(false);
  const [data, setData] = useState([]);
  const [baseSize, setBaseSize] = useState(12);
  const popWrap = useRef(null);
  const riskTypeMap = {
    0: "RegisterRecord",
    1: "InterceptRegisterRecord",
    2: "LoginRecord",
  };
  const riskTextMap = {
    0: "注册记录",
    1: "拦截注册记录",
    2: "登录记录",
  };
  const riskArea = ["马来西亚", "缅甸", "老挝", "菲律宾", "柬埔寨"];
  const ref = useRef(null);

  const filterTopData = (data) => {
    let topList = data.slice(0, 5);
    //风险地区
    let riskAreaList = topList.filter((el) => riskArea.includes(el.name));
    let areaList = topList.filter((el) => !riskArea.includes(el.name));
    return [riskAreaList, areaList];
  };

  const convertData = function (data) {
    let res = [];
    for (let i = 0; i < data.length; i++) {
      let geoCoord = worldCityGeo[data[i].name];
      if (geoCoord) {
        res.push({
          name: data[i].name,
          value: geoCoord.concat(data[i].value),
        });
      }
    }
    return res;
  };
  let arr = [1, 12, 100, 1000]; //标志点分段
  let option = {
    tooltip: {
      trigger: "item",
      
    },
    bmap: {
      center: [104.114129, 37.550339],
      zoom: 5,
      roam: "move",
      enableScrollWheelZoom: false,
      mapStyle: {
        styleJson: [
          {
            featureType: "water",
            elementType: "all",
            stylers: {
              color: "#d1d1d1",
            },
          },
          {
            featureType: "land",
            elementType: "all",
            stylers: {
              color: "#f3f3f3",
            },
          },

          {
            featureType: "highway",
            elementType: "all",
            stylers: {
              color: "#fdfdfd",
            },
          },
          {
            featureType: "highway",
            elementType: "labels",
            stylers: {
              visibility: "off",
            },
          },
          {
            featureType: "arterial",
            elementType: "geometry",
            stylers: {
              color: "#fefefe",
            },
          },
          {
            featureType: "arterial",
            elementType: "geometry.fill",
            stylers: {
              color: "#fefefe",
            },
          },
          {
            featureType: "poi",
            elementType: "all",
            stylers: {
              visibility: "off",
            },
          },
          {
            featureType: "green",
            elementType: "all",
            stylers: {
              visibility: "off",
            },
          },
          {
            featureType: "subway",
            elementType: "all",
            stylers: {
              visibility: "off",
            },
          },
          {
            featureType: "manmade",
            elementType: "all",
            stylers: {
              color: "#d1d1d1",
            },
          },
          {
            featureType: "local",
            elementType: "all",
            stylers: {
              color: "#d1d1d1",
            },
          },
          {
            featureType: "arterial",
            elementType: "labels",
            stylers: {
              visibility: "off",
            },
          },
          {
            featureType: "boundary",
            elementType: "all",
            stylers: {
              color: "#fefefe",
            },
          },
          {
            featureType: "building",
            elementType: "all",
            stylers: {
              color: "#d1d1d1",
            },
          },
          {
            featureType: "label",
            elementType: "labels.text.fill",
            stylers: {
              color: "#999999",
            },
          },
        ],
      },
    },
    series: [
      {
        // 非风险地区 非TOP5
        type: "scatter",
        coordinateSystem: "bmap",
        data: convertData(data),
        symbolSize: function (val) {
          if (val[2] < arr[1]) return baseSize + val[2] / 4;
          else if (val[2] < arr[2]) return baseSize + arr[1] / 4 + val[2] / 5;
          return val[2] / 50 + (baseSize + arr[1] / 4) + arr[2] / 5;
        },
        encode: {
          value: 2,
        },
        label: {
          formatter: "{b}",
          position: "right",
          show: false,
        },
        emphasis: {
          label: {
            show: true,
          },
        },
      },
      {
        // 风险地区 非TOP5
        name: "风险地区",
        type: "scatter",
        coordinateSystem: "bmap",
        data: convertData(
          data.filter((el) => openRisk && riskArea.includes(el.name))
        ),
        symbolSize: function (val) {
          if (val[2] < arr[1]) return baseSize + val[2] / 4;
          else if (val[2] < arr[2]) return baseSize + arr[1] / 4 + val[2] / 5;
          return val[2] / 50 + (baseSize + arr[1] / 4) + arr[2] / 5;
        },
        encode: {
          value: 2,
        },
        label: {
          formatter: "{b}",
          position: "right",
          show: false,
        },
        emphasis: {
          label: {
            show: true,
          },
        },
        itemStyle: {
          color: "#ffa23d",
        },
      },
      {
        name: "Top 5",
        type: "effectScatter",
        coordinateSystem: "bmap",
        data: convertData(filterTopData(data)[1]),
        symbolSize: function (val) {
          if (val[2] < arr[1]) return baseSize + val[2] / 4;
          else if (val[2] < arr[2]) return baseSize + arr[1] / 4 + val[2] / 5;
          return val[2] / 50 + (baseSize + arr[1] / 4) + arr[2] / 5;
        },
        encode: {
          value: 2,
        },
        showEffectOn: "render",
        rippleEffect: {
          brushType: "stroke",
        },
        label: {
          formatter: "{b}",
          position: "right",
          show: true,
        },
        itemStyle: {
          color: "#94db7c",
          shadowBlur: 10,
          shadowColor: "#333",
        },
        emphasis: {
          scale: true,
        },
        zlevel: 1,
      },
      {
        name: "Top 5", //风险地区Top5
        type: "effectScatter",
        coordinateSystem: "bmap",
        data: convertData(filterTopData(data)[0]),
        symbolSize: function (val) {
          if (val[2] < arr[1]) return baseSize + val[2] / 4;
          else if (val[2] < arr[2]) return baseSize + arr[1] / 4 + val[2] / 5;
          return val[2] / 50 + (baseSize + arr[1] / 4) + arr[2] / 5;
        },
        encode: {
          value: 2,
        },
        showEffectOn: "render",
        rippleEffect: {
          brushType: "stroke",
        },
        label: {
          formatter: "{b}",
          position: "right",
          show: true,
        },
        itemStyle: {
          color: openRisk ? "#f79238" : "#97ed85",
          shadowBlur: 10,
          shadowColor: "#333",
        },
        emphasis: {
          scale: true,
        },
        zlevel: 1,
      },
    ],
  };

  function disabledDate(current) {
    if(riskType==='2'){
      return current && current >= moment().startOf('day');
    }

    return current && current > moment().endOf('day');
  }    
  const renderChart = () => {
    // 基于准备好的dom，初始化echarts实例
    if (!mapInstance) {
      mapInstance = echarts.init(ref.current);
    }
    bmap = mapInstance?.getModel()?.getComponent("bmap").getBMap(); //获取控件
    if (bmap) {
      bmap.addControl(new window.BMap.NavigationControl()); // 添加平移缩放控件
      bmap.enableContinuousZoom(); //启用地图惯性拖拽，默认禁用
      bmap.addControl(new window.BMap.OverviewMapControl());
    }
    // 添加地图点击事件监听
  mapInstance.on('click', function (params) {
    // 阻止冒泡
    params.event.event.stopPropagation();
    return 
  });
    mapInstance.setOption(option);
  };
  const onChange = (date) => {
    setDate(date);
  };
  const onVisibleChange = (visible) => {
    setMenuVisiable(visible);
  };
  const menu = (
    <Menu
      onClick={(key) => {
        setRiskType(key.key);
        let isToday = date.format('YYYY-MM-DD')===moment().format('YYYY-MM-DD')
        if(key.key==='2'&&isToday){
          let yesterday = moment().subtract(1, 'days')
          setDate(yesterday)
        }
        setMenuVisiable(false);
      }}
    >
      <Menu.Item key="0">注册记录</Menu.Item>
      <Menu.Item key="1">拦截注册记录</Menu.Item>
      <Menu.Item key="2">登录记录</Menu.Item>
    </Menu>
  );
  const toggle = () => {
    setIsOpenRisk(!openRisk);
  };
  const getRiskInfo = () => {
    const params = {
      Date: moment(date).format("YYYY-MM-DD"),
      RiskType: riskTypeMap[riskType],
    };
    request("GetRiskTypeInfoByDate", params)
      .then((resp) => {
        if (resp.RetCode !== 0) {
          notification["error"]({
            message: "获取数据失败",
            description: resp.message || "内部错误",
          });
        }
        let resData = [];
        Object.entries(resp.DomesticData.City).forEach((el) =>
          resData.push({ name: el[0], value: el[1] })
        );
        Object.entries(resp.ForeignData).forEach((el) =>
          resData.push({ name: el[0], value: el[1] })
        );
        resData.sort(function (a, b) {
          return b.value - a.value;
        });
        //中位数
        let len = resData.length;
        if (len > 0) {
          //众数
          let mp = new Map();
          resData.forEach((el) => {
            if (mp.has(el)) {
              let val = mp.get(el);
              mp.set(el, val + 1);
            } else {
              mp.set(el, 1);
            }
          });
          let mostNum = 0;
          for (let [, value] of mp) {
            if (value > mostNum) mostNum = value;
          }
          if (mostNum < 12) mostNum = 12;
          else mostNum = mostNum * 4;
          setBaseSize(mostNum);
        }

        setData(resData);
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "获取数据失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  useEffect(() => {
    getRiskInfo();
  }, [openRisk, riskType, date]);
  useEffect(() => {
    renderChart();
  }, [data, baseSize]);
  useEffect(() => {
    return () => {
        if (mapInstance) {
            mapInstance.dispose(); // 清理实例
            mapInstance = null;
        }
    };
}, []);
  return (
    <Card
      title="控制台注册/登录IP属地分布图"
      style={{ marginBottom: "20px" }}
      onClickCapture={(e) => {
        //点击到canvas，停止冒泡
        if (e.target.tagName === "CANVAS") e.stopPropagation();
      }}
    >
      <div className="cardContent">
        <div className="mapWrap" ref={ref}></div>
        <div className="tooltip" ref={popWrap}>
          <DatePicker
            onChange={onChange}
            value={date}
            className="datePicker"
            disabledDate={disabledDate}
          />
          <Divider
            type="vertical"
            style={{ height: "2em", margin: "0 10px" }}
          />
          <Dropdown
            overlay={menu}
            trigger={["click"]}
            onVisibleChange={onVisibleChange}
            getPopupContainer={() => popWrap.current}
          >
            <span
              style={{
                display: "inline-flex",
                width: "115px",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              {riskTextMap[riskType]}
              <Icon
                type={menuVisiable ? "up" : "down"}
                style={{ float: "right" }}
              />
            </span>
          </Dropdown>
          <Divider
            type="vertical"
            style={{ height: "2em", margin: "0 10px" }}
          />
          <span
            style={{ color: openRisk && "#1890ff", cursor: "pointer" }}
            onClick={toggle}
          >
            <Icon type="alert" style={{ marginRight: "10px" }} />
            风险地区
          </span>
          {/* <Divider
            type="vertical"
            style={{ height: "2em", margin: "0 10px" }}
          />
          <button onClick={() => setBaseSize(baseSize + 1)}>+</button>
          {baseSize}
          <button onClick={() => setBaseSize(baseSize - 1)}>-</button> */}
        </div>
      </div>
    </Card>
  );
}
