import React, { PureComponent, Fragment } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Table } from 'antd'

class CrossReportAuditTable extends PureComponent {
	handleTableChange = (pagination, filters, sorter)=> {
		this.props.onChange(pagination, filters, sorter)
	}
	render() {
		const { data, pagination, loading,ApplyStatusEnum={}} = this.props
		const columns = [
			{
				title: '公司ID',
				dataIndex: 'CompanyId',
			},
			{
				title: '企业名称',
				dataIndex: 'CompanyName',
			},
			{
				title: '经办人',
				dataIndex: 'ManagerName',
			},
			{
				title: '创建时间',
				dataIndex: 'CreateTime',
				render: (val) => <span>{ moment((val * 1000)).format('YYYY-MM-DD HH:mm:ss')}</span>
			},
			{
				title: '客户经理',
				dataIndex: 'Manager',
			},
			{
				title: '审核状态',
				dataIndex: 'Status',
				render:(val)=>ApplyStatusEnum[val]
			},
			{
				title: '操作',
				render: (val, row) => {
					let url = '/CrossReportAuditDetails/'+ row.CompanyId
					let path = {
						pathname: url,
						state: {
							...row,
						}
					}
					return (
						<Fragment>
							<Link to={{...path}} target='_blank' onClick={()=>{
							  localStorage.setItem(row.CompanyId,JSON.stringify(path.state))
							}}>{row.Status === 1 ? '审核':'详情'}</Link>
						</Fragment>
					)
				},
			},
		]

		const paginationProps = {
			showSizeChanger: true,
			showQuickJumper: true,
			...pagination,
		}

		return (
			<div className={'realnameTable'}>
				<Table
					loading={loading}
					rowKey={record => { return (record.CompanyId + Math.random()*1000) }}
					dataSource={data}
					columns={columns}
					pagination={paginationProps}
					onChange={this.handleTableChange.bind(this)}
				/>
			</div>
		)
	}
}

export default CrossReportAuditTable
