import React, { PureComponent } from 'react';
import { <PERSON>confirm, Button, notification } from 'antd';
import { Table } from 'antd';
import request from '../../utils/request'
import moment from 'moment';

class NotifyTable extends PureComponent {
  state = {};

  notify(options = {}) {
    let self = this
    //重发消息
    let action = options.Action
    delete options.Action
    self.setState({ loading: true })

    request(action, options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '正在发送信息中' : resp.Message || resp.RetCode + "发送失败"
        notification.open({
          message: message,
        });
        self.setState({
          loading: false
        })
      })
      .then(
        this.props.fetchInfo
      )
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  handleTableChange = (pagination, filters, sorter) => {
    this.props.onChange(pagination, filters, sorter);
  }

  render() {
    const { data, pagination, loading } = this.props;

    const columns = [
      // {
      //   title: '通知人Id',
      //   dataIndex: 'id',
      // },
      {
        title: '手机号',
        dataIndex: 'Mobile',
      },
      {
        title: '邮箱地址',
        dataIndex: 'Email',
      }, {
        title: '手机通知状态',
        dataIndex: 'SmsStatus',
      }, {
        title: '邮件通知状态',
        dataIndex: 'EmailStatus',
      }, {
        title: '更新时间',
        render: (val, row) => {
          return moment(row.UpdateTime* 1000).format('YYYY-MM-DD HH:mm')
        }
        },
      {
        title: '短信操作',
        render: (val, row) => {

          return (
            <span>{
              !(row.SmsStatus === "发送失败" || row.SmsStatus === "接收失败")?<Button disabled={!(row.SmsStatus === "发送失败" || row.SmsStatus === "接收失败")}>重发短信</Button>
              :<Popconfirm title="是否确认重发短信？" loading={this.state.loading} onConfirm={() => this.notify({ Action: this.props.Action ? this.props.Action('sms') : "SendSMS", NotifyId: row.Id })}>
                <Button>
                  重发短信
                </Button>
              </Popconfirm>
              }
              
            </span>
          )

        },
      }, {
        title: '邮件操作',
        render: (val, row) => {
          return (
            <span>{
              (row.EmailStatus === "发送失败" || row.EmailStatus === "接收失败") ? <Popconfirm title="是否确认重发邮件？" onConfirm={() => this.notify({ Action: this.props.Action ? this.props.Action('email') : "SendEmail", NotifyId: row.Id })}>
              <Button>
                重发邮件</Button></Popconfirm> 
                : <Button disabled={!(row.EmailStatus === "发送失败" || row.EmailStatus === "接收失败")}>重发邮件</Button>
            }</span>
          )
        },
      },
    ];

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    return (
      <div className={'NotifyTable'}>
        <Table
          loading={loading}
          rowKey={record => record.id}
          dataSource={data}
          columns={columns}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    );
  }
}

export default NotifyTable;
