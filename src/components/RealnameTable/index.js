import React, { PureComponent, Fragment } from 'react'
import moment from 'moment'
import { Link } from 'react-router-dom'
import { Table } from 'antd'
class RealnameTable extends PureComponent {
	handleTableChange(pagination, filters, sorter) {
		this.props.onChange(pagination, filters, sorter)
	}

	render() {
		const { data, pagination, loading, channelList={} } = this.props

		const columns = [
			{
				title: '公司Id',
				dataIndex: 'CompanyId',
			},
			{
				title: '公司名/用户名',
				dataIndex: 'CompanyName',
				render: (val, row) => row.AuthType.indexOf('个人认证') > -1 ? row.UserName : row.CompanyName,
			},
			{
				title: '认证类型',
				dataIndex: 'AuthType',
			},
			{
				title: '创建时间',
				dataIndex: 'Created',
				render:  (val, row) => <span>{moment((val|| row.CreatedAt) * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
			},
			{
				title: '客户经理',
				dataIndex: 'Manager',
				render: (val) => {
					return val === '' ? '-' : val
				}
			},
			{
				title: '认证状态',
				dataIndex: 'AuditState',
			},
			{
				title: '渠道',
				dataIndex: 'ChannelId',
				render: (val) => channelList[val]
			},
			{
				title: '操作',
				render: (val, row) => {
					let url = '/realname/detail/' + row.CompanyId
					let action = row.AuditState === '待审核' ? '审核' : '详情'
					let path = {
						pathname: url,
						state: {
							...row,
							form: this.props.formValues,
							pagination: this.props.pagination,
							channel: channelList[row.ChannelId]
						}
					}
					localStorage.setItem(row.CompanyId,JSON.stringify(path.state))
					return (
						<Fragment>
							<Link to={{...path}} target='_blank' onClick={()=>{
								console.log("sessionStorage.setItem(row.CompanyId,JSON.stringify(path.state)",path.state)
				             	//数据容易丢失，写到缓存
                       localStorage.setItem(row.CompanyId,JSON.stringify(path.state))
							}}>{action}</Link>
						</Fragment>
					)
				},
			},
		]

		const paginationProps = {
			showSizeChanger: true,
			showQuickJumper: true,
			...pagination,
		}

		return (
			<div className={'realnameTable'}>
				<Table
					loading={loading}
					rowKey={record => { return (record.CompanyId + record.AuthType) }}
					dataSource={data}
					columns={columns}
					pagination={paginationProps}
					onChange={this.handleTableChange.bind(this)}
				/>
			</div>
		)
	}
}

export default RealnameTable
