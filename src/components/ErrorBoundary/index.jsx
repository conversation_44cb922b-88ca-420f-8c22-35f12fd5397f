import React from 'react';
import { Result } from 'antd';
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Caught by ErrorBoundary:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false });
    // 你可以在这里重新加载页面或触发特定逻辑
  };

  render() {
    if (this.state.hasError) {
      return (
        <Result
          status="error"
          title="页面崩溃了..."
          subTitle="事缓则圆，人缓则安"
        ></Result>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
