import React, { useState, useEffect } from "react";
import { Select } from "antd";
import PropTypes from "prop-types";

const { Option } = Select;

/**
 * 带全选功能的多选Select组件
 * 特点：
 * 1. 自动添加"全部"选项
 * 2. 全选与其他选项互斥（选中全选时，其他选项自动取消；选中其他选项时，全选自动取消）
 * 3. 当所有选项都被选中时，自动选中全选
 */
const SelectAll = ({
  options, // 选项数据，格式：[{ value, label }]
  defaultValue = [], // 默认值
  onChange, // 选择变化时的回调
  placeholder = "请选择", // 占位符
  disabled = false, // 是否禁用
  className = "", // 自定义类名
  style = {}, // 自定义样式
  allOptionLabel = "全部", // 全选选项的标签
  allOptionValue = "all", // 全选选项的值
  maxTagCount = "responsive", // 最多显示多少个标签
  ...rest
}) => {
  // 状态管理
  const [selectedValues, setSelectedValues] = useState(defaultValue);

  // 处理选择变化
  const handleChange = (newValues) => {
    let finalValues = [...newValues];

    // 判断是否包含全选选项
    const hasAll = finalValues.includes(allOptionValue);

    // 逻辑处理：
    // 1. 如果选择了全选，自动选中所有其他选项
    // 2. 如果选择了部分选项，取消全选
    if (hasAll) {
      // 选中全选时，选中所有选项（除了全选本身）
      finalValues = options.map((option) => option.value);
    } else if (finalValues.length === options.length) {
      // 如果已选中所有选项，自动选中全选
      finalValues.push(allOptionValue);
    }

    // 更新状态并触发回调
    setSelectedValues(finalValues);
    onChange && onChange(finalValues);
  };

  // 初始值处理
  useEffect(() => {
    if (defaultValue && defaultValue.length > 0) {
      setSelectedValues(defaultValue);
    }
  }, [defaultValue]);

  // 渲染选项
  const renderOptions = () => {
    // 全选选项
    const allOption = (
      <Option key={allOptionValue} value={allOptionValue}>
        {allOptionLabel}
      </Option>
    );

    // 普通选项
    const normalOptions = options.map((option) => (
      <Option key={option.value} value={option.value}>
        {option.label}
      </Option>
    ));

    // 组合选项（全选选项放最前面）
    return [allOption, ...normalOptions];
  };

  return (
    <Select
      mode="multiple"
      value={selectedValues}
      onChange={handleChange}
      placeholder={placeholder}
      disabled={disabled}
      className={`select-all-wrapper ${className}`}
      style={style}
      maxTagCount={maxTagCount}
      {...rest}
    >
      {renderOptions()}
    </Select>
  );
};

// 属性类型定义
SelectAll.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.any.isRequired,
      label: PropTypes.node.isRequired,
    })
  ).isRequired,
  defaultValue: PropTypes.array,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  allOptionLabel: PropTypes.node,
  allOptionValue: PropTypes.any,
  maxTagCount: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default SelectAll;
