const MenuList = [
  {
    key: "/dashboard",
    path: "/dashboard",
    iconType: "line-chart",
    title: "控制台看板",
    isTop: true,
  },
  {
    key: "/realname",
    path: "/realname",
    iconType: "idcard",
    title: "实名制审核",
    children: [
      {
        key: "/RealnameReexam",
        path: "/RealnameReexam",
        //iconType: "edit",
        title: "新企业实名",
      },
      {
        key: "/realname",
        path: "/realname",
        //iconType: "edit",
        title: "企业认证",
      },
      {
        key: "/realnamePersonal",
        path: "/realnamePersonal",
        //iconType: "edit",
        title: "个人认证",
      },
      {
        key: "/APPAudit",
        path: "/APPAudit",
        //iconType: "edit",
        title: "活体认证",
      },
      {
        key: "/CrossReportAudit",
        path: "/CrossReportAudit",
        //iconType: "edit",
        title: "跨境报备审核",
      },
      {
        key: "/exclusiveCloudAudit",
        path: "/exclusiveCloudAudit",
        //iconType: "edit",
        title: "专属云实名认证",
      },
      {
        key: "/AuditSet",
        path: "/AuditSet",
        //iconType: "edit",
        title: "审核配置",
      },
    ],
  },
  {
    key: "/message",
    path: "/message",
    title: "合规通知",
    iconType: "bell",
    children: [
      {
        key: "/message",
        path: "/message",
        //iconType: "bell",
        title: "合规通知",
      },
      {
        key: "/RegisteredDomain",
        path: "/RegisteredDomain",
        //iconType: "bell",
        title: "已备案域名",
      },
      {
        key: "/IllegalMessage",
        path: "/IllegalMessage",
        //iconType: "bell",
        title: "违规信息通知",
      },
    ],
  },
  {
    key: "/buypermission",
    path: "/buypermission",
    title: "购买权限设置",
    iconType: "lock",
    children: [
      {
        key: "/buypermission",
        path: "/buypermission",
        //iconType: "unlock",
        title: "用户购买权限设置",
      },
      {
        key: "/buypermission/historylist",
        path: "/buypermission/historylist",
        //iconType: "unlock",
        title: "历史操作",
      },
    ],
  },
  {
    key: "/PoliceRegister/BatchList",
    path: "/PoliceRegister/BatchList",
    title: "公安未备案通知",
    iconType: "message",
    isTop: true,
  },
  {
    key: "/PoliceCase",
    path: "/PoliceCase",
    title: "公安",
    iconType: "lock",
    children: [
      {
        key: "/PoliceCopy/BatchList",
        path: "/PoliceCopy/BatchList",
        title: "公安取证",
        //iconType: "usb",
      },
      {
        key: "/PoliceCase",
        path: "/PoliceCase",
        title: "公安案件",
        //iconType: "usb",
      },
    ],
  },
  {
    key: "/BlockInfo/BlockList",
    path: "/BlockInfo/BlockList",
    title: "合规封禁",
    iconType: "poweroff",
    isTop: true,
  },
  {
    key: "/banAccount",
    path: "/banAccount",
    title: "自动封号",
    iconType: "disconnect",
    children: [
      {
        key: "/banAccount",
        path: "/banAccount",
        title: "自动封号",
        //iconType: "disconnect",
      },
      {
        key: "/banIP",
        path: "/banIP",
        title: "自动封禁IP",
        //iconType: "disconnect",
      },
      {
        key: "/portUnblock",
        path: "/portUnblock",
        title: "端口加白",
        //iconType: "disconnect",
      },
    ],
  },
  {
    key: "/emailNotify",
    path: "/emailNotify",
    title: "邮件通知",
    iconType: "mail",
    children: [
      {
        key: "/emailNotify",
        path: "/emailNotify",
        //iconType: "edit",
        title: "邮件处理",
      },
      {
        key: "/emailNotify/history",
        path: "/emailNotify/history",
        //iconType: "edit",
        title: "历史邮件",
      },
      {
        key: "/emailNotify/ControlPanel",
        path: "/emailNotify/ControlPanel",
        title: "控制面板",
        //iconType: "edit",
      },
    ],
  },
  {
    key: "/DataSearch",
    path: "/DataSearch",
    title: "数据查询",
    iconType: "search",
    children: [
      {
        key: "/DataSearch",
        path: "/DataSearch",
        //iconType: "search",
        title: "数据查询",
      },
      {
        key: "/DataSearch/ChargeInfo",
        path: "/DataSearch/ChargeInfo",
        //iconType: "search",
        title: "充值情况查询",
      },
      {
        key: "/DataSearch/BanResource",
        path: "/DataSearch/BanResource",
        title: "封号资源查询",
      },
      {
        key: "/DataSearch/ScamFraudPhone",
        path: "/DataSearch/ScamFraudPhone",
        title: "手机号注册查询",
      },
    ],
  },
  {
    key: "/SensitiveWords",
    path: "/SensitiveWords",
    title: "广告敏感词审核",
    iconType: "exception",
    children: [
      {
        key: "/SensitiveWords",
        path: "/SensitiveWords",
        //iconType: "exception",
        title: "广告敏感词审核",
      },
      {
        key: "/whiteList",
        path: "/whiteList",
        //iconType: "exception",
        title: "加白列表",
      },
    ],
  },
  {
    key: "/RiskWarning",
    path: "/RiskWarning",
    title: "风险预警",
    iconType: "alert",
    isTop: true,
  },
  {
    key: "/RiskControl",
    path: "/RiskControl",
    title: "风险控制",
    iconType: "control",
    children: [
      {
        key: "/RiskControl",
        path: "/RiskControl",
        //iconType: "control",
        title: "风险控制",
      },
      {
        key: "/RiskControl/GlobalSSH",
        path: "/RiskControl/GlobalSSH",
        title: "Global SSH/RDP拦截",
        //iconType: "control",
      },
      {
        key: "/RiskControl/EmailSuffixBlacklist",
        path: "/RiskControl/EmailSuffixBlacklist",
        title: "渠道注册邮箱后缀黑名单",
        //iconType: "control",
      },
    ],
  },
  {
    key: "/MiningData",
    path: "/MiningData",
    title: "挖矿数据",
    iconType: "compass",
    children: [
      {
        key: "/MiningData",
        path: "/MiningData",
        //iconType: "compass",
        title: "挖矿数据",
      },
      {
        key: "/MiningData/AddressManage",
        path: "/MiningData/AddressManage",
        title: "矿池地址管理",
        //iconType: "compass",
      },
    ],
  },
  {
    key: "/WhiteList",
    path: "/WhiteList/PhoneNumberBindingCount",
    title: "白名单",
    iconType: "safety-certificate", // 设置图标
    children: [
      {
        key: "/WhiteList/PhoneNumberBindingCount",
        path: "/WhiteList/PhoneNumberBindingCount",
        //iconType: "safety-certificate",
        title: "手机号绑定主账号数",
      },
      {
        key: "/WhiteList/PhoneNumberTop",
        path: "/WhiteList/PhoneNumberTop",
        //iconType: "safety-certificate",
        title: "手机号最高白名单",
      },
      {
        key: "/WhiteList/DNSWhiteList",
        path: "/WhiteList/DNSWhiteList",
        //iconType: "safety-certificate",
        title: "53端口白名单",
      },
      {
        key: "/WhiteList/EmailSuffix",
        path: "/WhiteList/EmailSuffix",
        //iconType: "safety-certificate",
        title: "渠道注册邮箱后缀白名单",
      },
      {
        key: "/WhiteList/UniversityAuthEmail",
        path: "/WhiteList/UniversityAuthEmail",
        //iconType: "safety-certificate",
        title: "高校认证邮箱后缀",
      },
      {
        key: "/WhiteList/GlobalSSH",
        path: "/WhiteList/GlobalSSH",
        //iconType: "safety-certificate",
        title: "Global SSH/Rdp 白名单",
      },
    ],
  },
];
export default MenuList;
