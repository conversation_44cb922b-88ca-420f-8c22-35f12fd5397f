import React, { useState } from "react";
import { <PERSON>, with<PERSON>out<PERSON> } from "react-router-dom";
import { Layout, Menu, Icon } from "antd";
import { useEffect } from "react";
import './menu.less'
const { Sider } = Layout;
const SubMenu = Menu.SubMenu;

const renderMenu = (list) => {
  return list.map((item) => {
    return item.children ? (
      <SubMenu
        key={item.path}
        title={
          <span>
            <Icon type={item.iconType} />
            <span>{item.title}</span>
          </span>
        }
      >
        {renderMenu(item.children)}
      </SubMenu>
    ) : (
      <Menu.Item key={item.path}>
        <Link to={item.path}>
          <Icon type={item.iconType} />
          <span style={!item.isTop ? {marginLeft: "-20px"}:{}}>{item.title}</span>
        </Link>
      </Menu.Item>
    );
  });
};

const MenuComponent = (props) => {
  const [pathname, setPathname] = useState(props.location.pathname);
  const [collapsed, setCollapsed] = useState(props.collapsed)
  //根据path获取subMenu的全部key
  const getSubMenus = (pathname) => {
    let subMenu = [];
    props.menuList
      .filter((el) => el.children)
      .forEach((menu) => {
        let index = menu.children.findIndex((child) => child.path === pathname);
        if (index !== -1) {
          subMenu = menu.children.map((el) => el.key);
        }
      });
    return subMenu;
  };
  const [openKeys, setOpenKeys] = useState(getSubMenus(pathname));
  useEffect(() => {
    setPathname(props.location.pathname);
    setOpenKeys(getSubMenus(props.location.pathname));
  }, [props.location.pathname]);
    // 用于监听 props.collapsed 改变 收缩前先关闭所有展开项 打开前先展开当前选中项
  useEffect(()=>{
    if(props.collapsed){
      setOpenKeys([]);
    }else{
      setOpenKeys(getSubMenus(props.location.pathname));
    }
    setTimeout(()=>{
      setCollapsed(props.collapsed)
    })
  },[props.collapsed])
  const onOpenChange = (keys) => {
    keys = keys.slice(-1);
    const rootSubmenuKeys = props.menuList.map((item) => item.key);
    const latestOpenKey = openKeys.find((key) => openKeys.indexOf(key) === -1);
    if (rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
      setOpenKeys(keys);
    } else {
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    }
  };
  const logoStyle = {
    height: 32,
    // background: 'rgba(255, 255, 255, 0.2)',
    textAlign: "center",
    lineHeight: "32px",
    margin: 16,
  };
  const titleStyle = {
    transition: "display .3s",
    color: "#fff",
    animation: "fadeIn",
    animationDuration: ".3s",
  };
  return (
    <Sider collapsible collapsed={collapsed} onCollapse={props.onCollapse} className="sideBar" width={230}>
      <div style={logoStyle}>
        <Icon
          type="appstore"
          style={{ color: "#fff", fontSize: "18px", marginRight: "5px" }}
        />
        <h1
          style={{
            ...titleStyle,
            display: collapsed ? "none" : "inline-block",
          }}
        >
          合规运营平台
        </h1>
      </div>
      <Menu
        theme="dark"
        defaultSelectedKeys={[pathname]}
        selectedKeys={[pathname]}
        mode="inline"
        openKeys={openKeys}
        defaultOpenKeys={openKeys}
        onOpenChange={onOpenChange}
      >
        {renderMenu(props.menuList)}
      </Menu>
    </Sider>
  );
}

export default withRouter(MenuComponent);
