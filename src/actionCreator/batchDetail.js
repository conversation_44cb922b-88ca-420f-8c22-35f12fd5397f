import request from "../utils/request";
import {
	GET_BATCH_LIST,
	GET_ILLEGAL_BATCH_LIST,
	GET_ANALY_PROGRESS,
	GET_ILLEGAL_ANALY_PROGRESS
} from "../action/batchDetail"
import {
	notification
} from "antd"
export function setPagination(type, data) {
	return {
		type,
		data
	}
}

export function setLoading(type, data) {
	return {
		type,
		data
	}
}
export const getBatchList = (options) => (dispatch) => {
	// Loading Modal 
	Promise.all([
		request("GetRecordListV2", options),
		request("GetBatchList", {		
			Id: options.BatchId
		}),
		request("GetAbnormalRecord", {
			BatchId: options.BatchId
		}),
		request("GetNotifyBURecord", {
			BatchId: options.BatchId
		}),
		request("GetRegisteredDomainByRecord", {
			Id: options.BatchId
		})
	])
		.then(datas => {
		// 结果不正确时报错
			for (let data of datas) {
				if (data.RetCode !== 0) {
					throw new Error(data.Message)
				}
			}

			//数据归集操作
			const orgList = []

			//多条记录按组织ID整合，同时标记出有发送未成功的记录

			datas[0].RecordList.forEach((value) => {

				const indexNumber = orgList.findIndex(Org => Org && Org.OrgId === value.OrgId)

				if (indexNumber === -1) {
					const {
						CompanyId,
						CompanyName,
						VipLevel,
						Manager,
						OrgId,
						BU,
						IP,
						Channel,
						NotifyInfo,
						Domain,
						NotifyStatus
					} = value
					const eachvalue = {
						CompanyId,
						CompanyName,
						VipLevel,
						Manager,
						OrgId,
						Channel,
						NotifyInfo,
						BU,
						NotifyStatus,
						BlockInfo: []
					}

					eachvalue.BlockInfo.push({
						IP,
						Domain
					})
					orgList.push(eachvalue)
	
				} else {
				    //有记录则增加
					orgList[indexNumber].BlockInfo.push({
						IP: value.IP,
						Domain: value.Domain
					})
				}
			})

			const sorter = (a, b) => {
				const status = {
					"发送中": 3,
					"发送成功": 4,
					"发送失败": 1,
					"接收失败": 2,
				}
				return status[a.TaskStatus] - status[b.TaskStatus]
			}
			//batchStatus标记批次状态，供判断是否禁用批量发送功能
			dispatch(
				{
					type: GET_BATCH_LIST,
					data: {
						rawList: orgList,
						list: orgList,
						showAnalyProgress: datas[1].BatchList[0].Status === "NEW" ? true : false,
						speedButtonDisabled:datas[1].BatchList[0].Status === "NEW" ? false : true,//加速按钮修改成 GetBatchList这个接口 返回的Status是New的时候 可以使用加速 
						batchStatus: datas[1].BatchList[0].Status,
						abnormalRecord: datas[2].Records,
						BUNotificationInfo: datas[3].Records.sort(sorter),
						registeredDomainRecord: datas[4].Records,
						buIsNotified: datas[3].Records.length !== 0 ? true : false,
						pagination: {
							current: options.Offset/options.Limit +1,
							pageSize: options.Limit,
							total: datas[0].TotalCount
						}
					}
				}
			)
		})
		.catch(err => {
		// 报错
			notification["error"]({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})

			// 清空列表
			dispatch({
				type: GET_BATCH_LIST,
				data: {
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				}
			})
		})
}

export const setBatchList = (type,data) => {
	return {
		type,data
	}
}
export const getAnalyProgress = (options) => (dispatch) => {
	request("GetAnalyProgress", options)
		.then(res=>{
			dispatch({
				type: GET_ANALY_PROGRESS,
				data: {
					companyProgress:res.companyProgress,
					registerProgress:res.registerProgress
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取解析进度失败",
				description: err.message || "内部错误"
			})
		})
}
export const getIllegalBatchList = (options) => (dispatch) => {
	Promise.all([
		request("GetIllegalRecordList", options),
		request("GetIllegalBatchList", { Id: options.BatchId}),
		request("GetAbnormalIllegalRecord", { BatchId: options.BatchId }),
		request("GetNotifyBUIllegalRecord", { BatchId: options.BatchId })
	])
		.then(datas => {
		// 结果不正确时报错
			for (let data of datas) {
				if (data.RetCode !== 0) {
					throw new Error(data.Message)
				}
			}

			//数据归集操作
			const orgList = []

			//多条记录按组织ID整合，同时标记出有发送未成功的记录

			datas[0].RecordList.forEach((value) => {

				const indexNumber = orgList.findIndex(Org => Org && Org.OrgId === value.OrgId)

				if (indexNumber === -1) {
					const {
						CompanyId,
						CompanyName,
						VipLevel,
						Manager,
						OrgId,
						BU,
						Channel,
						NotifyInfo,
						SealInfo
					} = value
					const eachvalue = {
						CompanyId,
						CompanyName,
						VipLevel,
						Manager,
						OrgId,
						BU,
						Channel,
						NotifyInfo,
						BlockInfo: SealInfo
					}

					const checkIsInStatuses = (infos, status) => {
						if (!Array.isArray(status)) status = [status]
						return infos.some(item => status.includes(item.SmsStatus) || status.includes(item.EmailStatus))
					}
					const checkAllIsInStatuses = (infos, status) => {
						if (!Array.isArray(status)) status = [status]
						return infos.every(item => status.includes(item.SmsStatus) && status.includes(item.EmailStatus))
					}
					const checkHasOneEmsAndEmailSendOk = (infos) => {
						return infos.some(item => item.SmsStatus === "Received" && item.EmailStatus === "Received")
					}

					orgList.push(eachvalue)

					/* 
					* 逻辑如下，确定该条的状态
					* 1. 只要有一个在发送中，就是发送中
					* 2. 当不满足上面的条件时，如果有一个人邮件并且短信发送成功，则认为是成功
					* 3. 当不满足上面的条件时，只要有一个失败就是发送失败
					* 4. 如果所有的都是禁止发送的，就是禁止发送
					* 5. 当不满足上面的条件时， 如果所有的都是 未发送或者禁止发送，就是待发送状态
					* 6. 不满足以上所有时，就是发送成功状态
					*/
					switch (true) {
					case checkIsInStatuses(value.NotifyInfo, ["Sending"]):
						eachvalue.Status = "发送中"
						break
					case checkHasOneEmsAndEmailSendOk(value.NotifyInfo):
						eachvalue.Status = "发送成功"
						break
					case checkIsInStatuses(value.NotifyInfo, ["SendFailed", "ReceiveFailed"]):
						eachvalue.Status = "发送失败"
						//需要把失败的数据提前
						orgList.unshift(orgList.pop())
						break
					case checkAllIsInStatuses(value.NotifyInfo, ["Prohibit"]):
						eachvalue.Status = "禁止发送"
						break
					case checkIsInStatuses(value.NotifyInfo, ["Timeout"]):
						eachvalue.Status = "超时未响应"
						//需要把失败的数据提前
						orgList.unshift(orgList.pop())
						break
					case checkAllIsInStatuses(value.NotifyInfo, ["New", "Prohibit"]):
						eachvalue.Status = "待发送"
						break
					default:
						eachvalue.Status = "发送成功"
					}

				} else {
				//有记录则增加
				orgList[indexNumber].BlockInfo = orgList[indexNumber].BlockInfo.contact(value.SealInfo)
				}
			})

			const sorter = (a, b) => {
				const status = {
					"发送中": 3,
					"发送成功": 4,
					"发送失败": 1,
					"接收失败": 2,
				}
				return status[a.TaskStatus] - status[b.TaskStatus]
			}
			dispatch(
				{
					type: GET_ILLEGAL_BATCH_LIST,
					data: {
						rawList: orgList,
						list: orgList,
						batchStatus: datas[1].BatchList[0].Status,
						showAnalyProgress: datas[1].BatchList[0].Status === "NEW" ? true : false,
						abnormalRecord: datas[2].Records,
						BUNotificationInfo: datas[3].Records.sort(sorter),
						buIsNotified: datas[3].Records.length !== 0 ? true : false,
						pagination: {
							current: options.Offset/options.Limit + 1,
							pageSize: options.Limit,
							total: datas[0].TotalCount
						}
					}
				}
			)
		})
		.catch(err => {
		// 报错
			notification["error"]({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})

			// 清空列表
			dispatch({
				type: GET_ILLEGAL_BATCH_LIST,
				data: {
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				}
			})
	})
}
export const setIllegalBatchList = (type,data) => {
	return {
		type,data
	}
}
export const getIllegalAnalyProgress = (options) => (dispatch) => {
	request("GetIllegalAnalyProgress", options)
		.then(res=>{
			dispatch({
				type: GET_ILLEGAL_ANALY_PROGRESS,
				data: {
					companyProgress:res.companyProgress
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取解析进度失败",
				description: err.message || "内部错误"
			})
		})
}