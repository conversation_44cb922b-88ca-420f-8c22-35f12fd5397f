import request from "../utils/request"
import {
	Get_Block_IP_List,
	Get_Block_IP_Log,
} from "../action/banIP"
import {
	notification
} from "antd"

export function setPagination(type, data) {
	return {
		type,
		data
	}
}

export function setHistoryPagination(type, data) {
	return {
		type,
		data
	}
}
export function setLoading(type, data) {
	return {
		type,
		data
	}
}

export const getBlockIPList = (options) => (dispatch) => {
	request("GetBlockIPList", options)
		.then(resp => {
			dispatch({
				type: Get_Block_IP_List,
				data: {
					list: resp.DataSet,
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.Count || 0
					},
					loading: false,
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: Get_Block_IP_List,
				data: {
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				}
			})
		})
}

export const getBlockIPLog = (options) => (dispatch) => {
	request("GetBlockIPLog", options)
		.then(resp => {
			dispatch({
				type: Get_Block_IP_Log,
				data: {
					historyList: resp.DataSet || [],
					historyPagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.Count.length || 0
					},
					loading: false,
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: Get_Block_IP_Log,
				data: {
					loading: false,
					historyList: [],
					historyPagination: {
						current: 1,
						pageSize: 10,
						total: 0
					}
				}
			})
		})
}
