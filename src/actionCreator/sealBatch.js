import request from "../utils/request"
import {
	GET_LIST,
	GET_DETAIL_LIST,
	GET_HISTORY_LIST,
	// SET_DETAIL_LIST
} from "../action/sealBatch"
import {
	notification
} from "antd"
import cloneDeep from "lodash/cloneDeep"

export function setPagination(type, data) {
	return {
		type,
		data
	}
}

export function setHistoryPagination(type, data) {
	return {
		type,
		data
	}
}
export function setLoading(type, data) {
	return {
		type,
		data
	}
}

export const getList = (options) => (dispatch) => {
	request("GetSealBatchList", options)
		.then(resp => {
			dispatch({
				type: GET_LIST,
				data: {
					list: resp.SealBatchList || [],
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.Total || 0
					},
					loading: false,
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: GET_LIST,
				data: {
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				}
			})
		})
}

export const getHistoryList = (options) => (dispatch) => {
	request("GetSealHistory", options)
		.then(resp => {
			dispatch({
				type: GET_HISTORY_LIST,
				data: {
					historyList: resp.HistoryList || [],
					historyPagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.Total || 0
					},
					loading: false,
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: GET_HISTORY_LIST,
				data: {
					loading: false,
					historyList: [],
					historyPagination: {
						current: 1,
						pageSize: 10,
						total: 0
					}
				}
			})
		})
}
export function setDetailPagination(type, data) {
	return {
		type,
		data
	}
}

export function setDetailLoading(type, data) {
	return {
		type,
		data
	}
}

export function setDetailList(type, data) {
	return {
		type,
		data
	}
}

export const getDetailList = (options) => (dispatch) => {
	request("GetSealRecord", options)
		.then(resp => {
			let list = resp.RecordList || []
			let canRetrySeal = false, canRetrySendNotice = false
			list.forEach(( item )=> {
				if (item.SealStatus === "操作失败" || item.RecycleStatus === "7天后回收") {
					canRetrySeal = true
					return
				}
			})
			if (list.length > 0 && list[0].HasNotifyInfo !== "待发送") {
				list.forEach(( item )=> {
					if (item.NotifyInfo) {
						item.NotifyInfo.forEach(notify => {
							if (notify.EmailStatus === "SENDFAILED" || notify.SmsStatus === "SENDFAILED") {
								canRetrySendNotice = true
							}
						})
					}
				})
			} else {
				canRetrySendNotice = true
			}
			dispatch({
				type: GET_DETAIL_LIST,
				data: {
					list: resp.RecordList || [],
					originList:  cloneDeep(resp.RecordList) || [],
					canRetrySeal,
					canRetrySendNotice,
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.TotalCount || 0
					},
					loading: false,
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: GET_DETAIL_LIST,
				data: {
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 10,
						total: 0
					}
				}
			})
		})
}
