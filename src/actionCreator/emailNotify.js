import request from "../utils/request"
import {
	GET_EMAIL_NOTIFY_LIST,
	GET_EMAIL_NOTIFY_TYPE,
	GET_EMAIL_NOTIFY_HISTORY
} from "../action/emailNotify"
import {
	notification
} from "antd"

export function setPagination(type, data) {
	return {
		type,
		data
	}
}

export const getEmailNotifyList = (options) => (dispatch) => {
	dispatch({
		type: GET_EMAIL_NOTIFY_LIST,
		data: {loading: true,list:[]}
	})
	request("GetEmailNotifyList", options)
		.then(res=>{
			dispatch({
				type: GET_EMAIL_NOTIFY_LIST,
				data: {list: res.RecordList, total: res.Total,loading: false}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取待处理邮件记录失败",
				description: err.message || "内部错误"
			})
		})
}
export const getEmailNotifyHisotry = (options) => (dispatch) => {
	dispatch({
		type: GET_EMAIL_NOTIFY_HISTORY,
		data: { list: [], loading: true }
	})
	request("GetEmailNotifyList", options)
		.then(res=>{
			dispatch({
				type: GET_EMAIL_NOTIFY_HISTORY,
				data: {list: res.RecordList, historyTotal: res.Total,loading:false}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取待处理邮件记录失败",
				description: err.message || "内部错误"
			})
		})
}

export const getEmailNotifyType = () => (dispatch) => {
	request("GetEmailNotifyType")
		.then(res=>{
			dispatch({
				type: GET_EMAIL_NOTIFY_TYPE,
				data: res.Types
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取邮件处理类型失败",
				description: err.message || "内部错误"
			})
		})
}