import request from "../utils/request"
import {
	GET_LIST,
	GET_ILLEGAL_LIST,
	GET_ANALY_PROGRESS,
	GET_ILLEGAL_ANALY_PROGRESS
} from "../action/message"
import {
	notification
} from "antd"

//状态枚举,将英文状态转成中文在前端显示
const statusList = {
	NEW: "解析中",
	PROCESSED: "已解析",
	SENDING: "发送中",
	SEND_FINISHED: "发送完成",
	FINISHED: "已完成",
	REFETCHING: "重新获取中",
}

export function setFormValues(type, data) {
	return {
		type,
		data
	}
}

export function setPagination(type, data) {
	return {
		type,
		data
	}
}

export function setLoading(type, data) {
	return {
		type,
		data
	}
}

export const getList = (options) => (dispatch) => {
	request("GetBatchList", options)
		.then(resp => {
			let batchList = []
			resp.BatchList.forEach((value) => {
				value.Status = statusList[value.Status]
				batchList.push(value)
			})
			// if(batchList && batchList[0].Status==="解析中"){
			// 	getAnalyProgress({Id:batchList[0].Id})
			// }
			dispatch({
				type: GET_LIST,
				data: {
					list: batchList || [],
					showAnalyProgress:batchList && batchList[0] && batchList[0].Status==="解析中"?true:false,
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.TotalCount || 0
					},
					loading: false,
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: GET_LIST,
				data: {
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				}
			})
		})
}

export const getAnalyProgress = (options) => (dispatch) => {
	request("GetAnalyProgress", options)
		.then(res=>{
			dispatch({
				type: GET_ANALY_PROGRESS,
				data: {
					companyProgress:res.companyProgress,
					registerProgress:res.registerProgress
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取解析进度失败",
				description: err.message || "内部错误"
			})
		})
}
export const getIllegalAnalyProgress = (options) => (dispatch) => {
	request("GetIllegalAnalyProgress", options)
		.then(res=>{
			dispatch({
				type: GET_ILLEGAL_ANALY_PROGRESS,
				data: {
					companyProgress:res.companyProgress
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取解析进度失败",
				description: err.message || "内部错误"
			})
		})
}
export const getIllegalList = (options) => (dispatch) => {
	request("GetIllegalBatchList", options)
		.then(resp => {
			let batchList = []
			resp.BatchList.forEach((value) => {
				value.Status = statusList[value.Status]
				batchList.push(value)
			})
			dispatch({
				type: GET_ILLEGAL_LIST,
				data: {
					list: batchList || [],
					showAnalyProgress:batchList && batchList[0] && batchList[0].Status === "解析中" ? true : false,
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.TotalCount || 0
					},
					loading: false
				}
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "获取列表失败",
				description: err.message || "内部错误"
			})
			// 清空列表
			dispatch({
				type: GET_LIST,
				data: {
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				}
			})
		})
}

