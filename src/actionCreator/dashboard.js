import request from "../utils/request"
import {
  GET_NO_ICP_DATA,
  GET_ILLAEGAL_ICP_DATA,
  GET_AO_DUN_DATA,
  GET_PERSONAL_AUTH_DATA,
  GET_SEAL_DATA,
  GET_COMPANY_AUTH_DATA,
  GET_REAL_TIME_DATA
} from "../action/dashboard"
import {
  notification
} from "antd"
import { sealTypeDict } from '../utils/config'

//未备案
export const getNoICPData = (params) => (dispatch) => {
  dispatch({
    type: 'SET_NOICPDATA_LOADING',
    loading: true
  })
  request("GetNoRegisterRecordStatistics", params)
    .then(res => {
      dispatch({
        type: GET_NO_ICP_DATA,
        data: res.DataSet,
        loading: false
      })
    })
    .catch(err => {
      dispatch({
        type: 'SET_NOICPDATA_LOADING',
        loading: false
      })
      // 报错
      notification.error({
        message: "获取未备案数据失败",
        description: err.message || "内部错误"
      })

    })
}

//违规
export const getIllegalICPData = (params) => (dispatch) => {
  dispatch({
    type: 'SET_ILLEGALDATA_LOADING',
    loading: true
  })
  request("GetIllegalRecordStatistics", params)
    .then(res => {
      dispatch({
        type: GET_ILLAEGAL_ICP_DATA,
        data: res.DataSet,
        loading: false
      })
    })
    .catch(err => {
      // 报错
      notification.error({
        message: "获取违规备案数据失败",
        description: err.message || "内部错误"
      })
      dispatch({
        type: 'SET_ILLEGALDATA_LOADING',
        loading: false
      })
    })
}
//傲盾流量
export const getAoDunData = (params) => (dispatch) => {
  let dataArray1 = [],
      dataArray2 = [],
      dataArray3 = [],
      data = [],
      regionList = ['bj','gd','sh'],
      actionList = ['GetAodunRecordStatistics','GetAodunBlockRatioStatistics','GetRegionFlowStatistics'],
      getAodunRecordStatistics = ['北京傲盾流量','广东傲盾流量','上海傲盾流量'],
      getAodunBlockRatioStatistics = ['北京机房封堵率','广东机房封堵率','上海机房封堵率'],
      getRegionFlowStatistics = ['北京机房流量','广东机房流量','上海机房流量'],
      number = 0;
  actionList.forEach((item)=>{
    regionList.forEach((ite)=>{
      number++
      request(item, {
        ...params,
        Region: ite
      }).then(res=>{
        if(res.RetCode===0){
          console.log("right",item,ite)
          if(item==="GetAodunRecordStatistics"){
            dataArray1.push(res);
            if(dataArray1.length ===3){
              console.log('dataArray1',dataArray1)
              let  maxLength1 = Math.max(dataArray1[0].DataSet.length, dataArray1[1].DataSet.length, dataArray1[2].DataSet.length)
              for (let i = 0; i < maxLength1; i++) {
                getAodunRecordStatistics.forEach((item1,index1)=>{
                  data.push({...dataArray1[index1].DataSet[i], Name: item1})
                })
               }
            }
          }else if(item==="GetAodunBlockRatioStatistics"){
            dataArray2.push(res);
            if(dataArray2.length ===3){
              console.log('dataArray2',dataArray2)
              let  maxLength2 = Math.max(dataArray2[0].DataSet.length, dataArray2[1].DataSet.length, dataArray2[2].DataSet.length)
            for (let i = 0; i < maxLength2; i++) {
              getAodunBlockRatioStatistics.forEach((item2,index2)=>{
                data.push({Time: dataArray2[index2].DataSet[i].Time, Ratio: dataArray2[index2].DataSet[i].Ratio, Name: item2})
              })
             }
            }
          }else if(item==="GetRegionFlowStatistics"){
            dataArray3.push(res);
            if(dataArray3.length ===3){
            console.log('dataArray3',dataArray3)
            let  maxLength3 = Math.max(dataArray3[0].DataSet.length, dataArray3[1].DataSet.length, dataArray3[2].DataSet.length)
            for (let i = 0; i < maxLength3; i++) {
              getRegionFlowStatistics.forEach((item3,index3)=>{
                data.push({...dataArray3[index3].DataSet[i], Name: item3})
              })
             }
            }
          }
          if(number === 9){
            dispatch({
              type: GET_AO_DUN_DATA,
              data,
              loading: false
            })
          }
        }
        }).catch(()=>{
          console.log("catch",item,ite)
        })
    })
  })
}
//个人认证
export const getPersonalAuthData = (params) => (dispatch) => {
  request("GetAuthStatistics", params)
  .then(res => {
    let DataSet = res.DataSet
    if (DataSet) {
      DataSet = DataSet.map(data => ({
        ...data,
        percent: (data.AutoCount * 100 / (data.AutoCount + data.ArtificialCount)).toFixed(2)
      }))
    }
    dispatch({
      type: GET_PERSONAL_AUTH_DATA,
      data: DataSet
    })
  })
  .catch(err => {
    // 报错
    notification.error({
      message: "获取个人实名认证数据失败",
      description: err.message || "内部错误"
    })
  })
}

//企业认证
export const getCompanyAuthData = (params) => (dispatch) => {
  request("GetCompanyAuthDataStatistics", params)
  .then(res => {
    let DataSet = res.DataSet
    if (DataSet) {
      DataSet = DataSet.map(data => ({
        ...data,
        percent: (data.AutoCount * 100 / (data.AutoCount + data.ArtificialCount)).toFixed(2)
      }))
    }
    dispatch({
      type: GET_COMPANY_AUTH_DATA,
      data: DataSet
    })
  })
  .catch(err => {
    // 报错
    notification.error({
      message: "获取企业实名认证数据失败",
      description: err.message || "内部错误"
    })
  })
}

//封号记录
export const getSealRecordData = (params) => (dispatch) => {
  request("GetSealRecordDataStatistics", params)
    .then(res => {
      let data = []
      if (res.DataSet && res.DataSet[0] && res.DataSet[0].Mate) {
        let mate = res.DataSet[0].Mate
        for(let key in res.DataSet[0].Mate) {
          data.push({type: sealTypeDict[key].replace('封号','').replace('完成',''), value:mate[key]})
        }
      }
      dispatch({
        type: GET_SEAL_DATA,
        data: data
      })
    })
    .catch(err => {
      // 报错
      notification.error({
        message: "获取封号数据失败",
        description: err.message || "内部错误"
      })
    })
}

export const getRealTimeData = () => (dispatch) => {
  request("GetTodayStatistics")
  .then(res => {
    let { AuthData } = res.DataSet
    dispatch({
      type: GET_REAL_TIME_DATA,
      data: {
        AuthData: {
          ArtificialCount: AuthData.PersonArtificialCount + AuthData.CompanyArtificialCount,
          AutoCount: AuthData.PersonAutoCount +  AuthData.CompanyAutoCount,
          AutoRate: AuthData.AutoRate
        },
        NotifyData: {}
      }
    })
  })
  .catch(err => {
    // 报错
    notification.error({
      message: "获取实时数据失败",
      description: err.message || "内部错误"
    })
  })
}
