import { SET_ILLEGAL_BATCH_PAGINATION,GET_ILLEGAL_BATCH_LIST, SET_ILLEGAL_BATCH_LOADING, SET_ILLEGAL_BATCH_LIST, GET_ILLEGAL_ANALY_PROGRESS } from '../action/batchDetail'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 20,
      	total: 0
	},
	list:[]
}
//这是reducer
const reducer = (state = initState, action) => {
	switch (action.type) {
        case SET_ILLEGAL_BATCH_PAGINATION:
			return {...state,pagination:action.data}
		case GET_ILLEGAL_BATCH_LIST:
			return {...state,...action.data}
		case SET_ILLEGAL_BATCH_LOADING:
            return {...state, loading:action.data}
        case SET_ILLEGAL_BATCH_LIST:
			return {...state,list:action.data}
		case GET_ILLEGAL_ANALY_PROGRESS:
			return {...state,...action.data}
		default:
			return state
	}
}
export default reducer