import {
	// SET_BATCH_PAGINATION,
	GET_EMAIL_NOTIFY_LIST,
	GET_EMAIL_NOTIFY_TYPE,
	SET_PAGINATION,
	GET_EMAIL_NOTIFY_HISTORY
} from '../action/emailNotify'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 30,
      	total: 0
	},
	historyTotal: 0,
	types: [],
	list:[],
	history:[]
}
//这是reducer
const reducer = (state = initState, action) => {
	switch (action.type) {
		case GET_EMAIL_NOTIFY_LIST:
			return { ...state, list: action.data.list,
					pagination: {
						...state.pagination,
						total: action.data.total
					}
				}
		case GET_EMAIL_NOTIFY_HISTORY:
			return { ...state, history: action.data.list, historyTotal: action.data.historyTotal, loading: action.data.loading}
		case GET_EMAIL_NOTIFY_TYPE:
			return { ...state, types: action.data }
		case SET_PAGINATION:
			return { ...state, pagination: action.data }
		default:
			return state
	}
}
export default reducer
