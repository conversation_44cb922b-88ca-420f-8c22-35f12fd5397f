import { SET_ILLEGAL_FORM_VALUES, SET_ILLEGAL_PAGINATION, GET_ILLEGAL_LIST, SET_ILLEGAL_LOADING, GET_ILLEGAL_ANALY_PROGRESS } from '../action/message'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 20,
      	total: 0
	},
	list:[]
}
//这是reducer
const illegalMessageReducer = (state = initState, action) => {
	switch (action.type) {
		case SET_ILLEGAL_FORM_VALUES:
			return {...state,formValues:action.data}
		case SET_ILLEGAL_PAGINATION:
			return {...state,pagination:action.data}
		case GET_ILLEGAL_ANALY_PROGRESS:
			return {...state,...action.data}
		case GET_ILLEGAL_LIST:
			return {...state,...action.data}
		case SET_ILLEGAL_LOADING:
			return {...state, loading:action.data}
		default:
			return state
	}
}
export default illegalMessageReducer