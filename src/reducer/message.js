import { 
	SET_FORM_VALUES, 
	SET_PAGINATION, 
	GET_LIST, SET_LOADING, 
	GET_ANALY_PROGRESS, 
	GET_ILLEGAL_ANALY_PROGRESS
} from '../action/message'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 20,
      	total: 0
	},
	list:[]
}
//这是reducer
const messageReducer = (state = initState, action) => {
	switch (action.type) {
		case SET_FORM_VALUES:
            return {...state,formValues:action.data}
        case SET_PAGINATION:
			return {...state,pagination:action.data}
		case GET_LIST:
			return {...state,...action.data}
		case GET_ANALY_PROGRESS:
			return {...state,...action.data}
		case GET_ILLEGAL_ANALY_PROGRESS:
			return {...state,...action.data}
		case SET_LOADING:
			return {...state, loading:action.data}
		default:
			return state
	}
}
export default messageReducer