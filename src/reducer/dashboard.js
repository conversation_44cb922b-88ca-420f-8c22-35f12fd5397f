import {
    GET_NO_ICP_DATA,
    GET_ILLAEGAL_ICP_DATA,
    GET_AO_DUN_DATA,
    GET_PERSONAL_AUTH_DATA,
    GET_SEAL_DATA,
    GET_COMPANY_AUTH_DATA,
    GET_REAL_TIME_DATA,
} from "../action/dashboard"
// const tiger = 10000
const initState = {
    loading:false,
    noICPData: [],
    illegalICPData: [],
    aoDunData: [],
    personalAuthData: [],
    companyAuthData: [],
    sealData: [],
    AuthData: {
        "AutoRate": 0,
        "AutoCount": 0,
        "ArtificialCount": 0,
    },
    NotifyData: {
        "YesterdayCount": 0,
        "TodayCount": 0,
        "FailedRate": 0
    },
    noICPLoading: false,
    illegalICPLoading: false,
    aodunDataLoading: false
}
//这是reducer
const reducer = (state = initState, action) => {
	switch (action.type) {
	case GET_NO_ICP_DATA:
        return { ...state, noICPData: action.data, noICPLoading: action.loading }
    case 'SET_NOICPDATA_LOADING':
        return { ...state, noICPLoading: action.loading }
    case 'SET_ILLEGALDATA_LOADING':
      return { ...state, illegalICPLoading: action.loading }
    case GET_ILLAEGAL_ICP_DATA:
        return { ...state, illegalICPData: action.data, illegalICPLoading: action.loading }
    case GET_AO_DUN_DATA:
        return { ...state, aoDunData: action.data, aodunDataLoading: action.loading }
    case GET_PERSONAL_AUTH_DATA:
        return { ...state, personalAuthData: action.data }
    case GET_SEAL_DATA:
        return { ...state, sealData: action.data }
    case GET_COMPANY_AUTH_DATA:
        return { ...state, companyAuthData: action.data }
    case GET_REAL_TIME_DATA:
        return { ...state, AuthData: {...state.AuthData,...action.data.AuthData}, NotifyData: {...state.NotifyData,...action.data.NotifyData} }
	default:
		return state
	}
}
export default reducer
