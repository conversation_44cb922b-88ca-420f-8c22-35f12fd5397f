import {  
	Get_Block_IP_List,
	Get_Block_IP_Log,
	SET_LOADING,
	SET_PAGINATION,
	SET_HISTORY_PAGINATION
} from '../action/banIP'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 10,
      	total: 0
	},
	historyPagination: {
		current: 1,
      	pageSize: 10
	},
	historyList: [],
	list: []
}
//这是reducer
const BanIPReducer = (state = initState, action) => {
	switch (action.type) {
		case Get_Block_IP_List:
			return {...state, ...action.data}
		case SET_PAGINATION:
			return {...state, pagination: action.data}
		case SET_LOADING:
			return {...state, loading: action.data}
		case Get_Block_IP_Log:
			return {...state, ...action.data}
		case SET_HISTORY_PAGINATION:
			return {...state, historyPagination: action.data}
		default:
			return state
	}
}
export default BanIPReducer