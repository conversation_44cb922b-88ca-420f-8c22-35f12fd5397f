
import {combineReducers} from "redux"
 
import messageReducer from "./message.js"
import illegalMessageReducer from "./illegalMessage"
import batchDetailReducer from "./batchDetail"
import illegalBatchDetailReducer from "./illegalBatchDetail"
import sealBatchReducer from "./sealBatch"
import sealBatchDetailReducer from "./sealBatchDetail"
import dashboardReducer from "./dashboard"
import emailNotifyReducer from "./emailNotify"
import BanIPReducer from "./BanIP"
const reducer = combineReducers({
	messageReducer,
	illegalMessageReducer,
	batchDetailReducer,
	illegalBatchDetailReducer,
	sealBatchReducer,
	sealBatchDetailReducer,
	dashboardReducer,
	emailNotifyReducer,
	BanIPReducer
})
export default reducer