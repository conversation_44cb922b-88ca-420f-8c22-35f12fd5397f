import {  
	GET_LIST,
	GET_HISTORY_LIST,
	SET_LOADING,
	SET_PAGINATION,
	SET_HISTORY_PAGINATION
} from '../action/sealBatch'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 10,
      	total: 0
	},
	historyPagination: {
		current: 1,
      	pageSize: 10
	},
	historyList: [],
	list: []
}
//这是reducer
const sealBatchReducer = (state = initState, action) => {
	switch (action.type) {
		case GET_LIST:
			return {...state, ...action.data}
		case SET_PAGINATION:
			return {...state, pagination: action.data}
		case SET_LOADING:
			return {...state, loading: action.data}
		case GET_HISTORY_LIST:
			return {...state, ...action.data}
		case SET_HISTORY_PAGINATION:
			return {...state, historyPagination: action.data}
		default:
			return state
	}
}
export default sealBatchReducer