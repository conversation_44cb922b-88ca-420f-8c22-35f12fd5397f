import {  
	GET_DETAIL_LIST,
	SET_LOADING,
	SET_DETAIL_LIST,
	SET_DETAIL_PANIGATION
} from '../action/sealBatch'
// const tiger = 10000
const initState = {
	loading:false,
	pagination:{
		current: 1,
      	pageSize: 10,
      	total: 0
	},
	list:[]
}
//这是reducer
const sealBatchDetailReducer = (state = initState, action) => {
	switch (action.type) {
		case GET_DETAIL_LIST:
			return {...state, ...action.data}
		case SET_LOADING:
			return {...state, loading:action.data}
		case SET_DETAIL_LIST:
			return {...state, list: action.data}
		case SET_DETAIL_PANIGATION:
			return {...state, pagination: {...action.data}}
		default:
			return state
	}
}
export default sealBatchDetailReducer