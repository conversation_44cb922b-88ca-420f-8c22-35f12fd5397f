//获取当天开始日期0点-返回时间戳
export const getTodayStartTime = function (){
    return new Date(new Date().toLocaleDateString()).getTime()
}
//获取当前结束日期-返回时间戳
export const getTodayEndTime = function (){
    return  new Date().getTime()
}
//获取本周开始日期-返回时间戳
export const getWeekStartTime = function(){
    const start = new Date(new Date().toLocaleDateString())
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    const t = start.getTime();
    return  t
}
//获取本周结束日期-返回时间戳
export const getWeekEndTime = function(){
    return new Date(new Date().toLocaleDateString()).getTime()
}