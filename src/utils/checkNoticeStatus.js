const checkIsInStatuses = (infos, status) => {
	if(!infos) return
	if (!Array.isArray(status)) status = [status]
	return infos.some(item => status.includes(item.SmsStatus) || status.includes(item.EmailStatus))
}
const checkAllIsInStatuses = (infos, status) => {
	if(!infos) return
	if (!Array.isArray(status)) status = [status]
	return infos.every(item => status.includes(item.SmsStatus) && status.includes(item.EmailStatus))
}

const checkHasOneEmsAndEmailSendOk = (infos) => {
	if(!infos) return
	return infos.some(item => item.SmsStatus === "Received" && item.EmailStatus === "Received")
}

/* 
* 逻辑如下，确定该条的状态
* 1. 只要有一个在发送中，就是发送中
* 2. 当不满足上面的条件时，如果有一个人邮件并且短信发送成功，则认为是成功
* 3. 当不满足上面的条件时，只要有一个失败就是发送失败
* 4. 如果所有的都是禁止发送的，就是禁止发送
* 5. 当不满足上面的条件时， 如果所有的都是 未发送或者禁止发送，就是待发送状态
* 6. 不满足以上所有时，就是发送成功状态
*/
function checkNoticeStatus(notifyInfo) {
	switch (true) {
	case checkIsInStatuses(notifyInfo, ["SENDING"]):
		return "发送中"
	case checkHasOneEmsAndEmailSendOk(notifyInfo):
		return "发送成功"
	case checkIsInStatuses(notifyInfo, ["SENDFAILED", "RECEIVEDFAILED"]):
		return "发送失败"
	case checkAllIsInStatuses(notifyInfo, ["PROHIBIT"]):
		return "禁止发送"
	case checkIsInStatuses(notifyInfo, ["TIMEOUT"]):
		return "超时未响应"
	case checkAllIsInStatuses(notifyInfo, ["NEW", "PROHIBIT","TRYING_TO_SEND"]):
		return "待发送"
	default:
		return "发送成功"
	}
}
export default checkNoticeStatus