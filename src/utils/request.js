import request from "request";
import axios from 'axios';
import { notification } from 'antd';
import Permission from './Permission.json';
const env = require('../../envConfigs/env').default;
const requestUrl = require('../../envConfigs/' + env + '/config');
const ssoUrl = requestUrl.ssoUrl;
let uAuthSystemName = env === "test" ? "Gray." : ".";
export default function api(action, params) {
	return requestApi(action, params,'hegui')
}

//备案信息查询
export const icpApi = function (action, params) {
  return requestApi(action, params,'ICPAdmin')
}
//实名认证
export const idAuthApi = function (action, params) {
    return requestApi(action, params,'IdAuth')
}
//活体认证
export const newAuthApi = function (action, params) {
    return requestApi(action, params,'NewAuth')
}
//公安取证
export const policeCopy = function (action, params) {
  return requestApi(action, params,'PoliceCopy')
}
//数据查询
export const DataSearchApi = function (action, params) {
	return requestApi(action, params,'hegui')
}
//敏感词查询
export const SensitiveApi = function (action, params) {
	return requestApi(action, params,'hegui')
}

//挖矿数据
export const MiningDataApi = function (action, params) {
	return requestApi(action, params,'hegui')
}
/**
 * uauth鉴权--------本地不走uauth，读取Permission.json文件，跟线上不一致时可去更新
 */
export const ssoAuthApi = function (item) {
  if(env === "local"){
    return Promise.resolve(Permission[item])
  }
  return new Promise((resolve, reject) => {
    request({
      uri: ssoUrl + "action=SSO.GetPermission&api_category="+ item + (env === "test" ? "Gray" : ""),
      method: "GET",
      json: true,
      withCredentials: true,
      timeout: 2000
    }, (err, response, body) => {
      resolve(body)
      console.log(reject(err))
    })
  })
}
/**
 * 本地不走uauth，读取local的url
 * @param {*} action api名称
 * @param {*} params 参数
 * @param {*} route  uauth的系统分类，对应uauth系统的设置，https://uauth.ucloudadmin.com/
 * @returns
 */
function requestApi(action,params,route){
	//本地请求，URL可以设置对应的路径，例如：heguiLocalUrl.hegui
    if(env === "local"){
		    const heguiLocalUrl = require('../../envConfigs/local/config.json');
        return axios({
            url: heguiLocalUrl[route] + '/?Action=' + action,//对应着envConfigs/url.json中的相关路径
            method: 'post',
            data: {
                ...params,
                Action: action
            },
        }).then(({
            data
        }) => {
            let result = Promise.resolve(data);

            if (data.RetCode !== 0) {
                notification['error']({
                    message: action,
                    description: `${data.RetCode}: ${data.Message || '未知错误'}`
                })
                result = Promise.reject({
                    message: `${data.RetCode}: ${data.Message || '未知错误'}`
                })
            }
            return result;
        }).catch((e) => {
            notification['error']({
                message: action,
                description:  `${e.message || e.Message || '请求api出错'}`
            })
            return Promise.reject({
                message: `${e.message || e.Message || '请求api出错'}`
            })
        })
    }
	console.log(action,isUrlAuth(action))
    if (isUrlAuth(action)) {
		  let url = ssoUrl + 'Action=' + route + uAuthSystemName + action
	    params = {...params,Action: route+ uAuthSystemName + action}

      if(route === 'ICPAdmin'){
        url = `https://web_api.ucloudadmin.com/newicp/?Action=ICPAdmin.${action}`
        params = {...params,Action: `ICPAdmin.${action}`}
      }
		return new Promise((resolve) => {
		  request({
			uri: url,
			method: 'POST',
			body: {
			  ...params,
			},
			json: true,
			timeout: 2000,
			withCredentials: true,
		  }, (err, response, body) => {
			  resolve(body)
		  })
		})
	  } else {
		notification['error']({
			message: '无权限调用接口'+action,
			description: '请联系管理员开通接口权限'
		})
		return new Promise((resolve, reject) => {
		  request({}, () => {
			reject("无权限")
		  })
		})
	}
}
//判断是否有此action的调用权限
function isUrlAuth(action) {
  let authResult = sessionStorage.getItem("heGuiAuth")
  let result = false
  if (authResult) {
    authResult = JSON.parse(authResult)
    if (Object.prototype.hasOwnProperty.call(authResult,action)) {
      result = true
    }
  }
  return result
}
