{"hegui": {"action": "GetPermissionResponse", "ret_code": 0, "tag": {"445": {"tag_id": 445, "tag_name": "任何人都有权限组", "tag_describe": "所有人"}, "446": {"tag_id": 446, "tag_name": "公安未备案通知", "tag_describe": "公安未备案通知"}, "447": {"tag_id": 447, "tag_name": "其他", "tag_describe": "其他"}, "448": {"tag_id": 448, "tag_name": "合规封禁", "tag_describe": "合规封禁"}, "449": {"tag_id": 449, "tag_name": "合规通知", "tag_describe": "合规通知"}, "450": {"tag_id": 450, "tag_name": "实名制审核", "tag_describe": "实名制审核"}, "451": {"tag_id": 451, "tag_name": "控制台看板", "tag_describe": "控制台看板"}, "452": {"tag_id": 452, "tag_name": "数据查询", "tag_describe": "数据查询"}, "453": {"tag_id": 453, "tag_name": "自动封号", "tag_describe": "自动封号"}, "454": {"tag_id": 454, "tag_name": "购买权限设置", "tag_describe": "购买权限设置"}, "455": {"tag_id": 455, "tag_name": "通用", "tag_describe": "通用"}, "456": {"tag_id": 456, "tag_name": "邮件通知", "tag_describe": "邮件通知"}, "475": {"tag_id": 475, "tag_name": "广告敏感词审核", "tag_describe": "广告敏感词审核"}, "514": {"tag_id": 514, "tag_name": "风险预警", "tag_describe": "根据配置，监控需要关注的公司"}, "545": {"tag_id": 545, "tag_name": "挖矿数据", "tag_describe": "挖矿数据"}, "557": {"tag_id": 557, "tag_name": "公安", "tag_describe": "公安"}, "571": {"tag_id": 571, "tag_name": "跨境报备", "tag_describe": "跨境报备"}, "572": {"tag_id": 572, "tag_name": "风险控制", "tag_describe": "风险控制"}, "577": {"tag_id": 577, "tag_name": "白名单", "tag_describe": "白名单"}}, "permission": {"CheckBuyPermissionBit": 1, "GetNoRegisterRecordStatistics": 1, "CreateSealBatch": 1, "GetSealBatchList": 1, "GetSealHistory": 1, "GetSealNoticeDetail": 1, "GetSealRecord": 1, "GetSealRecordDataStatistics": 1, "GetSealRecordDownload": 1, "GetSealRecordNumber": 1, "GetUnSealAccount": 1, "NoticeSeal": 1, "RetrySealAccount": 1, "UnSealAccount": 1, "GetNeedUpdateBuyPermissionList": 1, "UpdateBuyPermissionBit": 1, "CheckAccountStatus": 1, "CheckDomainRecordStatus": 1, "CheckSensitiveWord": 1, "CreateCDNViolationBatch": 1, "GetCDNNotifyInfo": 1, "GetCompanyInfo": 1, "GetDominBlockInfoInAudun": 1, "GetPictureBase64": 1, "ReadUrlFile": 1, "RefetchCompanyInfo": 1, "SendCDNViolation": 1, "SendSMS": 1, "UploadFile": 1, "GetAodunBlockRatioStatistics": 1, "GetAodunRecordStatistics": 1, "GetAuthStatistics": 1, "GetCompanyAuthDataStatistics": 1, "GetRegionBlockRatio": 1, "GetRegionBlockRatioForISP": 1, "GetRegionFlowStatistics": 1, "GetTodayAuthInfoStatistics": 1, "GetTodayNotifyStatistics": 1, "GetTodayStatistics": 1, "CreateBatch": 1, "CreateRecord": 1, "ExportRecordListFile": 1, "FinishBatch": 1, "GetAbnormalRecord": 1, "GetAnalyProgress": 1, "GetBatchList": 1, "GetNotifyBURecord": 1, "GetNotifyInfoList": 1, "GetRecordList": 1, "GetRecordListV2": 1, "GetSmsInfo": 1, "NotifyBatch": 1, "NotifyBU": 1, "RePushMQUnRegister": 1, "RetryBatch": 1, "RetryNotifyBUById": 1, "SendMailBatch": 1, "SendMailRecord": 1, "UpdateMailBatch": 1, "UpdateRecord": 1, "GetHistoryList": 1, "CreateIllegalBatch": 1, "ExportIllegalRecordList": 1, "FinishIllegalBatch": 1, "GetAbnormalIllegalRecord": 1, "GetDominHaveIllegalAndRegisteredRecordFromNotify": 1, "GetIllegalAnalyProgress": 1, "GetIllegalBatchList": 1, "GetIllegalEmailInfo": 1, "GetIllegalRecordList": 1, "GetIllegalRecordStatistics": 1, "GetIllegalSmsInfo": 1, "GetNotifyBUIllegalRecord": 1, "NotifyBUAboutIllegal": 1, "NotifyIllegalBatch": 1, "RefetchCompanyInfoForIllegal": 1, "RetryIllegalBatch": 1, "RetryIllegalNotifyBUById": 1, "SendIllegalEmail": 1, "SendIllegalSms": 1, "EditMail": 1, "GetMailInfo": 1, "GetEmailNotifyDetail": 1, "GetEmailNotifyList": 1, "GetEmailNotifyType": 1, "IgnoreMailSend": 1, "SendEmail": 1, "SendMail": 1, "CreateIPBlockInfo": 1, "GetIPDomainStatus": 1, "GetIPNotify": 1, "SelectIPBlockInfo": 1, "UnBlockRecordDomain": 1, "UpdateBlockIPInfo": 1, "UpdateDisabledIpNotify": 1, "CreateInfoEvidenceTask": 1, "DescribeInfoEvidenceTask": 1, "FeatchRelevanceCompanyList": 1, "GetInfoEvidenceTaskList": 1, "GetTagList": 1, "ModifyTagsAndRemark": 1, "CreatePoliceRegisterBatch": 1, "EndPoliceRegisterBatch": 1, "GetPoliceRegisterBatchList": 1, "GetPoliceRegisterContent": 1, "GetPoliceRegisterDomainListById": 1, "GetPoliceRegisterListByBatchId": 1, "NotifyPoliceRegisterBatch": 1, "SendPoliceRegisterRecord": 1, "GetRegisteredDomainByRecord": 1, "GetRegisteredDomainList": 1, "UpdateRegisteredDomain": 1, "CheckURLSensitiveWord": 1, "CheckMatchOfEnterprise": 1, "GetIdentityURL": 1, "GetChannelInfo": 1, "GetVerifyLog": 1, "GetUserAuthInfo": 1, "UpdateIdentityStatus": 1, "GetEmailInfo": 1, "AddWhiteWordToURL": 1, "DescribeSensitiveWordBatch": 1, "GetSensitiveWordBatchList": 1, "UpdateSensitiveWordRecord": 1, "GetFileURL": 1, "GetAuditedFaceAuthList": 1, "AuditFaceAuthWithManualReview": 1, "GetRiskArea": 1, "GetRiskBatchList": 1, "GetRiskRecordInfo": 1, "GetRiskRecordList": 1, "UpdateRiskArea": 1, "AddMailTemplate": 1, "UpdateMailTemplate": 1, "DeleteMailTemplate": 1, "GetMailTempList": 1, "DescribeMiningCheckBatch": 1, "DescribeMiningPool": 1, "AddMiningPoolConfig": 1, "DescribeMiningBatchDetails": 1, "CreatePoliceCase": 1, "ModifyPoliceCase": 1, "GetPoliceCaseDetail": 1, "GetPoliceCaseList": 1, "GetPoliceType": 1, "DelRegisteredDomainList": 1, "DelVerbalTrick": 1, "AddVerbalTrick": 1, "DescribeVerbalTricks": 1, "AuditCrossBorderApply": 1, "GetCrossBorderApplyLog": 1, "GetCrossBorderApplyList": 1, "GetPictureUrl": 1, "AddGlobalWhiteWord": 1, "ModifyGlobalWhiteWord": 1, "GetGlobalWhiteList": 1}, "TrackSN": "1bc30266-96ca-42c4-b5da-51d5bf32112c"}, "PoliceCopy": {"action": "GetPermissionResponse", "ret_code": 0, "tag": {"457": {"tag_id": 457, "tag_name": "公安取证", "tag_describe": "公安取证"}}, "permission": {"GetCopyTaskList": 1, "GetCopyTaskDetail": 1, "GetBackupTaskList": 1, "DeleteCopyTask": 1, "GetBackupTaskDetail": 1, "GetCompanyList": 1, "PrepareCopyTask": 1, "CreateCopyTask": 1, "TerminateCopyTask": 1, "RetryDiskCopyTask": 1, "PrepareBackupTask": 1, "CreateBackupTask": 1, "GetZoneList": 1, "GetTargetDiskList": 1, "DeleteBackupTask": 1, "RetryDiskBackupTask": 1, "TerminateBackupTask": 1}, "TrackSN": "73ab8647-71cf-43dd-8c4a-0664a3e1ec09", "TrackSNV2": "15ee2c84cab03ce3c3382ea270c7c0f2"}, "IdAuth": {"action": "GetPermissionResponse", "ret_code": 0, "tag": {"458": {"tag_id": 458, "tag_name": "实名制审核", "tag_describe": "实名制审核"}}, "permission": {"FinishLegal": 1, "AutoTransferAccounts": 1, "SetUserAuthStatusPass": 1, "GetBankInfoByCardNo": 1}, "TrackSN": "cc0e7163-49b5-4120-98c8-8d34473266ab"}, "NewAuth": {"action": "GetPermissionResponse", "ret_code": 0, "tag": {"579": {"tag_id": 579, "tag_name": "新实名", "tag_describe": "新实名接口"}}, "permission": {"DescribeManualAuthInfo": 1, "GetUS3File": 1, "AuditFaceAuthWithManualReview": 1, "DescribeAuthLog": 1, "CheckPersonalInfo": 1}, "TrackSN": "6e8bd955-542f-41b3-902b-7174620f6a59"}, "ICPAdmin": {"action": "GetPermissionResponse", "ret_code": 0, "tag": {"389": {"tag_id": 389, "tag_name": "信息验证相关api", "tag_describe": "审核信息验证"}, "390": {"tag_id": 390, "tag_name": "审核相关api", "tag_describe": "审核"}, "391": {"tag_id": 391, "tag_name": "订单查看搜索类api", "tag_describe": "获取订单信息"}, "392": {"tag_id": 392, "tag_name": "删除订单", "tag_describe": "是删除动作的api"}, "393": {"tag_id": 393, "tag_name": "同步五图和恒安相关", "tag_describe": "同步到恒安"}, "395": {"tag_id": 395, "tag_name": "核验日志查看api", "tag_describe": "核验--日志查看类"}, "418": {"tag_id": 418, "tag_name": "通用api", "tag_describe": "通用"}, "420": {"tag_id": 420, "tag_name": "备案信息查看类api", "tag_describe": "备案查看"}, "421": {"tag_id": 421, "tag_name": "备案信息修改类api", "tag_describe": "修改备案信息"}, "422": {"tag_id": 422, "tag_name": "主体分配", "tag_describe": "主体分配到指定的公司"}, "423": {"tag_id": 423, "tag_name": "设置", "tag_describe": "代理、白名单等设置"}, "424": {"tag_id": 424, "tag_name": "规则配置", "tag_describe": "规则配置"}, "425": {"tag_id": 425, "tag_name": "同步", "tag_describe": "同步tab中的api"}, "537": {"tag_id": 537, "tag_name": "通知", "tag_describe": "通知相关api"}, "539": {"tag_id": 539, "tag_name": "删除配置", "tag_describe": "删除配置相关api"}, "550": {"tag_id": 550, "tag_name": "大客户域名白名单", "tag_describe": "大客户域名白名单"}, "553": {"tag_id": 553, "tag_name": "域名加白", "tag_describe": "域名加白"}, "555": {"tag_id": 555, "tag_name": "数据看板", "tag_describe": "数据看板"}, "568": {"tag_id": 568, "tag_name": "billing", "tag_describe": "计费相关功能"}}, "permission": {"GetFuzzyOrderList": 1, "GetOrderVerifyLog": 1, "GetOrderInfo": 1, "ApplyConfig": 1, "ApplyRule": 1, "CheckEpicEntPersonRelation": 1, "GetIdenityOCRInfo": 1, "CheckPersonalInfo": 1, "CheckPICMainPersonRepetitionInfo": 1, "CheckTwoFaceMatch": 1, "CheckWebOwnerRepetitionInfo": 1, "CompressFile": 1, "CreateCompanyInfoCheckBatch": 1, "DeleteConfig": 1, "DeleteOrder": 1, "DescribeOnlineConfig": 1, "DescribeCompanyInfoCheckBatch": 1, "DescribeRule": 1, "DownloadCompanyInfoCheckBatch": 1, "EditConfig": 1, "EditRule": 1, "ForceStopCompanyInfoCheck": 1, "GetBlockRule": 1, "GetCompanyInfoCheckBatchList": 1, "GetConfigList": 1, "GetICPInfoList": 1, "RePushCompanyInfoCheckBatch": 1, "SetBlockRule": 1, "UpdateStatus": 1, "DescribeICPOrder": 1, "StopCompanyInfoCheckBatch": 1, "StartCompanyInfoCheckBatch": 1, "GetBlockList": 1, "GetBlockLogList": 1, "AddBlockCompany": 1, "DeleteBlockCompany": 1, "GetIpWhiteList": 1, "AddIpWhite": 1, "DeleteIpWhite": 1, "GetOrderList": 1, "GetCompanyInfoByEmail": 1, "GetProxyCompanyList": 1, "CreateProxyCompany": 1, "UpdateProxyCompany": 1, "RejectOrder": 1, "ResolveOrder": 1, "UploadPicture": 1, "GetPicture": 1, "GetPictureContent": 1, "GetICPList": 1, "GetDistributionLogList": 1, "DistributionICPRecord": 1, "ModifyCurtain": 1, "ModifyOrder": 1, "ModifyWebsite": 1, "DescribeICPConfig": 1, "AutoSyncCid": 1, "CancelConnectRequest": 1, "CancelMainBodyRecord": 1, "CancelWebsite": 1, "ConnectUpdateInterfaceAction": 1, "EpicEntPersonRelation": 1, "GetAreaAndUpdate": 1, "GetSyncLogList": 1, "ICPPictureSyncFromHengAn": 1, "ICPSyncFromHengAn": 1, "MainOnlyUpdateInterfaceAction": 1, "MainUpdateInterfaceAction": 1, "ReceiveICPRecord": 1, "ReceiveICPRecordId": 1, "ReceiveICPRecordRG": 1, "SelectICPInterface": 1, "SubmitICPInfo": 1, "SubmitTransfer": 1, "SubmitWebsiteInfo": 1, "SyncSetCId": 1, "TwoFaceMatch": 1, "WebsiteUpdateInterfaceAction": 1, "GetICPInfo": 1, "GetIpWhiteLogList": 1, "GetIcpCommonConfigOnlineList": 1, "EditCommonConfig": 1, "ApplyCommonConfig": 1, "AddDomainWriteList": 1, "UpdateDomainWriteList": 1, "DeleteDomainWriteList": 1, "GetDomainWriteOperatorLog": 1, "GetDomainWriteList": 1, "ChangeCurtainPicture": 1, "GetOptionalCurtainPictures": 1, "SyncCIDToHengan": 1, "DeleteProxyCompany": 1, "UploadUnresourceBatch": 1, "GenerateUnresourceBatch": 1, "GetUnResourcesBatchList": 1, "DescribeUnResourcesBatch": 1, "GetAbnormalUnresourceData": 1, "RePullAbnormalRecordInfo": 1, "NotifyUnResourcesBatch": 1, "FinishUnResourcesBatch": 1, "RetrySendUnResources": 1, "DescribeProxyCompanyDomainList": 1, "DescribeProxyCompanyDomainActionList": 1, "AddProxyCompanyDomain": 1, "DeleteProxyCompanyDomain": 1, "GetAuditRateForAuditors": 1, "GetAuditRateForCompany": 1, "GetAuditRateForSupervision": 1, "GetCostForAPI": 1, "CreateNotifyBatch": 1, "GetNotifyBatchList": 1, "DescribeNotifyBatch": 1, "GetNotifyDetail": 1, "NotifyBatch": 1, "FinishNotifyBatch": 1, "RetryNotify": 1, "CreateICPMigrationBillOrder": 1, "GetBillingDetails": 1, "GetBillingList": 1, "PostBill": 1, "DownloadICPInfoFromHengan": 1, "DescribeVerbalTricks": 1, "AddVerbalTrick": 1, "DelVerbalTrick": 1, "CompareHenganOnlineData": 1, "GetNotifyTemplate": 1, "CreateNotifyTemplate": 1, "GetNotifyTypeList": 1, "ModifyNotifyTemplate": 1, "CheckDomainRegisterMsg": 1, "GetDomainRegisterMsg": 1, "CreateICPCancelOrder": 1, "GetNoAccessBatchList": 1, "UploadNoAccessBatch": 1, "GetNoAccessBatchDetail": 1, "GetNoAccessBUNotifyData": 1, "NoAccessNotifyBatch": 1, "NoAccessFinishBatch": 1, "GetNoAccessAbnormalData": 1, "NoAccessRetrySend": 1, "CreateBulkBlockDomain": 1, "DescribeBulkBlockBatch": 1, "DescribeBulkBlockRecord": 1, "StartBulkBlockDomain": 1, "StopBulkBlockDomain": 1, "AddDomainWhiteList": 1, "DeleteDomainWhiteList": 1, "GetDomainWhiteList": 1, "GetDomainWhiteOperatorLog": 1, "UpdateDomainWhiteList": 1, "DomainExpireCheck": 1, "CheckDomainAuth": 1, "UploadDomainCheck": 1, "GetDomainBatchList": 1, "GetDomainBatchInfo": 1, "CreateICPQuotaBillOrder": 1, "GetNotifyContent": 1, "CreateWebCheckBatch": 1, "GetWebCheckBatchList": 1, "GetWebCheckRecordList": 1, "GetClearOrderBatchList": 1, "GetClearOrderRecordList": 1, "ChangeClearBatchStatus": 1, "ClearExpiredOrder": 1, "CheckDomainMatchWebNo": 1, "GetBillCount": 1, "GetPictureMetaInfo": 1, "CheckICPCompanyIdAndNameMatch": 1, "QueryCompanyRisk": 1, "ICPSyncFromAodun": 1, "GetAwaitDistributionICPRecord": 1, "GetBusinessInfoByTYC": 1, "GetBusinessChangeRecordByTYC": 1, "GetSpiderBatchList": 1, "GetSpiderBatchInfo": 1, "UploadSpiderDomain": 1, "CheckWords": 1, "SpiderDomain": 1, "PatchSpiderDomain": 1, "GetCompanyInfoByTYC": 1}, "TrackSN": "3a474e02-9051-47f0-b210-1e24666446ef", "ChainSN": "8428258c"}}