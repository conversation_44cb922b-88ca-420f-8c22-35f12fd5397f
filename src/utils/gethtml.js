export default function gethtml(newHtml, record) {
	let text = record.Text
	// if (!newHtml.includes("class=\"J-reply\"")) {
	// 	newHtml += `<div class="J-reply" style="background-color:#f2f2f2;color:black;padding-top:6px;padding-bottom:6px;border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;margin-top:45px;margin-bottom:20px;font-family:'微软雅黑';"> <div style="font-size:12px;line-height:1.5;word-break:break-all;margin-left:10px;margin-right:10px">On <span class="mail-date">${new Date(record.CreateTime * 1000)}</span>，<a class="mail-to" style="text-decoration:none;color:#2a83f2;" href="mailto:${record.From}">BitNinja&lt;${record.From}&gt;</a> wrote： </div></div>`
	// }
	//判断邮件正文是否有html外壳
	if (text && text.includes("<html>") && text.includes("<body>")) {
		let index = text.indexOf("<body>")
		//将模版插入到body中
		text = text.substr(0, index + 6) + newHtml + text.substr(index + 6)
		let lastIndex = text.indexOf("</body>") + 7
		//检查body中是否有<style>*</style>内容，如果有就删除掉。
		const styleReg = /<style[^>]*>[^<>]*<\/style>/gim
		let str = text.substr(index + 6, lastIndex)
		while (str.match(styleReg) && str.match(styleReg).length > 0) {
			text = text.substr(0, index + 6) + str.replace(styleReg,"") + text.substr(lastIndex)
			str = text.substr(index + 6, lastIndex)
			lastIndex = text.indexOf("</body>") + 7
		}
		//去掉现有模版
		let filterStr = "<div key=\"template\">"
		if(text.includes(filterStr)) {
			let start = text.indexOf(filterStr)
			let end = text.substr(start).indexOf("</div>") + start + 6
			text = text.substr(0, start) + text.substr(end)
		}
		return text
	}
	
	return `<html>
	<head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>  <style>  font{    line-height: 1.6;  }  ul,ol{    padding-left: 20px;    list-style-position: inside;  }</style></head>
	<body>
	<div style = 'font-family:微软雅黑,Verdana,&quot;Microsoft Yahei&quot;,SimSun,sans-serif;font-size:14px; line-height:1.6;'>
		<div><div>${newHtml}</div><div>    <span>      <br>    </span>  </div></div>
	</div>
	<div class="J-reply" style="background-color:#f2f2f2;color:black;padding-top:6px;padding-bottom:6px;border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;margin-top:45px;margin-bottom:20px;font-family:'微软雅黑';">  
		<div style="font-size:12px;line-height:1.5;word-break:break-all;margin-left:10px;margin-right:10px">
			On <span class="mail-date">${new Date(record.CreateTime * 1000)}</span>，
			<a class="mail-to" style="text-decoration:none;color:#2a83f2;" href="mailto:${record.From}">BitNinja&lt;${record.From}&gt;</a> wrote： 
		</div>
	</div>
	<blockquote id="ntes-pcmail-quote" style="margin: 0; padding: 0; font-size: 14px; font-family: '微软雅黑';">${record.Text} </blockquote><!--?-->
	</body>
	</html>`
}
