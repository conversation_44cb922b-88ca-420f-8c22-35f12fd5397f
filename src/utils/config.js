const AuthType = {
	CPMPANY_AUTH: "企业认证",
	PERSONAL_AUTH: "个人认证",
}


export const personAuthState = {
	AuthPending: "待审核",
	AuthPicFail: "证照驳回",
	AuthPicPass: "证照通过",
}
export const AuthState = {
	AuthPending: "待审核",
	AuthPicFail: "证照驳回",
	AuthPicPass: "证照通过",
	AutoRemitting: "自动打款中",
	AuthFinanceFail: "财务驳回",
	AuthSuccess: "已认证"
}
//证照审核状态
export const AuthPicStatus = {
	[AuthState.AuthPending]: "wait",
	[AuthState.AuthPicFail]: "error",
	[AuthState.AuthPicPass]: "finish",
	[AuthState.AutoRemitting]: "finish",
	[AuthState.AuthFinanceFail]: "finish",
	[AuthState.AuthSuccess]: "finish"
}
//财务审核状态
export const AuthFinanceStatus = {
	[AuthState.AuthPending]: "wait",
	[AuthState.AuthPicFail]: "wait",
	[AuthState.AuthPicPass]: "wait",
	[AuthState.AutoRemitting]: "wait",
	[AuthState.AuthFinanceFail]: "error",
	[AuthState.AuthSuccess]: "finish"
}

export const AuthStep = {
	[AuthState.AuthPending]: 1,
	[AuthState.AuthPicFail]: 1,
	[AuthState.AuthPicPass]: 2,
	[AuthState.AutoRemitting]: 2,
	[AuthState.AuthSuccess]: 3,
}
export const AuthTypeMap = {
	[AuthType.CPMPANY_AUTH]: 0,
	[AuthType.PERSONAL_AUTH]: 1
}

/**
 * 批量封号/解封
 */
export const sealTypes = {
	// UNSEAL: 1,
	OTHER_SEAL: 2,
	UNAUTH_SEAL: 3,
	NO_RULE_SEAL: 4,
	FALSE_ICP: 5,
	FALSE_AUTH: 6,
	ABNORMAL_AUTH: 7,
	ACCOUNT_OVE: 8,
	SPITE_REGISTER: 9
}
export const sealTypeDict = {
	// [sealTypes.UNSEAL]: "解封",
	// [sealTypes.OTHER_SEAL]: "其他原因封号",
	[sealTypes.UNAUTH_SEAL]: "未完成实名认证封号",
	[sealTypes.NO_RULE_SEAL]: "业务违规封号",
	[sealTypes.FALSE_ICP]: "虚假备案封号",
	[sealTypes.FALSE_AUTH]: "虚假实名封号",
	[sealTypes.ABNORMAL_AUTH]: "实名异常封号",
	[sealTypes.ACCOUNT_OVE]: "账户欠费封号",
	[sealTypes.SPITE_REGISTER]: "恶意注册",
	[sealTypes.OTHER_SEAL]: "其他原因封号"
}

export const statusList = {
	NEW: "新建",
	SENDING: "发送中",
	RECEIVED: "已经接收",
	SENDFAILED: "发送失败",
	RECEIVEFAILED: "接收失败",
	PROHIBIT: "禁止发送邮件或短信",
	TIMEOUT: "超时未响应"
}

// 邮件自动处理用
export const mailStatus = {
	INIT: 0,
	WUFAJIEXI: 1,
	DENGDAIJIEXI: 2,
	DAIFASONG: 3,
	SENDING: 4,
	SEND_FINISHED: 5,
	FINISHED: 6
}

// 邮件自动处理用
export const mailStatusDict = {
	[mailStatus.INIT]: "初始化",
	[mailStatus.WUFAJIEXI]: "待人工处理",
	[mailStatus.DENGDAIJIEXI]: "解析中",
	[mailStatus.DAIFASONG]: "待发送",
	[mailStatus.SENDING]: "发送中",
	[mailStatus.SEND_FINISHED]: "发送完成",
	[mailStatus.FINISHED]: "已完成"
}

export const mailStatusIcon = {
	0: {
		type: "edit",
		twoToneColor:""
	},
	1: {
		type: "clock-circle",
		twoToneColor: "#faad14"
	},
	2: {
		type: "check-circle",
		twoToneColor: "#52c41a"
	},
	3: {
		type: "close-circle",
		twoToneColor: "#f5222d"
	},
	4: {
		type: "close-circle",
		twoToneColor: "#f5222d"
	},
	5: {
		type: "clock-circle",
		twoToneColor: "#faad14"
	},
	6: {
		type: "exclamation-circle",
		twoToneColor: "#f5222d"
	}
}