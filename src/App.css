@import '~antd/dist/antd.css';

body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}
#weatherWrap{
  position: absolute;
  width: 100%;
  left: 50%;
  top:10px;
}
.App {
  text-align: center;
}

.App-logo {
  animation: App-logo-spin infinite 20s linear;
  height: 80px;
}

.App-header {
  background-color: #222;
  height: 150px;
  padding: 20px;
  color: white;
}

.App-title {
  font-size: 1.5em;
}

.App-intro {
  font-size: large;
}

@keyframes App-logo-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

::-webkit-scrollbar {
  width:6px;
  /* height:6px; */
}
::-webkit-scrollbar-thumb {
  background-clip:padding-box;
  border-radius:3px;
}

