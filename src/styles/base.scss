@import 'src/styles/const';

* {
  margin: 0;
  padding: 0;
}

::-webkit-scrollbar {
  width: 0;
  height: 6px;
}

::-webkit-scrollbar-track {
  border-radius: 9em;
  background-color: transparent;
  box-shadow: unset;
}

::-webkit-scrollbar-thumb {
  border-radius: 9em;
  background-color: #ddd;
}

body {
  font-size: 12px;
  background-color: #f0f2f5;
}

:global {
  .fs12 {
    font-size: 12px;
  }

  .fs13 {
    font-size: 13px;
  }

  .fs14 {
    font-size: 14px;
  }

  .red {
    color: #ca3232;
  }

  .normal-weight {
    font-weight: normal;
  }

  .green {
    color: green;
  }

  .gray {
    color: gray;
  }

  .pointer {
    cursor: pointer;
  }

  .tip {
    color: #888;
  }

  .bold {
    font-weight: bold;
  }

  .link {
    color: var(--primary-color);
    cursor: pointer;
    position: relative;
    display: inline-block;

    &:before {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -1px;
      width: 0;
      height: 1px;
      background: var(--primary-color);
      transition: all 0.3s;
    }

    &:hover {
      color: var(--primary-color);

      &:before {
        width: 100%;
        left: 0;
        right: 0;
      }
    }

    &:focus {
      &:before {
        width: 100%;
        left: 0;
        right: 0;
      }
    }
  }

  .hover-primary {
    &:hover {
      color: var(--primary-color);
    }
  }

  .block-title {
    margin-bottom: 8px;
    color: #444;
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding-left: 12px;
    position: relative;

    &:before {
      content: '';
      display: block;
      width: 3px;
      height: 14px;
      background-color: var(--primary-color);
      position: absolute;
      left: 0;
    }

    &:first-child {
      margin-top: 0;
    }

    &.offset {
      margin-left: -12px;
    }
  }

  .block-offset {
    padding: 0 10px;
  }

  .pointer {
    cursor: pointer;
  }

  .light {
    color: var(--primary-color);
  }

  .hidden {
    display: none;
    opacity: 0;
  }

  .layout-card {
    margin-bottom: 8px;
  }

  .flex-start {
    display: flex;
    justify-content: flex-start;
  }

  .flex-start-middle {
    @extend .flex-start;
    align-items: center;
  }

  .flex-center {
    display: flex;
    justify-content: center;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
  }

  .flex-between-middle {
    @extend .flex-between;
    align-items: center;
  }

  .flex-end {
    display: flex;
    justify-content: flex-end;
  }

  .flex-end-middle {
    @extend .flex-end;
    align-items: center;
  }

  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
  }

  .flex-none {
    flex: none;
  }

  .text-wrap {
    white-space: pre-wrap;
    word-break: break-all;
  }

  .ant-card {
    font-size: 12px;
    border: none !important;

    .ant-card-head {
      padding: 0 8px;
      min-height: 40px;
      font-size: 13px;

      .ant-card-head-title {
        padding: 10px 0;
      }
    }

    .ant-card-body {
      padding: 8px;
    }
  }

  .relative {
    position: relative;
  }

  .checkbox-rect {
    margin: -3px;

    .ant-checkbox-wrapper {
      cursor: pointer;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 4px;
      margin: 3px;

      &.ant-checkbox-wrapper-checked {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }

    .ant-checkbox {
      display: none;
    }
  }

  .right-line {
    position: absolute;
    width: 1px;
    height: 100%;
    background-color: #e6e6e6;
    right: 0;
    top: 0;
    z-index: 1;
  }

  .ant-alert-with-description {
    &.ant-alert-no-icon {
      padding: 8px 10px;
    }
  }

  .ant-alert-description {
    font-size: 12px;
    line-height: 1.6;
  }

  .ant-card {
    .ant-card-head {
      font-size: 14px;
    }
  }

  .ant-radio-group {
    margin: -2px;

    .ant-radio-button-wrapper {
      margin: 2px 0;
    }
  }

  .ant-table {
    font-size: 12px;

    &.ant-table-small {
      font-size: 12px;
    }

    .ant-table-thead {
      .ant-table-column-has-sorters {
        .ant-table-column-sorters {
          padding: 0;
        }
      }

      tr:not(tr:only-child) {
        &:first-child,
        &:nth-child(2) {
          th {
            border-right: 1px solid #fff !important;

            &:last-child {
              border-right: 1px solid #f0f0f0;
            }
          }
        }

        &:first-child th {
          background-color: #eee;
        }

        &:nth-child(2) th {
          background-color: #f6f6f6;
        }
      }
    }

    .ant-table-measure-row {
      td {
        padding: 0 !important;
      }
    }

    .ant-table-column-sorters {
      padding: 6px;
    }

    .ant-table-cell {
      white-space: pre-wrap;
      word-break: break-all;
      padding: 6px !important;
    }
  }

  .ant-divider-horizontal {
    margin: 16px 0;
  }

  .ant-select-selector {
    border: 1px solid #bbb !important;
  }

  .page-layout-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;

      &::before {
        display: none;
      }

      .ant-tabs-tab {
        background: transparent;
        border-color: transparent;
      }

      .ant-tabs-tab-active {
        background: #fff;
        border-color: #fff;
      }
    }
  }

  .watermark {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9999999999;
    pointer-events: none;
    background-size: 120px 50px;
  }

  .extra-items {
    font-size: 14px;
    display: flex;
    align-items: center;
    font-weight: 400;

    & > * {
      margin-left: 12px;
    }
  }

  .simple-table {
    width: 100%;

    td {
      border: 1px solid #f0f0f0;
      padding: 6px 8px;
    }
  }

  .full-chart {
    width: 100% !important;

    div {
      width: 100% !important;
    }
  }

  .echarts-for-react {
    * {
      max-width: 100%;
    }

    & > div:first-child {
      width: 100% !important;

      & > canvas {
        width: 100% !important;
      }
    }
  }
}

@media screen and (max-width: $pad) {
  input {
    font-size: 14px !important;
    line-height: 20px !important;
  }

  :global {
    .ant-modal-root {
      .ant-modal {
        top: 0;
        left: 0;
        margin: 2vw;
        width: calc(100vw - 4vw) !important;
        padding-bottom: 0;

        .ant-modal-body {
          padding: 10px;
        }
      }
    }
  }
}
