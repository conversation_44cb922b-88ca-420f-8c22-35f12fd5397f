import React from "react";
import { Route, Redirect, Switch } from "react-router-dom";
import "./App.css";
import { Layout, Icon, ConfigProvider } from "antd";
import Footer from "./layouts/Footer";
import Menu from "./layouts/Menu.jsx";
import asyncComponent from "./components/async-component";
import RealnameList from "./pages/Realname/List";
import RealnamePersonalList from "./pages/Realname/ListPersonal";
// import BuyPermissionList from './pages/BuyPermission/List'
// import HistoryList from './pages/BuyPermission/History'
// import RealnameDetail from './pages/Realname/Detail'
import MessageList from "./pages/Message";
// import Messagdetail from './pages/Message/BatchDetail'
// import NotifyInfodetail from './pages/Message/NotifyInfodetail'
import RegisteredDomain from "./pages/RegisteredDomain/List";
import IllegalMessageList from "./pages/IllegalMessage";
// import IllegalMessageDetail from './pages/IllegalMessage/BatchDetail'
// import IllegalNotifyInfodetail from './pages/IllegalMessage/NotifyInfodetail'

import PoliceRegisterList from "./pages/PoliceRegister/List";
// import PoliceRegisterDetail from './pages/PoliceRegister/BatchDetail'

import PoliceCopyList from "./pages/PoliceCopy/List";
// import PoliceCopyDetail from './pages/PoliceCopy/BatchDetail'
import PoliceCreate from "./pages/PoliceCopy/BatchCreate";
import PoliceCreateCopyTask from "./pages/PoliceCopy/PoliceCreateCopyTask";

import BlockList from "./pages/BlockInfo/List";
import BanAccountList from "./pages/BanAccount/Index";
import WhiteListUnblock from "./pages/BanAccount/whiteListUnblock";
// import BanAccountDetail from './pages/BanAccount/Detail'
// import BankAccountMessageDetail from './pages/BanAccount/MessageDetail'
import Home from "./pages/home/";
import EmailNotify from "./pages/EmailNotify";
// import EmailDetail from './pages/EmailNotify/emailDetail'
import EmailHistory from "./pages/EmailNotify/history";
// import EmailHistoryDetail from './pages/EmailNotify/historyDetail'

import zhCN from "antd/lib/locale-provider/zh_CN";
import DataSearch from "./pages/DataSearch/DataSearch";
import DataSeachDetail from "./pages/DataSearch/DataSearchDetail.jsx";
// import { browserRedirect } from './utils/checkUserAgent';
import { ssoAuthApi } from "./utils/request";
import _ from "lodash";
import SensitiveWords from "./pages/SensitiveWords/index";
import SensitiveBatchDetails from "./pages/SensitiveWords/BatchDetailsPage";
import BatchApply from "./pages/SensitiveWords/BatchApplyPage";
import RiskWarning from "./pages/RiskWarning/index";
import RiskControl from "./pages/RiskControl/index";
import RiskWarningDetails from "./pages/RiskWarning/RiskWarningDetails";
import JunkEmailDetails from "./pages/RiskWarning/JunkEmailDetails";
import DNSRiskDetails from "./pages/RiskWarning/DNSRiskDetails";
import PayAbnormalDetails from "./pages/RiskWarning/PayAbnormalDetails.jsx";
import AbnormalModifyDetails from "./pages/RiskWarning/AbnormalModifyDetails";
import RiskWarningApplyPage from "./pages/RiskWarning/RiskWarningApplyPage";
import MiningData from "./pages/MiningData/index";
import MiningDataDetails from "./pages/MiningData/miningDataDetail";
import AddressManage from "./pages/MiningData/miningDataManage";
import MenuList from "./layouts/menuList";
import PoliceCase from "./pages/PoliceCase/index";
import CaseDetails from "./pages/PoliceCase/PoliceCaseDetails";
import CrossReportAudit from "./pages/Realname/CrossReportAudit";
import AuditSet from "./pages/Realname/AuditSet";
import APPAudit from "./pages/Realname/APPAudit";
import RealnameReexam from "./pages/Realname/RealnameReexam";
import ExclusiveCloudAudit from "./pages/Realname/ExclusiveCloudAudit";
import WhiteList from "./pages/SensitiveWords/whiteList";
import BanIP from "./pages/BanAccount/BanIP";
import GlobalSSHPage from "./pages/RiskControl/GlobalSSHPage";
const env = require("../envConfigs/env").default;
const requestUrl = require("../envConfigs/" + env + "/config");
const heguiUrl = requestUrl.heguiUrl;
const { Content, Header } = Layout;

//合规前端权限模块划分分别对应403无权限，管理员全部权限，部分栏目模块，不同角色只能看相对应角色的模块，权限配置在uAuth里面
const uAuthList = ["hegui", "PoliceCopy", "IdAuth", "NewAuth", "ICPAdmin"];
class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      collapsed: false,
      noAuth: false,
      menuList: [],
      menuListLength: 0,
    };
  }

  toggle = () => {
    this.setState({
      collapsed: !this.state.collapsed,
    });
  };
  requestAuthData = async () => {
    let promiseAll = [];
    uAuthList.forEach((item) => {
      promiseAll.push(this.getLoginUser(item));
    });
    await Promise.all(promiseAll);
    let object = {};
    let tag = {};
    uAuthList.forEach((item) => {
      if (sessionStorage.getItem(item)) {
        let param = JSON.parse(sessionStorage.getItem(item));
        object = { ...object, ...param };
      }
      if (sessionStorage.getItem(item + "Tag")) {
        let param = JSON.parse(sessionStorage.getItem(item + "Tag"));
        tag = { ...tag, ...param };
      }
    });
    if (_.isEmpty(object) && _.isEmpty(tag)) {
      this.setState({
        //noAuth: true
        menuList: MenuList,
        menuListLength: MenuList.length,
      });
    } else {
      sessionStorage.setItem("heGuiAuth", JSON.stringify(object));
      let array = [];
      for (var i in MenuList) {
        for (var j in tag) {
          if (
            MenuList[i].title === tag[j].tag_name &&
            !array.includes(MenuList[i])
          ) {
            array.push(MenuList[i]);
          }
        }
      }
      this.setState(
        {
          noAuth: false,
          menuList: array,
          menuListLength: array.length,
        },
        () => {
          console.log(this.state.menuList);
        }
      );
    }
  };
  componentDidMount() {
    console.log("线上环境");
    this.requestAuthData();
  }
  getLoginUser = (item) => {
    return ssoAuthApi(item).then((resp) => {
      if (resp.ret_code && resp.ret_code === -100 && env !== "local") {
        window.location.href = `https://cas.ucloudadmin.com/cas/login?service=${heguiUrl}`;
      } else if (resp.ret_code === 0) {
        if (!_.isEmpty(resp.permission)) {
          sessionStorage.setItem(item, JSON.stringify(resp.permission));
          sessionStorage.setItem(item + "Tag", JSON.stringify(resp.tag));
        }
      } else {
        sessionStorage.setItem(item, JSON.stringify({}));
        sessionStorage.setItem(item + "Tag", JSON.stringify({}));
      }
    });
  };
  render() {
    let { noAuth, collapsed, menuList, menuListLength } = this.state;
    const noauthStyle = {
      width: 800,
      height: 500,
      padding: 200,
      fontSize: 32,
      textAlign: "center",
      margin: "0 auto",
    };
    const NoAuth = () => {
      return (
        <div style={noauthStyle}>
          403(ಡωಡ)hiahiahia，抱歉～您暂无权限，请联系管理员开通权限～
        </div>
      );
    };
    return (
      <ConfigProvider locale={zhCN}>
        <Layout style={{ minHeight: "100vh" }}>
          {menuListLength ? (
            <Menu
              collapsed={collapsed}
              menuList={menuList}
              onCollapse={this.toggle}
            />
          ) : null}
          <Layout style={{ position: "relative" }}>
            <div style={{ position: "fixed", width: "100%", zIndex: "999" }}>
              <Header style={{ background: "#fff", padding: 0 }}>
                <Icon
                  className="trigger"
                  type={collapsed ? "menu-unfold" : "menu-fold"}
                  onClick={this.toggle}
                />
                <div id="weatherWrap">
                  <iframe
                    scrolling="no"
                    src="https://widget.tianqiapi.com/?style=tg&skin=pitaya"
                    frameBorder="0"
                    width="500"
                    height="60"
                  ></iframe>
                </div>
              </Header>
            </div>
            <div style={{ height: "64px" }}></div>
            {noAuth ? (
              <NoAuth />
            ) : (
              <Content style={{ margin: "10px" }}>
                {menuListLength ? (
                  <Switch>
                    <Route
                      exact
                      path="/"
                      render={() => <Redirect to={menuList[0].path} />}
                    />
                    <Route
                      exact
                      path="/dashboard"
                      component={(props) => <Home {...props} />}
                    />
                    <Route exact path="/realname" component={RealnameList} />
                    <Route
                      exact
                      path="/realnamePersonal"
                      component={RealnamePersonalList}
                    />
                    <Route
                      exact
                      path="/buypermission"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 BuyPermissionList 组件
                        () =>
                          import(
                            /* webpackChunkName: 'buy-permission-list' */ "./pages/BuyPermission/List"
                          )
                      )}
                    />
                    <Route
                      exact
                      path="/buypermission/historylist"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 HistoryList 组件
                        () =>
                          import(
                            /* webpackChunkName: 'buy-permission-history' */ "./pages/BuyPermission/History"
                          )
                      )}
                    />
                    <Route
                      path="/realname/detail/:companyId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 RealnameDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'realname' */ "./pages/Realname/Detail"
                          )
                      )}
                    />
                    <Route
                      path="/realnamePersonal/detail/:companyId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 RealnameDetail 组件
                        () => import("./pages/Realname/PersonalDetail")
                      )}
                    />
                    <Route exact path="/message" component={MessageList} />
                    <Route
                      path="/message/BatchDetail/:BatchId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 Messagdetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'message-batch' */ "./pages/Message/BatchDetail"
                          )
                      )}
                    />
                    <Route
                      path="/message/NotifyInfodetail/:NotifyInfo"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 NotifyInfodetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'notifyinfo-info' */ "./pages/Message/NotifyInfodetail"
                          )
                      )}
                    />
                    <Route
                      exact
                      path="/IllegalMessage"
                      component={IllegalMessageList}
                    />
                    <Route
                      path="/IllegalMessage/BatchDetail/:BatchId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 IllegalMessageDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'illegal-message' */ "./pages/IllegalMessage/BatchDetail"
                          )
                      )}
                    />
                    <Route
                      path="/IllegalMessage/NotifyInfodetail/:NotifyInfo"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 IllegalNotifyInfodetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'illegal-notifyinfo' */ "./pages/IllegalMessage/NotifyInfodetail"
                          )
                      )}
                    />
                    <Route
                      path="/RegisteredDomain"
                      component={RegisteredDomain}
                    />
                    <Route
                      path="/PoliceRegister/BatchList"
                      component={PoliceRegisterList}
                    />
                    <Route
                      path="/PoliceRegister/BatchDetail/:BatchId/:Type"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 PoliceRegisterDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'police-register' */ "./pages/PoliceRegister/BatchDetail"
                          )
                      )}
                    />
                    <Route
                      exact
                      path="/PoliceCopy/BatchList"
                      component={PoliceCopyList}
                    />
                    <Route
                      path="/PoliceCopy/BatchCreate"
                      component={PoliceCreate}
                    />
                    <Route
                      path="/PoliceCopy/PoliceCreateCopyTask"
                      component={PoliceCreateCopyTask}
                    />
                    <Route
                      path="/PoliceCopy/BatchDetail/:Id"
                      component={asyncComponent(() =>
                        import(
                          /* webpackChunkName: 'policy-batch-detail' */ "./pages/PoliceCopy/BatchDetail"
                        )
                      )}
                    />
                    <Route path="/BlockInfo/BlockList" component={BlockList} />
                    <Route
                      exact
                      path="/banAccount"
                      component={BanAccountList}
                    />
                    <Route
                      exact
                      path="/portUnblock"
                      component={WhiteListUnblock}
                    />
                    <Route
                      exact
                      path="/banAccount/detail/:BatchId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 BanAccountDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'ban-account' */ "./pages/BanAccount/Detail"
                          )
                      )}
                    />
                    <Route exact path="/banIP" component={BanIP} />
                    <Route
                      exact
                      path="/banAccount/message/:BatchId/:CompanyId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 BankAccountMessageDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'ban-account-message' */ "./pages/BanAccount/MessageDetail"
                          )
                      )}
                    />
                    <Route exact path="/emailNotify/" component={EmailNotify} />
                    <Route
                      exact
                      path="/emailNotify/detail"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 EmailDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'email' */ "./pages/EmailNotify/emailDetail"
                          )
                      )}
                    />
                    <Route
                      exact
                      path="/emailNotify/history"
                      component={EmailHistory}
                    />
                    <Route
                      exact
                      path="/emailNotify/history/detail"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 EmailHistoryDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'email-history' */ "./pages/EmailNotify/historyDetail"
                          )
                      )}
                    />
                    <Route
                      exact
                      path="/emailNotify/ControlPanel"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 EmailHistoryDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'email-history' */ "./pages/EmailNotify/controlPanel"
                          )
                      )}
                    />
                    <Route exact path="/DataSearch" component={DataSearch} />
                    <Route
                      exact
                      path="/DataSearch/DataSeachDetail"
                      component={DataSeachDetail}
                    />
                    <Route
                      exact
                      path="/DataSearch/DataSeachDetail"
                      component={DataSeachDetail}
                    />
                    <Route
                      exact
                      path="/DataSearch/ChargeInfo"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载 EmailHistoryDetail 组件
                        () =>
                          import(
                            /* webpackChunkName: 'email-history' */ "./pages/DataSearch/ChargeInfo.jsx"
                          )
                      )}
                    />
                    <Route
                      exact
                      path="/DataSearch/BanResource"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/DataSearch/BanResource/List")
                      )}
                    />
                    <Route
                      exact
                      path="/DataSearch/ScamFraudPhone"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/DataSearch/ScamFraudPhone/List")
                      )}
                    />

                    <Route
                      exact
                      path="/SensitiveWords"
                      component={SensitiveWords}
                    />
                    <Route
                      exact
                      path="/SensitiveWords/SensitiveBatchDetails"
                      component={SensitiveBatchDetails}
                    />
                    <Route
                      exact
                      path="/SensitiveWords/SensitiveBatchDetails/BatchApply"
                      component={BatchApply}
                    />
                    <Route exact path="/RiskWarning" component={RiskWarning} />
                    <Route exact path="/RiskControl" component={RiskControl} />
                    <Route
                      exact
                      path="/RiskControl/GlobalSSH"
                      component={GlobalSSHPage}
                    />
                    <Route
                      exact
                      path="/RiskWarning/RiskWarningDetails"
                      component={RiskWarningDetails}
                    />
                    <Route
                      exact
                      path="/RiskWarning/RiskWarningDetails/RiskWarningApplyPage"
                      component={RiskWarningApplyPage}
                    />
                    <Route
                      exact
                      path="/RiskWarning/JunkEmailDetails"
                      component={JunkEmailDetails}
                    />
                    <Route
                      exact
                      path="/RiskWarning/DNSRiskDetails"
                      component={DNSRiskDetails}
                    />
                    <Route
                      exact
                      path="/RiskWarning/PayAbnormalDetails"
                      component={PayAbnormalDetails}
                    />
                    <Route
                      exact
                      path="/RiskWarning/AbnormalModifyDetails"
                      component={AbnormalModifyDetails}
                    />
                    <Route exact path="/MiningData" component={MiningData} />
                    <Route
                      exact
                      path="/MiningData/MiningDataDetails"
                      component={MiningDataDetails}
                    />
                    <Route
                      exact
                      path="/MiningData/AddressManage"
                      component={AddressManage}
                    />
                    <Route exact path="/PoliceCase" component={PoliceCase} />
                    <Route
                      exact
                      path="/PoliceCase/CaseDetails"
                      component={CaseDetails}
                    />
                    <Route
                      exact
                      path="/CrossReportAudit"
                      component={CrossReportAudit}
                    />
                    <Route
                      exact
                      path="/CrossReportAuditDetails/:companyId"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/Realname/CrossReportAuditDetails")
                      )}
                    />
                    <Route exact path="/AuditSet" component={AuditSet} />
                    <Route exact path="/APPAudit" component={APPAudit} />
                    <Route
                      exact
                      path="/RealnameReexam"
                      component={RealnameReexam}
                    />
                    <Route
                      exact
                      path="/RealNameReexamDetail/:FlowId&:Id"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/Realname/RealNameReexamDetail")
                      )}
                    />
                    <Route
                      exact
                      path="/APPAuditDetails/:FlowId&:Id"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/Realname/APPAuditDetails")
                      )}
                    />
                    <Route exact path="/WhiteList" component={WhiteList} />
                    <Route
                      exact
                      path="/ExclusiveCloudAudit"
                      component={ExclusiveCloudAudit}
                    />
                    <Route
                      exact
                      path="/ExclusiveCloudAuditDetail/:CompanyID/:AuthState/:TargetEnv?"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () =>
                          import("./pages/Realname/ExclusiveCloudAuditDetail")
                      )}
                    />
                    <Route
                      exact
                      path="/WhiteList/PhoneNumberBindingCount"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () =>
                          import("./pages/WhiteList/PhoneNumberBindingCount")
                      )}
                    />
                    <Route
                      exact
                      path="/WhiteList/PhoneNumberTop"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/WhiteList/PhoneNumberTop")
                      )}
                    />
                    <Route
                      exact
                      path="/WhiteList/DNSWhiteList"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/WhiteList/DNSWhiteList")
                      )}
                    />
                    <Route
                      exact
                      path="/WhiteList/EmailSuffix"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/WhiteList/EmailSuffix")
                      )}
                    />
                    <Route
                      exact
                      path="/WhiteList/UniversityAuthEmail"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/WhiteList/UniversityAuthEmail")
                      )}
                    />
                    <Route
                      exact
                      path="/WhiteList/GlobalSSH"
                      component={asyncComponent(
                        // 异步加载函数，异步地加载  组件
                        () => import("./pages/WhiteList/GlobalSSH")
                      )}
                    />
                    <Route
                      exact
                      path="/RiskControl/EmailSuffixBlacklist"
                      component={asyncComponent(() =>
                        import("./pages/RiskControl/EmailSuffixBlacklist")
                      )}
                    />
                  </Switch>
                ) : null}
              </Content>
            )}
            <Footer />
          </Layout>
        </Layout>
      </ConfigProvider>
    );
  }
}
export default App;
