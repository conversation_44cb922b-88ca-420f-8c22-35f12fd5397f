import React, { Component } from 'react';
import { Button, Form, Select, Radio, Input } from 'antd';
import Country from '../../utils/country';
const Option = Select.Option;
class NewCaseForm extends Component {
    constructor(props) {
        super(props)
        this.state = {
            Configs: this.props.Configs || {
                "1": "非法经营",
                "2": "操纵证券市场",
                "3": "涉及区块链的虚拟货币盗窃",
                "4": "盗窃案",
                "5": "网络赌博",
                "6": "帮助信息网络犯罪",
                "7": "敲诈勒索",
                "8": "侵犯公民信息",
                "9": "非法获取计算机信息系统数据",
                "10": "侵权",
                "11": "非法吸收公众存款",
                "12": "违规",
                "13": "涉嫌组织、领导传销活动",
                "14": "传播淫秽物品牟利",
                "15": "诈骗",
                "16": "非法制造枪支",
                "17": "侵犯著作权",
                "18": "涉案",
                "19": "寻衅滋事",
                "20": "非法利用信息网络"
            }
        };
    }
    onFinish = (e) => {
        e.preventDefault();
        this.props.form.validateFieldsAndScroll((err, values) => {
        if (!err) {
            console.log('Received values of form: ', values);
            values.Type = parseInt(values.Type);
            values.CompanyId =  parseInt(values.CompanyId);
            values.Notified =  parseInt(values.Notified);
            this.props.onFinish(values)
        }
        });
    };
    onFinishFailed = () => {
        this.props.form.resetFields();
        this.props.onFinish(null)
    };
    getDepartment = () => {
        const Configs = this.state.Configs
        return              <Select
                            showSearch
                            filterOption={(input, option) =>
                                    option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            >
            {
                Object.keys(Configs).map(item => <Option value={item} key={item+Configs[item]}>{Configs[item]}</Option>)
            }
        </Select>
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        return (
            <Form
                name="basic"
                labelCol={{
                    span: 8,
                }}
                wrapperCol={{
                    span: 16,
                }}
                onSubmit={this.onFinish}
                style={{marginRight:80}}
            >
                <Form.Item
                    label="来源省份"
                    name="Province"
                    required={true}
                >
                   {getFieldDecorator('Province',{
								rules: [{ required: true, message: '请输入来源省份' }],
							})(<Select
                            showSearch
                            filterOption={(input, option) =>
                                    option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            >{Object.keys(Country).map(item => <Option value={item} key={item}>{item}</Option>)}</Select>)
                            }
                </Form.Item>
                <Form.Item
                    label="执法部门"
                    name="Department"
                    required={true}
                >
                    {getFieldDecorator('Department', {
                        rules: [
                            {
                                required: true,
                                message: '请输入执法部门',
                            }
                        ],
                    })(<Input/>)}
                </Form.Item>
                <Form.Item
                    label="案件名称"
                    name="Name"
                    required={true}
                >
                    {getFieldDecorator('Name', {
                        rules: [
                            {
                                required: true,
                                message: '请输入案件名称',
                            }
                        ],
                    })(<Input />)}
                </Form.Item>
                <Form.Item
                    label="案件类型"
                    name="Type"
                    required={true}
                >
                    {getFieldDecorator('Type', {
                        rules: [
                            {
                                required: true,
                                message: '请输入案件类型',
                            }
                        ],
                    })(this.getDepartment())}
                </Form.Item>
                <Form.Item
                    label="公司ID"
                    name="CompanyId"
                    required={true}
                >
                    {getFieldDecorator('CompanyId', {
                        rules: [
                            {
                                required: true,
                                message: '请输入公司ID',
                            }
                        ],
                    })(<Input />)}
                </Form.Item>
                <Form.Item
                    label="协查IP"
                    name="IP"
                >{getFieldDecorator('IP')(<Input />)}
                </Form.Item>
                <Form.Item
                    label="是否通知协查"
                    name="Notified"
                    required={true}
                > {getFieldDecorator('Notified', {
                    rules: [
                        {
                            required: true,
                            message: '是否通知协查',
                        }
                    ],
                })(<Radio.Group>
                    <Radio value="1">收到</Radio>
                    <Radio value="0">未收到</Radio>
                </Radio.Group>)}
                </Form.Item>
                <Form.Item
                    label="备注"
                    name="Remark"
                >
                    {getFieldDecorator('Remark')(<Input />)}
                </Form.Item>
                <Form.Item
                    wrapperCol={{
                        offset: 8,
                        span: 16,
                    }}
                >
                    <Button type="primary" htmlType="submit">
                        创建案件
                    </Button>
                    <Button style={{ marginLeft: 20 }} onClick={this.onFinishFailed}>取消</Button>
                </Form.Item>
            </Form>
        );
    }
}
const WrappedNewCaseForm = Form.create({ name: 'NewCase' })(NewCaseForm);

export default WrappedNewCaseForm;