import React, { Component ,Fragment} from 'react';
import { Card,Row,Form,Col,Input, Table, Button, Tabs,notification, DatePicker, Modal } from 'antd';
import exportFile from '../../components/expoertFile/index';
import { SensitiveApi } from '../../utils/request';
import moment from 'moment';
import { Link } from 'react-router-dom';
import  NewCaseForm from './NewCaseForm';
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const FormItem = Form.Item;
const STATUS = {
    0:"处理中",
    1:"处理完成"
}
const NotifiedSTATUS = {
    "0":"未收到",
    "1":"收到"
}
class PoliceCase extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            Name:"",
            Department: "",
            CompanyId: "",
            StartTime: "",
            EndTime:"",
            modalVisile:false,
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
            Configs: {
                "1": "非法经营",
                "2": "操纵证券市场",
                "3": "涉及区块链的虚拟货币盗窃",
                "4": "盗窃案",
                "5": "网络赌博",
                "6": "帮助信息网络犯罪",
                "7": "敲诈勒索",
                "8": "侵犯公民信息",
                "9": "非法获取计算机信息系统数据",
                "10": "侵权",
                "11": "非法吸收公众存款",
                "12": "违规",
                "13": "涉嫌组织、领导传销活动",
                "14": "传播淫秽物品牟利",
                "15": "诈骗",
                "16": "非法制造枪支",
                "17": "侵犯著作权",
                "18": "涉案",
                "19": "寻衅滋事",
                "20": "非法利用信息网络"
            },
        };

    }
    // 挂载前查询
    componentDidMount() {
        this.GetPoliceType();
    }
    //下载excel
    async downloadExcelFile(row = {}) {
    try {
      if (row.length === 0) {
        notification['error']({
          message: '无下载内容',
          description: '无下载内容'
        })
        return
      }
      const Configs = this.state.Configs;
      let dataList = row.reduce((prev, item) => {
        prev.push([
          item.Id || '无',
          moment(item.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss') || '无',
          item.Province || '无',
          item.Department || '无',
          NotifiedSTATUS[item.Notified] || '无',
          Configs[item.Type] || '无',
          item.Name || '无',
          item.CompanyId || '无',
          item.Level || '无',
          item.BU || '无',
          item?.Auth?.[0]?.CompanyName || '无',
          item?.Auth?.[0]?.AuthType || '无',
          item.Ip || '无',
          item.IpAera || '无',
          item.Resources ? item.Resources.length : '无',
          item.UpdateTime ? moment(item.UpdateTime * 1000).format('YYYY-MM-DD HH:mm:ss') :'无',
          item.ConsumTime || '无',
          item.Remark || '无'
        ]);
        return prev;
      }, [["序号", "取证对接日期", "执法部门省份", "执法部门", "协查通知状态", "案件类型","案件名称","公司ID","客户等级","所属BU","公司名/用户名","认证类型","协查IP","IP归属地","服务器数量","完成日期","完成周期","备注"]]);
      let culumnWidthArray = [10, 10, 10, 10, 10, 10,10,10,10,10,10,10,10,10,10,10,10];
      let fileName = '导出.xlsx';
      exportFile(dataList, culumnWidthArray, fileName);
    } catch (err) {
      notification['error']({
        message: '当前下载任务信息失败',
        description: err.message || '内部错误'
      })
    }
    }
    onSearch = ()=>{
        this.setState({
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
        },()=>{
            this.GetPoliceCaseList()
        })
    }
    GetPoliceCaseList = ()=> {
        const { Name,CompanyId,StartTime,EndTime,Department } = this.state
        let options = {
            Action :'GetPoliceCaseList',
            Name: Name,
            StartTime:StartTime,
            EndTime:EndTime,
            CompanyId:CompanyId,
            Department:Department,
            Limit: this.state.pagination.pageSize,
            Offset: this.state.pagination.pageSize * (this.state.pagination.current - 1)
        }
        if(!Name){
            delete options.Name
        }
        if(!Department){
            delete options.Department
        }
        if(!CompanyId){
            delete options.CompanyId
        }
        if(!StartTime){
            delete options.StartTime
        }
        if(!EndTime){
            delete options.EndTime
        }
        this.setState({ loading: true })
        SensitiveApi('GetPoliceCaseList', options)
            .then(resp => {
                let message = '查询成功'
                this.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                        let {current,pageSize} = this.state.pagination
                        this.setState({
                            list: resp.Rows,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.TotalCount,
                            }
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    GetPoliceType = ()=> {
        let action = 'GetPoliceType';
        SensitiveApi(action, {
            Action:action
        }).then(resp => {
                let message = '查询成功'
                if (resp.RetCode === 0) {
                        this.setState({
                            Configs: resp.Configs,
                        },()=>{
                            this.GetPoliceCaseList()
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    CreatePoliceCase = (params) => {
      let action = 'CreatePoliceCase';
      SensitiveApi(action, params).then(resp => {
        let message = '创建成功'
        if (resp.RetCode === 0) {
          this.setState({
            pagination: {
              current: 1,
              pageSize: 20,
              total: 0
            },
          }, () => {
            this.GetPoliceCaseList()
          })
          return
        } else {
          message = resp.Message || resp.RetCode + "创建失败"
        }
        notification.open({
          message: message,
        });
      }).catch(err => {
        // 报错
        notification['error']({
          message: '请求失败',
          description: err.message || '内部错误'
        })
        return;
      })
    }
    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.GetPoliceCaseList()
        })
    }
    //上部份，获取信息
    renderAdvancedForm() {
        return (
            <Form layout="inline" className="ant-advanced-search-form">
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144444}>
                        <FormItem label="案件名称">
                            <Input style={{ width: '100%' }} value={this.state.Name} placeholder="请输入案件名称" onChange={(e) => { this.setState({ Name: e.target.value }) }} />
                        </FormItem>
                    </Col>
                    <Col span={10} key={1555}>
                        <FormItem label="执法部门">
                            <Input style={{ width: '100%' }} value={this.state.Department} placeholder="请输入执法部门" onChange={(e) => { this.setState({ Department: e.target.value }) }} />
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={1555}>
                        <FormItem label="公司ID">
                            <Input style={{ width: '100%' }} value={this.state.CompanyId} placeholder="请输入公司ID" onChange={(e) => { this.setState({ CompanyId: e.target.value?parseInt(e.target.value):""}) }} />
                        </FormItem>
                    </Col>
                    <Col span={10} key={144664}>
                    <FormItem label="时间">
                    <RangePicker
                        style={{width:'100%'}}
                        ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
                        showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
                        format="YYYY-MM-DD"
                        onChange= {(v)=>{
                            this.setState({
                                StartTime: moment(v[0]?.format("YYYY-MM-DD")).unix(),
                                EndTime:moment(v[1]?.format("YYYY-MM-DD")).unix(),
                            },()=>{
                                console.log(moment(v[0]?.format("YYYY-MM-DD")).unix(),moment(v[1]?.format("YYYY-MM-DD")).unix())
                            })
                        }}
                    />
                    </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={6} key={2} >
                        <FormItem style={{ width: '100%', marginLeft: "80px" }} label="">
                            <Button style={{ marginRight: "16px" }} onClick={this.onSearch} type="primary">查询</Button>
                            <Button onClick={() => {
                                window.location.reload();
                            }}>重置</Button>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        );
    }
    download = ()=>{
        let params = {}
        let { Name, Department, CompanyId, StartTime, EndTime } = this.state
        if(Name){
            params['Name'] = Name
        }
        if(CompanyId){
            params['CompanyId'] = CompanyId
        }
        if(Department){
            params['Department'] = Department
        }
        if(StartTime && EndTime){
            params['StartTime'] = StartTime
            params['EndTime'] = EndTime
        }
        let self = this,
            action = 'GetPoliceCaseDetail',
            options = {
                    Action: action,
                    ...params
            }
            SensitiveApi(action, options)
                .then(resp => {
                    self.setState({
                        loading: false,
                    })
                    let message = '查询成功'
                    if (resp.RetCode === 0) {
                        if (resp.Rows) {
                            this.downloadExcelFile(resp.Rows)
                        }
                        return
                    } else {
                        message = resp.Message || resp.RetCode + "查询失败"
                    }
                    notification.open({
                        message:message
                    })

                }).catch(err => {
                    // 报错
                    notification['error']({
                        message: '请求失败',
                        description: err.message || '内部错误'
                    })
                    return;
                })

    }
    onFinish = (params)=>{
        if(params){
            if(!params.Remark){
                params.Remark = ""
            }
            this.CreatePoliceCase(params)
        }
        this.setState({
            modalVisile:false
        })
    }
    render() {
        let { loading, list, modalVisile,Configs ={}, pagination} = this.state;
        const columns1 = [
            {
                title: '单号',
                dataIndex: 'No',
            },
            {
                title: '案件名称',
                dataIndex: 'Name',
            },
            {
                title: '案件类型',
                dataIndex: 'Type',
                render:val=>Configs[val] || val
            },
            {
                title: '公司ID',
                dataIndex: 'CompanyId',
            },
            {
                title: '处理状态',
                dataIndex: 'Status',
                render:val=>STATUS[val]
            },
            {
                title: '创建人',
                dataIndex: 'CreatedBy',
            },
            {
                title: '创建时间',
                dataIndex: 'CreateTime',
                render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
            },
            {
                title: '详情',
                dataIndex: 'Id',
                render: (val) => {
                    let urlAddress = `/PoliceCase/CaseDetails?Id=${val}`
                    return (
                      <Fragment>
                        {
                          <Link to={urlAddress} target="_blank">更多</Link>
                        }
                      </Fragment>
                    )
                  },
            }
        ]
        pagination = {
             ...pagination,
             showQuickJumper: true,
             showSizeChanger: true,
        }
        return (
            <Card bordered={false}>
                <div>
                    <Card title="搜索" style={{marginBottom: 24 }} bordered={false} >
                        {this.renderAdvancedForm()}
                    </Card>
                    <Button style={{ margin: "16px" }} onClick={()=> {
                        this.setState({
                          modalVisile: true
                        })
                    }} type="primary">创建案件</Button>
                    <Button  type="download" onClick = {this.download}>导出Excel</Button>
                    <Tabs defaultActiveKey="TaskList">
                        <TabPane tab="结果" key="GetList">
                            <Table
                                loading={loading}
                                dataSource={list}
                                columns= {columns1}
                                pagination={pagination}
                                onChange={this.handleTableChange}
                            />
                        </TabPane>
                    </Tabs>
                    <Modal
                        title = "新建案件"
                        width={700}
                        visible = {modalVisile}
                        onCancel={()=>{this.setState({modalVisile:false})}}
                        footer={null}
                    >
                        <NewCaseForm Configs={Configs} onFinish={this.onFinish}/>
                    </Modal>
                </div>
            </Card>
        )
    }
}

export default PoliceCase;
