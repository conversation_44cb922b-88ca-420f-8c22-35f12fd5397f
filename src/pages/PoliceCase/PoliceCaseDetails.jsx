import React, { Component } from 'react';
import { Card, Col,Table,Row,DatePicker,Tabs,notification, Select} from 'antd';
import { SensitiveApi } from '../../utils/request';
import { Breadcrumb } from 'antd';
import moment from 'moment';
import OperationsOnNote from './OperationsOnNote';
import './details.css';
import 'moment/locale/zh-cn';
moment.locale('zh-cn');
const { TabPane } = Tabs;
const STATUS = [
    {
        key: 0,
        value: "处理中"
    },
    {
        key: 1,
        value: "处理完成"
    }
]
const NotifiedSTATUS = {
    "0":"未收到",
    "1":"收到"
}
const col4 = {
    height:60,
    padding: "16px 16px",
    border: "1px solid #e8e8e8",
    background: "rgba(0,0,0,0.06)",
    textAlign: "center",
}
const col20 = {
    height:60,
    padding: "16px 16px",
    border: "1px solid #e8e8e8",
    fontWeight: "bolder"
}
const col21 = {
    height:60,
    border: "1px solid #e8e8e8",
    fontWeight: "bolder"
}
//状态枚举,将英文状态转成中文在前端显示
class PoliceCaseDetails extends Component {
    constructor(props) {
        super(props)
        let params = decodeURI(this.props.location.search.split("Id=")[1])
        this.state = {
            data:{},
            Configs: {
                "1": "非法经营",
                "2": "操纵证券市场",
                "3": "涉及区块链的虚拟货币盗窃",
                "4": "盗窃案",
                "5": "网络赌博",
                "6": "帮助信息网络犯罪",
                "7": "敲诈勒索",
                "8": "侵犯公民信息",
                "9": "非法获取计算机信息系统数据",
                "10": "侵权",
                "11": "非法吸收公众存款",
                "12": "违规",
                "13": "涉嫌组织、领导传销活动",
                "14": "传播淫秽物品牟利",
                "15": "诈骗",
                "16": "非法制造枪支",
                "17": "侵犯著作权",
                "18": "涉案",
                "19": "寻衅滋事",
                "20": "非法利用信息网络"
            },
            loading: false,
            Id: parseInt(params) || 0,
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
        };

    }
    // 挂载前查询
    componentDidMount() {
        this.GetPoliceType();
    }
    GetPoliceCaseDetail = ()=> {
        let {Id} = this.state
        let self = this,
            action = 'GetPoliceCaseDetail',
            options = {
                Action: action,
                Id: Id
            }
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                self.setState({
                    loading: false,
                })
                let message = '查询成功'
                if (resp.RetCode === 0) {
                        self.setState({
                            data: resp.Rows[0],
                            Province:resp.Rows[0]?.Province,
                            Department:resp.Rows[0]?.Department,
                            Name:resp.Rows[0]?.Name,
                            Remark:resp.Rows[0]?.Remark,
                            CopyTime:resp.Rows[0]?.CopyTime,
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
        })
    }
    handleChangeSelect = (e)=>{
        this.ModifyPoliceCase({name:'Status',value:e})
    }
    ModifyPoliceCase = (params)=> {
        const {Id} = this.state
        let self = this,
            action = 'ModifyPoliceCase',
            options = {
                Action: action,
                Id: Id,
            }
        options[params.name] = params.value
        console.log('options',options)
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                self.setState({
                    loading: false,
                })
                let message = '修改成功'
                if (resp.RetCode === 0) {
                    this.GetPoliceCaseDetail()
                    return
                } else {
                    message = resp.Message || resp.RetCode + "修改失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
        })
    }
    GetPoliceType = ()=> {
        let action = 'GetPoliceType';
        SensitiveApi(action, {
            Action:action
        }).then(resp => {
                let message = '查询成功'
                if (resp.RetCode === 0) {
                        this.setState({
                            Configs: resp.Configs,
                        },()=>{
                            this.GetPoliceCaseDetail()
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    getCopyTime = (time)=> {
        let format = "YYYY-MM-DD";
        let result = <DatePicker
                    style={{width:300}}
                    format= {format}
                    onChange= {(date)=>{
                         this.ModifyPoliceCase({name:'CopyTime',value: moment(date.format(format)).unix()+""})
                    }}></DatePicker>
        try{
            if(time){
                time = moment((time * 1000)).format(format);
                result = <DatePicker
                style={{width:300}}
                format= {format}
                value ={moment(time,format)}
                onChange= {(date)=>{
                    this.ModifyPoliceCase({name:'CopyTime',value: moment(date.format(format)).unix()+""})
                }}></DatePicker>
            }
        }catch(e){
            console.log(e)
        }
        return result
    }
    render() {
        let { loading, data,Configs={}} = this.state;
        const columns1 = [
            {
                title: '资源ID',
                dataIndex: 'ResourceId',
            },
            {
                title: '数据中心',
                dataIndex: 'RegionCN',
            },
            {
                title: '数据大小',
                dataIndex: 'DiskCapacity',
            }
        ];
        const columns2 = [
            {
                title: '公司Id',
                dataIndex: 'CompanyId'
            },
            {
                title: '单号',
                dataIndex: 'No'
            },
            {
                title: '案件名称',
                dataIndex: 'Name',
            }
        ];
        const timeFormat = (time)=>{
            if(time){
                return moment(time * 1000).format('YYYY-MM-DD HH:mm:ss')
            }
            return time
        }
        const getTotal = (list)=>{
            let total = 0
            if(list && list.length>0){
                list.map(item=>{
                    if(item.DiskCapacity){
                        total = total + item.DiskCapacity
                    }
                })
                return total
            }
            return total
        }
        const style = {width:"100%",height:"29px",borderBottom:"0.5px solid #e8e8e8",paddingLeft:"16px"}
        const getCompanyType = (auth)=> {
            let type = []
            if(auth && auth.length > 1){
                auth.map((item,index) => {
                    type.push(<div style={index === auth.length -1 ? {paddingLeft:"16px"} : style}>{item.AuthType}</div>)
                })
            }else{
                type.push(<div style={{padding: "16px 16px"}}>{auth && auth[0]?.AuthType}</div>)
            }
            return type
        }
        const getCompanyName = (auth)=> {
            let name = []
            if(auth && auth.length > 1){
                auth.map((item,index) => {
                    name.push(<div style={index === auth.length -1 ? {paddingLeft:"16px"} : style}>{item.CompanyName}</div>)
                })
            }else{
                name.push(<div style={{padding: "16px 16px"}}>{auth && auth[0]?.CompanyName}</div>)
            }
            return name
        }
        return (
            <Card bordered={true} style={{paddingRight:20}}>
                <Breadcrumb style={{marginBottom:30}}>
                        <Breadcrumb.Item>
                          <a href="" onClick={()=>{
                              this.props.history.push("/PoliceCase")
                          }}>
                            公安案件
                          </a>
                        </Breadcrumb.Item>
                        <Breadcrumb.Item>详情</Breadcrumb.Item>
                </Breadcrumb>
                <div>
                    <div>订单：<span className='orderTitle'>{data.No || ''}</span><span className='orderTitle'>{data.Name || ''}</span>
                        <Select value={data.Status} onChange={this.handleChangeSelect} style={{width:100}}>
                        {
                            STATUS.map(item=> <Select.Option key={item.key} value={item.key}>{item.value}</Select.Option> )
                        }
                        </Select>
                    </div>
                    <Tabs defaultActiveKey="details">
                        <TabPane tab="详情" key="details">
                           <Row>
                                    <Col span={4} style={col4}>来源省份</Col>
                                    <Col span={8} style={col20}><OperationsOnNote value={data['Province']} name="Province" onChange={(params)=>this.ModifyPoliceCase(params)}/></Col>
                                    <Col span={4} style={col4}>执法部门</Col>
                                    <Col span={8} style={col20}><OperationsOnNote value={data['Department']} name="Department" onChange={(params)=>this.ModifyPoliceCase(params)}/></Col>
                            </Row>
                            <Row>
                                    <Col span={4} style={col4} >案件名称</Col>
                                    <Col span={8} style={col20}><OperationsOnNote value={data['Name']} name="Name" onChange={(params)=>this.ModifyPoliceCase(params)}/></Col>
                                    <Col span={4} style={col4}>案件类型</Col>
                                    <Col span={8} style={col20}>{Configs[data.Type]}</Col>
                            </Row>
                            <Row>
                                    <Col span={4} style={col4} >公司ID</Col>
                                    <Col span={8} style={col20}>{data.CompanyId}</Col>
                                    <Col span={4} style={col4}>关联案件数</Col>
                                    <Col span={8} style={col20}>{data.RelatedCasesCount}</Col>
                            </Row>
                            <Row>
                                    <Col span={4} style={col4} >所属BU</Col>
                                    <Col span={8} style={col20}>{data.BU}</Col>
                                    <Col span={4} style={col4}>客户等级</Col>
                                    <Col span={8} style={col20}>{data.Level}</Col>
                            </Row>
                            <Row>
                                    <Col span={4} style={col4} >公司名/用户名</Col>
                                    <Col span={8} style={col21}>{getCompanyName(data.Auth)}</Col>
                                    <Col span={4} style={col4}>认证类型</Col>
                                    <Col span={8} style={col21}>{getCompanyType(data.Auth)}</Col>
                            </Row>
                            <Row>
                                    <Col span={4} style={col4} >协查IP</Col>
                                    <Col span={8} style={col20}>{data.Ip}</Col>
                                    <Col span={4} style={col4}>IP归属地</Col>
                                    <Col span={8} style={col20}>{data.IpAera}</Col>
                            </Row>
                            <Row>
                                    <Col span={4} style={col4} >协查通知</Col>
                                    <Col span={8} style={col20}>{NotifiedSTATUS[data.Notified] || ''}</Col>
                                    <Col span={4} style={col4}>备注</Col>
                                    <Col span={8} style={col20}><OperationsOnNote value={data['Remark']} name="Remark" onChange={(params)=>this.ModifyPoliceCase(params)}/></Col>
                            </Row>
                            <Card title="资源列表" bordered={false}>
                                <Row>
                                    <Col span={4}>产品总计:（{data?.Resources?.length || 0}）</Col>
                                    <Col span={4}>数据统计:({getTotal(data.Resources)}GB)</Col>
                               </Row>
                                <Table
                                    loading={loading}
                                    rowKey={record => record.ResourceId}
                                    dataSource={data.Resources || []}
                                    columns={columns1}
                                />
                            </Card>
                            <Card title="更多信息" bordered={false}>
                                <Row>
                                    <Col span={4} style={col4} >开始时间</Col>
                                    <Col span={8} style={col20}>{timeFormat(data.CreateTime)}</Col>
                                    <Col span={4} style={col4}>结束时间</Col>
                                    <Col span={8} style={col20}>{timeFormat(data.UpdateTime)}</Col>
                                </Row>
                                <Row>
                                    <Col span={4} style={col4}>拷贝日期</Col>
                                    <Col span={20} style={col20}>{this.getCopyTime(data.CopyTime)}</Col>
                                </Row>
                                <Row>
                                    <Col span={4} style={col4}>耗时</Col>
                                    <Col span={20} style={col20}>{data.ConsumTime}</Col>
                                </Row>
                                <Row>
                                    <Col span={4} style={col4}>创建记录</Col>
                                    <Col span={20} style={col20}>{data.CreateRecord}</Col>
                                </Row>
                                <Row>
                                    <Col span={4} style={col4}>更新记录</Col>
                                    <Col span={20} style={col20}>{data.UpdatedRecord}</Col>
                                </Row>
                            </Card>
                        </TabPane>
                        <TabPane tab="ID关联案件" key="idCase">
                            <Card title = {"关联案件数:"+data?.RelatedCases?.length || 0} bordered={false}>
                            <Table
                                loading={loading}
                                rowKey={record => record.Id}
                                dataSource={data.RelatedCases || []}
                                columns={columns2}
                            />
                            </Card>
                        </TabPane>
                    </Tabs>
                </div>
            </Card>
        )
    }
}

export default PoliceCaseDetails;
