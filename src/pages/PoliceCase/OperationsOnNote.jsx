import React from 'react';
import PropTypes from 'prop-types';
import { Icon,DatePicker } from 'antd';
import moment from 'moment';

const IconStyle = {
    fontSize: 16,
    cursor: 'pointer',
    color: 'blue',
    margin: 5
}
const InputStyle = {
    width:300,
    height: '30px',
    padding: '0 4px',
    color: 'rgba(0,0,0,.95)',
    backgroundColor: '#fff',
    border: '1px solid #d9d9d9',
    borderRadius: 4,
    transition: 'all .3s'
};
class OperationsOnvalue extends React.Component {
    static propTypes = {
        value: PropTypes.object,
        name: PropTypes.object,
        onChange:PropTypes.func
    };
    constructor(props) {
        super(props);
        this.state = {
            value: this.props.value,
            action: 'show',
            name: this.props.name,
            maxLength: this.props.name === 'Remark' ? 60 : 30
        };
        this.valueInputRef = React.createRef();
    }
    changevalue = () => {
        const { name,value } = this.state;
        if(name === 'CopyTime'){
            this.setState(
                {
                    action: 'show',
                },
                () => {
                    this.props.onChange({name:name,value:value})
                }
            );
            return
        }
        this.setState(
            {
                action: 'show',
                value: this.valueInputRef.current.value
            },
            () => {
                this.props.onChange({name:name,value: this.state.value})
            }
        );
    };
  
    changeValue = e => {
        this.setState({
            value: e.target.value
        });
    };

    render() {
        let valueInit =  this.props.value
        let { value = valueInit, action,maxLength,name } = this.state;
        let Showvalue = () => {
            return (
                <div>
                    <span title={value}>
                        {name === 'CopyTime' ? (moment(value * 1000).format('YYYY-MM-DD HH:mm:ss')) :(value && value.length > maxLength ? value.substring(0, maxLength) + '...' : value)}
                    </span>
                    <Icon
                        type='edit'
                        twoToneColor="#52c41a"
                        onClick={() => this.setState({ action: 'edit' })}
                    />
                </div>
            );
        };
        let Editvalue = () => {
            return (
                <div style={{display:"flex"}}>
                    {
                       name === 'CopyTime' ?
                       <DatePicker
                        bordered={false}
                        style={{width:300}}
                        format="YYYY-MM-DD"
                        onChange= {(date)=>{
                            this.setState({
                                value:moment(date.format("YYYY-MM-DD")).unix()+"",
                            })
                        }}
                       />
                       : <input defaultValue={value} ref={this.valueInputRef} style={InputStyle}/>
                    }
                    <Icon
                        style={IconStyle}
                        twoToneColor="#52c41a"
                        type = "check-circle"
                        onClick={this.changevalue}
                    />
                </div>
            );
        };
        return <div>{action === 'show' ? <Showvalue /> : <Editvalue />}</div>;
    }
}

export default OperationsOnvalue;
