import React, { Component } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Breadcrumb,
  notification,
  Modal,
  AutoComplete,
  Tooltip,
  message,
  Popconfirm,
  Button,
  Card,
  Table,
  Badge,
} from "antd";
import ImageViewer from "../../components/ImageViewer";
import StaticFieldBoard from "../../components/StaticFieldBoard";
import moment from "moment";
import request, { newAuthApi } from "../../utils/request";
const { Option } = AutoComplete;
// 日志列表
const columns = [
  {
    title: "操作类型",
    dataIndex: "Type",
  },
  {
    title: "操作人",
    dataIndex: "Operator",
    key: "Operator",
  },
  {
    title: "执行结果",
    dataIndex: "ResultStatus",
    key: "ResultStatus",
    render: (result) =>
      result === "请求成功" ? (
        <Badge status="success" text="通过" />
      ) : (
        <Badge status="error" text="未通过" />
      ),
  },
  {
    title: "操作时间",
    dataIndex: "CreateTime",
    key: "CreateTime",
    render: (val) => (
      <span>
        {val ? moment(val * 1000).format("YYYY-MM-DD HH:mm:ss") : "暂无"}
      </span>
    ),
  },
  {
    title: "备注",
    dataIndex: "Remark",
  },
];

class RealNameReexamDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAction: false,
      CompanyId: this.props?.location?.state?.CompanyId,
      FlowId: this.props?.match?.params?.FlowId,
      Id: this.props?.match?.params?.Id,
      showRejectForm: false,
      rejectReason: "",
      userInfo: {},
      BestImage: "",
      logs: [],
      loading: false,
      VerbalTricks: [],
    };
  }
  componentDidMount() {
    this.DescribeAuthLog();
    this.DescribeManualAuthInfo();
    this.DescribeVerbalTricks();
  }
  rejectForm = () => {
    this.setState({
      showRejectForm: true,
    });
  };
  inputRejectReason(e) {
    this.setState({
      rejectReason: e,
    });
  }
  hideRejectForm() {
    this.setState({
      showRejectForm: false,
    });
  }
  async audit(passed) {
    // 设置loading，关闭驳回对话框
    this.setState({
      showRejectForm: false,
      loading: true,
    });
    const { CompanyId, FlowId } = this.state;
    const UpdateIdentityStatus = await newAuthApi(
      "LegalCompanyAuthAuditWithManual",
      {
        CompanyId: parseInt(CompanyId),
        AuditResult: passed ? "Yes" : "No",
        RejectReason: this.state.rejectReason,
        FlowId: FlowId,
      }
    );
    if (UpdateIdentityStatus.RetCode === 0) {
      this.setState({
        showAction: false,
        loading: false,
      });
      message.success(passed ? "审核通过" : "完成驳回", 3, () => {
        window.location.reload();
      });
    }
  }
  DescribeAuthLog() {
    let self = this;
    self.setState({ loading: true });
    const FlowId = self.state.FlowId;
    if (!FlowId) {
      message.error("FlowId不存在，请返回主页重新进入详情页");
      return;
    }

    newAuthApi("DescribeAuthLog", { FlowId: FlowId })
      .then((resp) => {
        if (resp.RetCode !== 0) {
          notification["error"]({
            message: "DescribeAuthLog获取失败",
            description: resp.message || resp.Message,
          });
          return;
        }
        self.setState({
          logs: resp.DataSet,
          loading: false,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "DescribeAuthLog获取列表失败",
          description: err.message || "内部错误",
        });
        // 清空列表
        self.setState({
          loading: false,
          logs: [],
        });
        return;
      });
  }
  GetUS3File = (val) => {
    if (!val) {
      message.error("文件不存在！");
      return;
    }
    this.setState({ loading: true });
    newAuthApi("GetUS3File", { FileName: val, Source: "AUTH" })
      .then((resp) => {
        if (resp.RetCode !== 0) {
          notification["error"]({
            message: "GetUS3File获取失败",
            description: resp.message || resp.Message,
          });
          return;
        }
        this.setState(
          {
            loading: false,
          },
          () => {
            window.open(resp.FileURL);
          }
        );
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "GetUS3File获取失败",
          description: err.message || "内部错误",
        });
        // 清空列表
        this.setState({
          loading: false,
        });
        return;
      });
  };

  DescribeManualAuthInfo() {
    this.setState({ loading: true });
    const { FlowId } = this.state;
    if (!FlowId) {
      message.error("FlowId不存在");
      return;
    }
    const env = require("../../../envConfigs/env").default;
    newAuthApi("DescribeCompanyKYCFlowList", {
      FlowId: FlowId,
      ...(env === "local" && { staff_name_en: "local_user" }),
    })
      .then((resp) => {
        if (resp.RetCode !== 0) {
          notification["error"]({
            message: "DescribeCompanyKYCFlowList获取失败",
            description: resp.message || resp.Message,
          });
          return;
        }
        this.setState({
          userInfo: resp.DataSet[0],
          CompanyId: resp.DataSet[0].CompanyId,
          showAction: resp.DataSet[0].FlowStatus === "LicenseAuditPending",
          loading: false,
        });
        resp.DataSet[0]?.LegalPersonLivenessPhoto &&
          this.getPicture(
            resp.DataSet[0]?.LegalPersonLivenessPhoto,
            "LegalPersonImg"
          );
        resp.DataSet[0].BusinessLicenseFile &&
          this.getPicture(
            resp.DataSet[0].BusinessLicenseFile,
            "BusinessLicenseFile"
          );
        resp.DataSet[0].ReceiptFile &&
          this.getPicture(resp.DataSet[0].ReceiptFile, "ReceiptFile");
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "DescribeCompanyKYCFlowList获取失败",
          description: err.message || "内部错误",
        });
        // 清空列表
        this.setState({
          loading: false,
        });
        return;
      });
  }
  DescribeVerbalTricks() {
    request("DescribeVerbalTricks", {
      VerbalType: ["Common", "Person", "CompanyNew"],
    })
      .then((resp) => {
        if (resp.RetCode === 0) {
          this.setState({ VerbalTricks: resp.VerbalTricks });
        } else {
          message.error("获取列表失败!");
        }
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
      });
  }
  getPicture(val, param) {
    if (!val) {
      message.error("文件不存在！");
      return;
    }
    newAuthApi("GetUS3File", { FileName: val, Source: "AUTH" })
      .then((resp) => {
        if (resp.RetCode !== 0) {
          notification["error"]({
            message: "GetUS3File获取失败",
            description: resp.message || resp.Message,
          });
          return;
        }
        this.setState({
          [param]: resp.FileURL,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "GetUS3File获取失败",
          description: err.message || "内部错误",
        });
        return;
      });
  }
  ConfirmRechargeForCompanyKYC = () => {
    this.setState({ loading: true });
    const { FlowId, CompanyId } = this.state;
    if (!FlowId) {
      message.error("FlowId不存在");
      return;
    }
    let that = this;
    const env = require("../../../envConfigs/env").default;
    newAuthApi("ConfirmRechargeForCompanyKYC", {
      FlowId,
      CompanyId,
      ...(env === "local" && { staff_name_en: "local_user" }),
    })
      .then((resp) => {
        that.setState({
          loading: false,
        });
        if (resp.RetCode !== 0) {
          notification["error"]({
            message: resp.message || resp.Message,
          });
          return;
        }
        that.DescribeAuthLog();
      })
      .catch((err) => {
        // 清空列表
        that.setState({
          loading: false,
        });
        // 报错
        notification["error"]({
          message: "ConfirmRechargeForCompanyKYC获取失败",
          description: err.message || "内部错误",
        });

        return;
      });
  };
  render() {
    const {
      loading,
      BusinessLicenseFile,
      ReceiptFile,
      LegalPersonImg,
      userInfo,
      logs,
      showAction,
    } = this.state;
    const CompanyKYCTypeEnum = {
      CompanyRemit: 1, // 公司汇款给用户
      UserLegalPersonLive: 2, // 用户法人活体
      UserRecharge: 3, // 用户充值给公司
    }; // 审核信息模板
    const userInfoData = {
      企业名称: userInfo?.CompanyName,
      信用代码: userInfo?.CompanyCertificateNumber,
      ...(CompanyKYCTypeEnum[userInfo?.CompanyKYCType] === 2 && {
        法人姓名: userInfo?.IdCardName,
      }),
    };
    // 审核信息模板
    const OCRInfoData = {
      "企业名称(OCR识别)": userInfo?.OCRResult?.CompanyName || "-",
      "信用代码(OCR识别)": userInfo?.OCRResult?.CompanyCertificateNumber || "-",
      ...(CompanyKYCTypeEnum[userInfo?.CompanyKYCType] === 2 && {
        "法人姓名(OCR识别)": userInfo?.OCRResult?.IdCardName,
      }),
    };
    const baseInfoData = {
      通信地址: userInfo?.Address,
      财务账号类型: userInfo?.Account?.AccountType === 1 ? "支付宝" : "银行卡",
      财务账号: userInfo?.Account?.AccountNo,
    };
    let showLeagelImgStatus = [
      "LicenseAuditPending",
      "LicenseAuditPass",
      "LicenseAuditReject",
      "ProcessCompleted",
    ];
    const showLegalImg =
      userInfo.CompanyKYCType === "UserLegalPersonLive" &&
      showLeagelImgStatus.includes(userInfo.FlowStatus);
    const userInfoCard = (
      <div>
        <StaticFieldBoard data={userInfoData} />
        <StaticFieldBoard data={OCRInfoData} />
        {CompanyKYCTypeEnum[userInfo?.CompanyKYCType] === 1 ? (
          <StaticFieldBoard data={baseInfoData} />
        ) : (
          ""
        )}
        <div style={{ margin: 20, display: "flex" }}>
          {BusinessLicenseFile && (
            <ImageViewer
              src={BusinessLicenseFile}
              name="营业执照图片"
              style={{ marginRight: 10 }}
            />
          )}
          {showLegalImg && LegalPersonImg ? (
            <ImageViewer
              src={LegalPersonImg}
              name="法人活体照"
              style={{ marginRight: 10 }}
            />
          ) : (
            ""
          )}
          {CompanyKYCTypeEnum[userInfo?.CompanyKYCType] === 1 && ReceiptFile ? (
            <ImageViewer
              src={ReceiptFile}
              name="打款回执图片"
              style={{ marginRight: 10 }}
            />
          ) : (
            ""
          )}
        </div>
      </div>
    );

    let actionForm = (
      <Popconfirm
        title="确定通过审核？"
        onConfirm={() => {
          this.audit(true);
        }}
        okText="通过"
        cancelText="取消"
        placement="bottom"
      >
        <Button type="primary" style={{ marginRight: 20 }}>
          通过
        </Button>
      </Popconfirm>
    );

    if (!showAction) actionForm = "";
    const VerbalTricks = this.state.VerbalTricks;
    const dataSource = Array.from(
      new Set(VerbalTricks.map((item) => item.VerbalTrick))
    );
    return (
      <div>
        <Breadcrumb style={{ marginBottom: 10 }}>
          <Breadcrumb.Item>
            <Link to="/">主页</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={`/RealnameReexam?CompanyId=${userInfo.CompanyId}`}>
              审核列表
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>详情</Breadcrumb.Item>
        </Breadcrumb>
        <Card
          title="用户信息"
          style={{ marginBottom: 24 }}
          bordered={false}
          loading={loading}
        >
          {userInfoCard}
          {showAction ? actionForm : null}
          {showAction ? (
            <Button type="danger" onClick={this.rejectForm}>
              不通过
            </Button>
          ) : null}
          <div>
            {userInfo.FlowStatus === "待确定充值信息" &&
              userInfo.CompanyKYCType === "UserRecharge" && (
                <Button
                  type="primary"
                  onClick={this.ConfirmRechargeForCompanyKYC}
                >
                  打款信息验证
                </Button>
              )}
          </div>
        </Card>
        <Card title="审核日志" bordered={false}>
          <Table
            pagination={false}
            loading={loading}
            dataSource={logs}
            columns={columns}
          />
        </Card>
        <Modal
          title="复审不通过"
          visible={this.state.showRejectForm}
          onOk={() => {
            this.audit(false);
          }}
          onCancel={() => {
            this.hideRejectForm();
          }}
          width={800}
        >
          <AutoComplete
            style={{ width: "100%" }}
            dropdownMatchSelectWidth={false}
            dropdownStyle={{ width: 500 }}
            dataSource={dataSource.map((item) => (
              <Option key={item} value={item} title={item}>
                {item}
              </Option>
            ))}
            onChange={(e) => {
              this.inputRejectReason(e);
            }}
            placeholder="请填写理由"
            filterOption={(inputValue, option) =>
              option.props.children
                .toUpperCase()
                .indexOf(inputValue.toUpperCase()) !== -1
            }
          />
          <Tooltip title={this.state.rejectReason}></Tooltip>
        </Modal>
      </div>
    );
  }
}

export default RealNameReexamDetail;
