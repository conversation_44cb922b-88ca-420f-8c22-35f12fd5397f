import React from 'react';
import { notification, Modal,Popconfirm, Row, Col, Card, Form, Button, Select, Table, message, Tooltip } from 'antd';
import { useState,useEffect } from 'react';
import AddCustomFormWrapper from './AddCustomForm';
import request from '../../utils/request'
const FormItem = Form.Item
const { Option } = Select
const TYPE = {
    Common: "通用",
    Person: "个人认证",
    Company: "企业认证",
    Tunnel: "跨境报备审核",
    APP:"活体认证",
    CompanyNew: '企业认证复审'
}
const AuditSet = () => {
    const [data, setData] = useState([])
    const [visible, setVisible] = useState(false)
    const [type, setType] = useState(null)
    const searchCard = () => {
        return <Form layout="inline" className="ant-advanced-search-form">
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col md={10} sm={24}>
                    <FormItem label="审核类型">
                        <Select value={type} onChange={(v) => setType(v)}>
                            {Object.keys(TYPE).map(item => <Option key={item} value={item}>{TYPE[item]}</Option>)}
                        </Select>
                    </FormItem>
                </Col>
                <Col md={10} sm={24}>
                    <Button type='primary' style={{ marginRight: "20px" }} onClick={handleSearch}>查询</Button>
                    <Button onClick={onResetSearch}>重置</Button>
                </Col>
        </Row></Form>
    }
    const deleteTricks = (row)=> {
        request('DelVerbalTrick', {VerbalTrick:row.VerbalTrick,VerbalType:row.VerbalType})
        .then(resp => {
          if (resp.RetCode === 0) {
            message.success("删除成功!")
            getList({ VerbalType: type ? [type] : [] })
          }else{
            message.error('删除失败!')
          }
        })
        .catch(err => {
          // 报错
          notification['error']({
            message: '发送失败',
            description: err.message || '内部错误'
          })
        })
    }
    const columns = [
        {
            title: '审核类型',
            dataIndex: 'VerbalType',
            render:(val)=>TYPE[val]
        },
        {
            title: '自定义短信',
            dataIndex: 'VerbalTrick',
            render: (val) => <Tooltip title={val}>{val && val.length > 50 ? val.substring(0, 50) + '...' : val}</Tooltip>
        },
        {
            title: '操作',
            dataIndex: 'VerbalType',
            render:(val,row)=><Popconfirm title="高危操作，确定删除？" onConfirm={()=>deleteTricks(row)} okText="Yes" cancelText="No">
            <Button type='warning'>删除</Button></Popconfirm>
        },
    ]
    useEffect(() => {
        getList({VerbalType:[]})
    }, [])
    const getList = (param)=>{
        request('DescribeVerbalTricks', param)
        .then(resp => {
          if (resp.RetCode === 0) {
            setData(resp.VerbalTricks)
          }else{
            message.error('获取列表失败!')
          }
        })
        .catch(err => {
          // 报错
          notification['error']({
            message: '发送失败',
            description: err.message || '内部错误'
          })
        })
    }
    const handleSearch = ()=> {
      getList({VerbalType:type ?[type] : []})
    }
    const onResetSearch = ()=> {
          setType(null)
          getList({VerbalType:[]})
    }
    const addSubmit = (values)=>{
        setVisible(false)
        request('AddVerbalTrick', {VerbalTrick:values.VerbalTrick,VerbalType:values.VerbalType})
        .then(resp => {
          if (resp.RetCode === 0) {
            message.success("添加成功!")
            getList({ VerbalType: type ? [type] : [] })
          }else{
            message.error('添加失败!')
          }
        })
        .catch(err => {
          // 报错
          notification['error']({
            message: '发送失败',
            description: err.message || '内部错误'
          })
        })
    }
    const onCancel = ()=>{
        setVisible(false)
    }
    return (<Card>
        {searchCard()}
        <Button type='primary' onClick={() => setVisible(true)}>新增</Button>
        <Modal title="添加自定义短语" footer={null} visible={visible} onCancel={() => setVisible(false)}>
            <AddCustomFormWrapper handleSubmit = {addSubmit} onCancel={onCancel} TYPE ={TYPE}/>
        </Modal>
        <Table
            dataSource={data}
            columns={columns}
        /></Card>)
}

export default AuditSet
