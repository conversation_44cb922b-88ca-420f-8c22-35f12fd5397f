import React, { Component } from 'react'
import RealnameTable from '../../components/RealnameTable'
import './List.css'
import { notification, Row, Col, DatePicker, Card, Form, Input, Icon, Button,Select,InputNumber } from 'antd';

import request from '../../utils/request'
import moment from 'moment';

const { RangePicker } = DatePicker;
const FormItem = Form.Item
const { Option } = Select
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',')
class List extends Component {
	constructor(props) {
		super(props)
		this.state = {
			expandForm: false, // 是否展开
			formValues: { // 查询表单初始值
				AuditState: 'PENDING',
				AuthType: 1
			},
			list: [], // 列表初始值
			pagination: {
				current: 1,
				pageSize: 10,
				total: 0
			},
			loading: false
		}
	}

	// 查询列表
	fetch(options = {}) {
		// 统一设置分页或者报错
		let self = this
		if (options.CompanyId === null || options.CompanyId === undefined) {
			delete options.CompanyId
		}
		if ( options.AuthType !== null && options.AuthType !== undefined ) {
			options.AuthType = parseInt(options.AuthType, 10)
		} else {
			delete options.AuthType
		}
		if (typeof(options.time) === 'object') {
			delete options.AuditState
			// 格式转换
			options.CreatedBegin =parseInt(options.time[0].startOf('day').format("X"), 10)
			options.CreatedEnd =parseInt(options.time[1].endOf('day').format("X"), 10)

			// 查一天的时间实际压力从0点到23点，做startOf与endOf处理
			if (options.CreatedBegin === options.CreatedEnd) {
				options.CreatedBegin = options.time[0].startOf('day').format("X")
				options.CreatedEnd = options.time[1].endOf('day').format("X")
			}

			delete options.time
		} else {
			delete options.time
		}
		// 初始化options
		options.Offset = options.Offset || 0
		options.Limit = options.Limit || 20

		// Loading Modal
		self.setState({ loading: true })

		request('GetUserAuthInfo', options)
			.then(resp => {
				self.setState({
					list: resp.AuthInfo || [],
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.TotalCount || 0
					},
					loading: false
				})
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: '获取列表失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				self.setState({
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				})
				return
			})
	}

	// 挂载前查询
	componentDidMount() {
		const {state} = this.props.location
		const formValues = state && state['formValues']?state.formValues:{}
		const pagination = state && state['pagination']?state.pagination:{current:1}
		this.setState(prevState=>{
			return {
				formValues: {
					...prevState.formValues,
					...formValues
				},
				pagination: {
					...prevState.pagination,
					...pagination
				}
			}
		},()=>{
			this.fetch({ AuditState: 'PENDING',...formValues,Offset:(pagination.current-1)*20 })
			this.getChannelInfo()
		})
	}
	getChannelInfo = () => {
		request('GetChannelInfo')
			.then(resp => {
				if(resp.ChannelInfo) {
					let channelList = {}
					resp.ChannelInfo.forEach(channel => {
						channelList[channel.ChannelID] = channel.Title
					})
					this.setState({
						channelList
					})
				}
			})
			.catch(() => {
				// 清空列表
				this.setState({
					channelList: {}
				})
				return
			})
	}
	// 处理分页
	handleRealnameTableChange(pagination, filtersArg) {
		const { formValues } = this.state
		console.log(pagination,filtersArg)
		const filters = Object.keys(filtersArg).reduce((obj, key) => {
			const newObj = { ...obj }
			newObj[key] = getValue(filtersArg[key])
			return newObj
		}, {})

		const params = {
			...formValues,
			...filters,
		}

		params.Offset = (pagination.current - 1) * pagination.pageSize
		params.Limit = pagination.pageSize

		this.fetch(params)
	}

	// 重置搜索框
	handleFormReset() {
		const { form } = this.props
		let self = this
		form.resetFields()

		this.setState({
			formValues: {
				AuditState: 'PENDING',
        AuthType: 1,
				Offset: 0,
				Limit: 20
			},
		}, () => {
			self.fetch(self.state.formValues)
		})
	}

	// 展开、收起搜索框
	toggleForm() {
		this.setState({
			expandForm: !this.state.expandForm,
		})
	}

	// 搜索
	handleSearch(e) {
		e.preventDefault()

		const { form } = this.props

		form.validateFields((err, fieldsValue) => {
			if (err) return

			const values = {
				...fieldsValue,
			}
			if(values.ChannelId) {
				values.ChannelId = parseInt(values.ChannelId, 10)
			}
			this.setState({
				formValues: values,
			})
			this.fetch(values)
		})
	}

	renderAdvancedForm() {
		const { getFieldDecorator } = this.props.form
		const { formValues, expandForm, channelList={} } = this.state
		return (
			<Form onSubmit={this.handleSearch.bind(this)} layout="inline" className="ant-advanced-search-form">
				<Row gutter={{ md: 8, lg: 24, xl: 48 }}>
					<Col md={8} sm={24}>
						<FormItem label="状态">
							{getFieldDecorator('AuditState', {
								initialValue: formValues.AuditState
							})(
								<Select style={{ width: '100%' }}>
									<Option value="ALL">全部</Option>
									<Option value="PENDING">待审核</Option>
								</Select>
							)}
						</FormItem>
					</Col>
					<Col md={8} sm={24}>
						<FormItem label="公司Id">
							{getFieldDecorator('CompanyId', {
								initialValue: formValues.CompanyId
							})(
								<InputNumber style={{ width: '100%' }} />
							)}
						</FormItem>
					</Col>
					{
						expandForm?
						<span>
						<Col md={8} sm={24}>
							<FormItem label="账号">
								{getFieldDecorator('UserEmail', {
									initialValue: formValues.UserEmail
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="公司名">
								{getFieldDecorator('CompanyName', {
									initialValue: formValues.CompanyName
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="用户名">
								{getFieldDecorator('UserName', {
									initialValue: formValues.UserName
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
						<FormItem label="用户类型">
								{getFieldDecorator('AuthType', {
									initialValue: formValues.AuthType
								})(
									<Select style={{ width: '100%' }}>
										<Option value={1}>个人</Option>
										<Option value={0}>企业</Option>
									</Select>
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
						<FormItem label="时间">
							{getFieldDecorator('time')(
								<RangePicker
								style={{width:'100%'}}
								ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
								showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
								format="YYYYMMMMDo"
								/>)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="渠道Id">
								{getFieldDecorator('ChannelId', {
									initialValue: formValues.ChannelId
								})(
									<Select style={{ width: '100%' }} showSearch>
										{
											Object.keys(channelList).map(channel => <Option key={channel} value={channel}>{channelList[channel]}</Option>)
										}
									</Select>
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="证件号码">
								{getFieldDecorator('IdentityNo', {
									initialValue: formValues.IdentityNo
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
					</span>
					:''
					}
					<Col md={8} sm={24}>
						<span style={{ float: 'right', marginBottom: 24 }}>
							<Button type="primary" htmlType="submit">查询</Button>
							<Button style={{ marginLeft: 8 }} onClick={this.handleFormReset.bind(this)}>重置</Button>
              {expandForm?<a style={{ marginLeft: 8 }} onClick={this.toggleForm.bind(this)}>
								收起 <Icon type="up" />
							</a>:<a style={{ marginLeft: 8 }} onClick={this.toggleForm.bind(this)}>
								展开 <Icon type="down" />
							</a>}

						</span>
					</Col>
				</Row>
			</Form>
		)
	}

	render() {
		const { loading, list, pagination, formValues, channelList } = this.state
		return (
			<Card bordered={false}>
				<div>
					<div>
						{this.renderAdvancedForm()}
					</div>
					<RealnameTable
						loading={loading}
						data={list}
						channelList={channelList}
						pagination={{current:pagination.current}}
						onChange={this.handleRealnameTableChange.bind(this)}
						formValues={formValues}
					/>
				</div>
			</Card>
		)
	}
}

const ListForm = Form.create()(List)

export default ListForm
