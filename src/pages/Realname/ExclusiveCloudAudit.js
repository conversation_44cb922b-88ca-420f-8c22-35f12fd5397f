import React, { Component } from "react";
import ExclusiveCloudTable from "../../components/ExclusiveCloudTable";
import "./List.css";
import {
  notification,
  Row,
  Col,
  DatePicker,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Select,
  InputNumber,
} from "antd";
import moment from "moment";
import request from "../../utils/request";

const { RangePicker } = DatePicker;
const FormItem = Form.Item;
const { Option } = Select;
const getValue = (obj) =>
  Object.keys(obj)
    .map((key) => obj[key])
    .join(",");
class ExclusiveCloudAudit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      expandForm: false, // 是否展开
      formValues: {
        // 查询表单初始值
        AuthState: 1,
      },
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      loading: false,
    };
  }

  // 查询列表
  fetch = (options) => {
    // 统一设置分页或者报错
    let self = this;
    self.setState({loading: true});
    if (!options.AuthState) {
      delete options.AuthState;
    }
    if (!options.CompanyId) {
      delete options.CompanyId;
    }
    if (!options.CompanyName) {
      delete options.CompanyName;
    }
    if (!options.IdentityNo) {
      delete options.IdentityNo;
    }
    if (!options.TargetEnv) {
      delete options.TargetEnv;
    }
    if (typeof options.time === "object") {
      // 格式转换
      options.StartTime = parseInt(options.time[0].format("X"), 10);
      options.EndTime = parseInt(options.time[1].format("X"), 10);

      // 查一天的时间实际压力从0点到23点，做startOf与endOf处理
      if (options.StartTime === options.EndTime) {
        options.StartTime = options.time[0].startOf("day").format("X");
        options.EndTime = options.time[1].endOf("day").format("X");
      }
      delete options.time;
    } else {
      delete options.time;
    }
    let { current, pageSize } = this.state.pagination;
    // 初始化options
    options.Offset = (current - 1) * pageSize;
    options.Limit = pageSize;
    request("GetPrivateAuditList", options)
      .then((resp) => {
        if (resp.RetCode === 0) {
          self.setState({
            list: resp.Data || [],
            pagination: {
              current: current,
              pageSize: pageSize,
              total: resp.TotalCount,
            },
            loading: false,
          });
          return;
        }
        notification["error"]({
          message: "获取列表失败",
          description: resp.Message || "内部错误",
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "获取列表失败",
          description: err.message || "内部错误",
        });
        // 清空列表
        self.setState({
          loading: false,
          list: [],
          pagination: {
            current: 1,
            pageSize: 20,
            total: 0,
          },
        });
        return;
      });
  };
  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  };
  // 挂载前查询
  componentDidMount() {
    const { state } = this.props.location;
    const formValues = state && state["formValues"] ? state.formValues : {};
    this.setState(
      (prevState) => {
        return {
          formValues: {
            ...prevState.formValues,
            ...formValues,
          },
        };
      },
      () => {
        this.fetch({ AuthState: 1, ...formValues });
      }
    );
  }
  // 处理分页
  handleRealnameTableChange = (pagination, filtersArg) => {
    const { formValues } = this.state;
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      const newObj = { ...obj };
      newObj[key] = getValue(filtersArg[key]);
      return newObj;
    }, {});

    const params = {
      ...formValues,
      ...filters,
    };
    this.setState(
      {
        pagination: {
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
        },
      },
      () => {
        this.fetch(params);
      }
    );
  };

  // 重置搜索框
  handleFormReset = () => {
    this.props.form.resetFields()
		this.setState({
      formValues: {
        AuthState: 1
      },
      pagination: {
				current: 1,
				pageSize: 10,
				total: 0
			}
    },()=>{
      this.fetch(this.state.formValues)
    })
  };

  // 搜索
  handleSearch = (e) => {
    e.preventDefault();

    const { form } = this.props;

    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
      };
      this.setState({
        formValues: values,
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0
        }
      },()=>{
        this.fetch(values);
      });

    });
  };

  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    const { formValues, expandForm } = this.state;
    return (
      <Form
        onSubmit={this.handleSearch}
        layout="inline"
        className="ant-advanced-search-form"
      >
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col md={8} sm={24}>
            <FormItem label="状态">
              {getFieldDecorator("AuthState", {
                initialValue: 1,
              })(
                <Select style={{ width: "100%" }}>
                  <Option value={0}>全部</Option>
                  <Option value={1}>法务审核中</Option>
                </Select>
              )}
            </FormItem>
          </Col>
          <Col md={8} sm={24}>
            <FormItem label="公司Id">
              {getFieldDecorator("CompanyId", {
                initialValue: formValues.CompanyId,
              })(<InputNumber style={{ width: "100%" }} />)}
            </FormItem>
          </Col>
          {expandForm ? (
            <span>
              <Col md={8} sm={24}>
                <FormItem label="公司名">
                  {getFieldDecorator("CompanyName", {
                    initialValue: formValues.CompanyName,
                  })(<Input style={{ width: "100%" }} />)}
                </FormItem>
              </Col>
              <Col md={8} sm={24}>
                <FormItem label="证件号码">
                  {getFieldDecorator("IdentityNo", {
                    initialValue: formValues.IdentityNo,
                  })(<Input style={{ width: "100%" }} />)}
                </FormItem>
              </Col>
              <Col md={8} sm={24}>
                <FormItem label="时间">
                  {getFieldDecorator("time")(
                    <RangePicker
                      style={{ width: "100%" }}
                      ranges={{
                        Today: [moment().startOf("day"), moment().endOf("day")],
                        "This Month": [
                          moment().startOf("month"),
                          moment().endOf("month"),
                        ],
                      }}
                      showTime={{
                        defaultValue: [
                          moment("00:00:00", "HH:mm:ss"),
                          moment("23:59:59", "HH:mm:ss"),
                        ],
                      }}
                      format="YYYYMMMMDo"
                    />
                  )}
                </FormItem>
              </Col>
              <Col md={8} sm={24}>
                <FormItem label="海外站">
                  {getFieldDecorator("TargetEnv", {
                    initialValue: "",
                  })(
                    <Select style={{ width: "100%" }}>
                      <Option value={""}>国内</Option>
                      <Option value={"global"}>海外站</Option>
                    </Select>
                  )}
                </FormItem>
              </Col>
            </span>
          ) : (
            ""
          )}
          <Col md={8} sm={24}>
            <span style={{ float: "right", marginBottom: 24 }}>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
              <Button style={{ marginLeft: 8 }} onClick={this.handleFormReset}>
                重置
              </Button>
              {expandForm ? (
                <a style={{ marginLeft: 8 }} onClick={this.toggleForm}>
                  收起 <Icon type="up" />
                </a>
              ) : (
                <a style={{ marginLeft: 8 }} onClick={this.toggleForm}>
                  展开 <Icon type="down" />
                </a>
              )}
            </span>
          </Col>
        </Row>
      </Form>
    );
  }

  render() {
    const { loading, list, pagination, formValues } = this.state;
    return (
      <Card bordered={false}>
        <div>
          <div>{this.renderAdvancedForm()}</div>
          <ExclusiveCloudTable
            loading={loading}
            data={list}
            pagination={pagination}
            onChange={this.handleRealnameTableChange}
            formValues={formValues}
          />
        </div>
      </Card>
    );
  }
}

const ExclusiveCloudAuditWrapper = Form.create()(ExclusiveCloudAudit);

export default ExclusiveCloudAuditWrapper;
