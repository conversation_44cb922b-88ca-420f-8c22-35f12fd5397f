import React, { Component } from 'react'
import CrossReportAuditTable from '../../components/CrossReportAuditTable'
import './List.css'
import { notification, Row, Col, Card, Form, Input, Icon, Button,Select,InputNumber, message } from 'antd';
import request from '../../utils/request'
const FormItem = Form.Item
const { Option } = Select
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',')
const ApplyStatusEnum = {
    0: "编辑中",
    1: "审核中",
    2: "审核通过",
    3: "审核驳回",
    4: "已过期",
    5: "已作废"
}
class CrossReportAudit extends Component {
	constructor(props) {
		super(props)
		this.state = {
			expandForm: false, // 是否展开
			formValues: { // 查询表单初始值
				Status:"1"
			},
			list: [], // 列表初始值
			pagination: {
				current: 1,
				pageSize: 10,
				total: 0
			},
			loading: false
		}
	}

	// 查询列表
	fetch = (options)=> {
		try{
      this.setState({loading: true});
			if(Object.prototype.hasOwnProperty.call(options,'Status')){
				if(options.Status === '1'){
					options['Status'] = parseInt(options.Status)
				}else{
					delete options.Status
				}
			}
			if(!options.CompanyId){
				delete options.CompanyId
			}
			if(!options.ManagerName){
				delete options.ManagerName
			}
			if(!options.CompanyName){
				delete options.CompanyName
			}
			if(!options.CompanyCode){
				delete options.CompanyCode
			}
			if(!options.ManagerLicenseId){
				delete options.ManagerLicenseId
			}
			// 统一设置分页或者报错
			let self = this
			let { current,pageSize} = this.state.pagination
			// 初始化options
			options.Offset = (current -1) * pageSize
			options.Limit = pageSize
			request('GetCrossBorderApplyList', options)
				.then(resp => {
					self.setState({
						list: resp.Rows	 || [],
						pagination: {
							current: current,
							pageSize: pageSize,
							total: resp.TotalCount
						},
						loading: false
					})
				})
				.catch(err => {
					// 报错
					notification['error']({
						message: '获取列表失败',
						description: err.message || '内部错误'
					})
					// 清空列表
					self.setState({
						loading: false,
						list: [],
						pagination: {
							current: 1,
							pageSize: 20,
							total: 0
						}
					})
					return
				})
		}catch(e){
			console.log(e)
		}
	}
	// 展开、收起搜索框
	toggleForm =()=> {
		this.setState({
			expandForm: !this.state.expandForm,
		})
	}
	// 挂载前查询
	componentDidMount() {
		this.fetch(this.state.formValues)
	}
	// 处理分页
	handleRealnameTableChange = (pagination, filtersArg)=> {
		const { formValues } = this.state
		console.log(pagination,filtersArg)
		const filters = Object.keys(filtersArg).reduce((obj, key) => {
			const newObj = { ...obj }
			newObj[key] = getValue(filtersArg[key])
			return newObj
		}, {})

		const params = {
			...formValues,
			...filters,
		}
		this.setState({
			pagination:{
				current:pagination.current,
				pageSize:pagination.pageSize,
				total:pagination.total
			}
		},()=>{
			this.fetch(params)
		})

	}

	// 重置搜索框
	handleFormReset = ()=> {
    this.props.form.resetFields()
		this.setState({
      formValues: {
        Status:"1"
      },
      pagination: {
				current: 1,
				pageSize: 10,
				total: 0
			}
    },()=>{
      this.fetch(this.state.formValues)
    })
	}

	// 搜索
	handleSearch = (e)=> {
    let pagination = this.state.pagination
		e.preventDefault()
		const { form } = this.props
		let self = this
		form.validateFields((err, fieldsValue) => {
			if (err) {
				message.error(err)
				return
			}
			const values = {
				...fieldsValue
			}

			this.setState({
				formValues: values,
        pagination:{
          current:1,
          pageSize:pagination.pageSize,
          total:pagination.total
        }
			},()=>{
        self.fetch(values)
      })

		})
	}

	renderAdvancedForm() {
		const { getFieldDecorator } = this.props.form
		const { formValues, expandForm, } = this.state
		return (
			<Form onSubmit={this.handleSearch} layout="inline" className="ant-advanced-search-form">
				<Row gutter={{ md: 8, lg: 24, xl: 48 }}>
					<Col md={8} sm={24}>
						<FormItem label="状态">
							{getFieldDecorator('Status', {
								initialValue: "1"
							})(
								<Select style={{ width: '100%' }}>
									<Option value="0">全部</Option>
									<Option value="1">审核中</Option>
								</Select>
							)}
						</FormItem>
					</Col>
					<Col md={8} sm={24}>
						<FormItem label="公司Id">
							{getFieldDecorator('CompanyId', {
								initialValue: formValues.CompanyId
							})(
								<InputNumber style={{ width: '100%' }} />
							)}
						</FormItem>
					</Col>
					{
						expandForm?
						<span>
						<Col md={8} sm={24}>
						<FormItem label="经办人">
								{getFieldDecorator('ManagerName', {
									initialValue: formValues.ManagerName
								})(
									<Input style={{width:"100%"}}/>
								)}
						</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="企业名称">
								{getFieldDecorator('CompanyName', {
									initialValue: formValues.CompanyName
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="信用代码">
								{getFieldDecorator('CompanyCode', {
									initialValue: formValues.CompanyCode
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
						<Col md={8} sm={24}>
							<FormItem label="身份证号">
								{getFieldDecorator('ManagerLicenseId', {
									initialValue: formValues.ManagerLicenseId
								})(
									<Input style={{ width: '100%' }} />
								)}
							</FormItem>
						</Col>
					</span>
					:''
					}
					<Col md={8} sm={24}>
						<span style={{ float: 'right', marginBottom: 24 }}>
							<Button type="primary" htmlType="submit">查询</Button>
							<Button style={{ marginLeft: 8 }} onClick={this.handleFormReset}>重置</Button>
              {expandForm?<a style={{ marginLeft: 8 }} onClick={this.toggleForm}>
								收起 <Icon type="up" />
							</a>:<a style={{ marginLeft: 8 }} onClick={this.toggleForm}>
								展开 <Icon type="down" />
							</a>}
						</span>
					</Col>
				</Row>
			</Form>
		)
	}

	render() {
		const { loading, list, pagination } = this.state
		return (
			<Card bordered={false}>
				<div>
					<div>
						{this.renderAdvancedForm()}
					</div>
					<CrossReportAuditTable
						loading={loading}
						data={list}
						pagination={pagination}
						onChange={this.handleRealnameTableChange}
						ApplyStatusEnum= {ApplyStatusEnum}
					/>
				</div>
			</Card>
		)
	}
}


const CrossReportAuditForm = Form.create()(CrossReportAudit)

export default CrossReportAuditForm
