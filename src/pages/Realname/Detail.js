import React, { Component } from 'react'
import { Link } from 'react-router-dom'
import _ from 'lodash'
import { Breadcrumb, notification, Modal,AutoComplete, message, Popconfirm, Button, Steps, Card, Badge, Table, Tooltip } from 'antd'
import ImageViewer from '../../components/ImageViewer'
import StaticFieldBoard from '../../components/StaticFieldBoard'
import moment from 'moment'
import request,{ idAuthApi } from '../../utils/request'
import { AuthTypeMap, AuthState, AuthStep, AuthPicStatus, AuthFinanceStatus } from '../../utils/config'
const { Option } = AutoComplete;
const { Step } = Steps

// 日志列表
const columns = [{
  title: '操作类型',
  dataIndex: 'Id',
  render: (Id) => Id === -1 ? '自动审核' : '合规审核'
}, {
  title: '操作人',
  dataIndex: 'Operator',
  key: 'Operator',
}, {
  title: '执行结果',
  dataIndex: 'Result',
  key: 'Result',
  render: result => result === 1 ? <Badge status="success" text="通过" /> : <Badge status="error" text="未通过" />,
}, {
  title: '操作时间',
  dataIndex: 'CreateTime',
  key: 'CreateTime',
  render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
}, {
  title: '备注',
  dataIndex: 'Remark',
}]

class Detail extends Component {
  constructor(props) {
    super(props)

    let  data = this.props.location.state;
    let CompanyId
    if(data){
      CompanyId  = data.CompanyId;
    }else{
      CompanyId  = this.props.match.params ? this.props.match.params.companyId : null
      data =  CompanyId ? JSON.parse(localStorage.getItem(CompanyId)) : null
    }
    console.log("data,this.prop",data,this.props)
    console.log("sessionStorage.getItem(CompanyId)",JSON.parse(localStorage.getItem(CompanyId)))
    this.state = {
      stepDirection: 'horizontal',
      showAction: true,
      companyId: CompanyId,
      Info: data,
      showRejectForm: false,
      rejectReason: '',
      userInfo: {},
      photo: [],
      logs: [],
      loading: false,
      VerbalTricks:[]
    }
  }

  rejectForm() {
    this.setState({
      showRejectForm: true
    })
  }

  inputRejectReason(e) {
    this.setState({
      rejectReason: e
    })
  }

  hideRejectForm() {
    this.setState({
      showRejectForm: false
    })
  }

  componentDidMount() {
    this.fetch()
    this.getTunnelList()
  }

  async audit(passed) {
    // 设置loading，关闭驳回对话框
    this.setState({
      showRejectForm: false,
      loading: true,
    })
    let { CompanyId, AuthType, UserName, BankAccount, BankName, RegionType, AccountType, CompanyName, AlipayAccount, ChannelId} = this.state.Info
    let check = true
    // console.log("AlipayAccount",AlipayAccount,this.state.Info)
    if(RegionType==="大陆" && AccountType==='支付宝账号' && !AlipayAccount){
      message.warning('用户没有支付宝账户')
      return true;
    }
    try {
      //点击审核通过的时候，才去查银行信息
      if (passed && !BankName.trim() && RegionType==="大陆" && AccountType!=='支付宝账号') {
				//调用银联接口，获取银行名称
				const {BankName:bankName} = await idAuthApi('GetBankInfoByCardNo', {CardNo:BankAccount.split(' ').join('')})
				bankName && (BankName = bankName)
			}
      await request('UpdateIdentityStatus', {
        CompanyId,
        AuthType,
        // BankName,
        AuditResult: passed ? 'YES' : 'NO',
        RejectReason: this.state.rejectReason,
      })

      if(!passed || RegionType!=="大陆"){
        this.setState({
          showAction: false,
          loading: false
        })
        message.success(passed? '审核通过' : '完成驳回', 1, () => {
          this.props.history.push('/realname')
        })
      }
    } catch(err) {
      check = false
      notification['error']({
        message: '审核失败',
        description: err.message || '内部错误'
      })
      this.setState({ loading: false })
    }
    if (RegionType==="大陆" && passed && check) {
      try {
        //自动打款
        let params = {
          CompanyId,
          AuthType: AuthTypeMap[AuthType],
          Name:AuthTypeMap[AuthType]===0?CompanyName:UserName,
          RegionType,
          AccountType,
          ChannelId
        }
        if(AccountType==='银行账号'){
          params = Object.assign(params,{
            BankCardId:BankAccount,
            BankName
          })
        }else{
          params = Object.assign(params,{
            AlipayAccount
          })
        }
        await idAuthApi('FinishLegal',params)
        message.success('审核通过', 1,()=>{
          message.success('已发起自动打款', 1,()=>{
            this.props.history.push('/realname')
          })
        })
      }catch(err){
        this.setState({
          showAction: false,
          loading: false
        })
        message.success('审核通过', 1,()=>{
          notification['error']({
            message: '自动打款失败',
            description: err.message || '内部错误',
            duration: 2
          })
        })
      }
    }
  }

  CheckMatchOfEnterprise(options = {}) {
    // 批量发送信息
    request('CheckMatchOfEnterprise', options)
      .then(resp => {
        if (resp.RetCode !== 0) {
          throw Error(JSON.stringify(resp))
        }
        let message = '', description = '';

        let { CompanyNameMatch, CreditCodeMatch } = resp.Record;

        if (CompanyNameMatch && CreditCodeMatch) {
          message = '证照匹配'
        } else {
          message = '证照不匹配';
          description = "请人工复核:";
          if (!CompanyNameMatch) description += '公司名称、'
          if (!CreditCodeMatch) description += '社会信用代码、'
        }

        notification.open({
          message,
          description: description.slice(0, -1),
        });
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }

  //Scloud检查证照
  async sloudCheck() {
    let data = this.state.Info;
    let { channel, RegionType, AuthType, CompanyId, IdentityNo, UserName } = data;
    // SCloud渠道+非大陆证件号的个人认证
    if (
      channel === "SCloud" &&
      RegionType !== "大陆" &&
      AuthType === "个人认证"
    ) {
      let res = await request("CheckSCloudCodeRepeat", { CompanyId, IdentityNo, UserName });
      if(res.RetCode===0){
        request('GetVerifyLog', { CompanyId }).then((data)=>{
          if(data.RetCode===0){
            this.setState({
              logs: data.Info,
            })
          }
        })
      }
    }
  }

  // 查询基本信息
  async fetch() {
    let Info = this.state.Info
    if(!Info){
      let self = this
      this.setState({
        loading: true
      })
      let resp = await request('GetUserAuthInfo', { CompanyId: parseInt(this.props.match.params.companyId) })
      if(resp.RetCode===0 && resp?.AuthInfo.length>0){
        Info = resp.AuthInfo[0]
        self.setState({
          Info:resp.AuthInfo[0]
        })
      }

    }
    let self = this
    // Loading Modal
    self.setState({ loading: true })
    const { CompanyId, AuthType, CertificateType, IsUnified } = Info
    Promise.all([
      request('GetUserAuthInfo', { CompanyId, AuthType }),
      request('GetIdentityURL', { CompanyId, AuthType, CertificateType, IsUnified }),
      request('GetVerifyLog', { CompanyId }),
    ])
      .then(datas => {
        // 结果不正确时报错
        for (var i in datas) {
          if (datas[i].RetCode !== 0) {
            throw new Error(datas[i].Message)
          }
        }

        if (datas[0].TotalCount === 0) {
          throw new Error('找不到用户提交的审核信息')
        }

        delete datas[1].RetCode
        //日志为空
        if (!datas[2] || datas[2].Info.length === 0) {
          this.sloudCheck();
        }

        // // 如果是 自动审核  暂时伪造一条日志
        // 这条思路貌似无法解决 审核原因的问题
        // if(datas[0].AuthInfo[0].AuthMethod === "自动审核"){
        //   const info = datas[0].AuthInfo[0];
        //   const log = {
        //     CreateTime:info.Created,
        //     Id: -1,
        //     Operator:info.AuthMethod,
        //     Remark:info.AuditState,
        //     Result:1
        //   }
        //   datas[2].Info.push(log)
        // }

        this.setState({
          userInfo: datas[0].AuthInfo[0],
          photo: datas[1],
          logs: datas[2].Info,
          loading: false,
          showAction: datas[0].AuthInfo[0].AuditState === '待审核',
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取用户信息失败',
          description: err.message || '内部错误'
        })

        self.setState({ loading: false })
      })
  }
  getTunnelList = ()=>{
    request('DescribeVerbalTricks', {VerbalType:['Common','Company']})
    .then(resp => {
      if (resp.RetCode === 0) {
        this.setState({VerbalTricks:resp.VerbalTricks})
      }else{
        message.error('获取列表失败!')
      }
    })
    .catch(err => {
      // 报错
      notification['error']({
        message: '发送失败',
        description: err.message || '内部错误'
      })
    })
  }
  render() {
    const { stepDirection,Info,loading,userInfo,photo,logs } = this.state
    const { AuditState } = userInfo
    const step = (
      <Steps direction={stepDirection} current={AuthStep[AuditState]} initial={0}>
        <Step title="用户提交" />
        <Step title="证照审核"  status={AuthPicStatus[AuditState]}/>
        <Step title="财务审核" status={AuthFinanceStatus[AuditState]}/>
        <Step title="完成" status={AuditState === AuthState.AuthSuccess ? 'finish': 'wait'}/>
      </Steps>
    )
    const BankAccount = '银行卡号';
    const AlipayAccount = '支付宝账号';
    // 个人审核信息模板
    const userInfoData = {
      '用户姓名': userInfo.UserName,
      '认证类型': userInfo.AuthType,
      '身份地区': userInfo.RegionType,
      '证件号码': userInfo.IdentityNo,
      '所属渠道': Info ? Info.channel :"",
      '认证渠道': Info ? (Info.AuthMethod || "")+(Info.AuthState? "-"+Info.AuthState: ""):"",
    }
    if(userInfo.RegionType==='大陆'){
      userInfo.BankAccount?userInfoData[BankAccount]=userInfo.BankAccount:''
    }
    const userInfoCard = (
      <div style={{ marginBottom: 24 }}>
        <StaticFieldBoard data={userInfoData} />
        <br />
        <div>
          <ImageViewer src={photo.IDPhotoFrontURL} name="证件照正面" style={{ marginRight: 10 }} />
         {photo.IDPhotoBackURL ? <ImageViewer src={photo.IDPhotoBackURL} name="证件照反面" /> : null }
        </div>
      </div>
    )
    // 企业认证审核信息模板
    const companyInfoData = {
      '用户姓名': userInfo.UserName,
      '手机号码': userInfo.TelephoneNo,
      '公司名称': userInfo.CompanyName,
      '企业地址': userInfo.CompanyAddress,
      '信用代码': userInfo.CreditCode,
      '所属渠道': Info ? Info.channel : "",
      '所在地区': Info ? Info.RegionType : ""
    }
    if(userInfo.RegionType==='大陆'){
      userInfo.BankAccount?companyInfoData[BankAccount]=userInfo.BankAccount:companyInfoData[AlipayAccount]=userInfo.AlipayAccount;
    }
    const companyInfoCard = (
      <div style={{ marginBottom: 24 }}>
        <StaticFieldBoard data={companyInfoData} />
        <br />
        <div>
          <ImageViewer src={photo.BusinessLicenseURL} name="公司三证" />
        </div>
      </div>
    )
    let infoCard = _.isEmpty(userInfo) ? '' : (userInfo.AuthType.indexOf('个人认证') !== -1 ? userInfoCard : companyInfoCard)

    let actionForm = (
      <div>
        <Popconfirm title="确定通过审核？" onConfirm={() => { this.audit(true) }} okText="通过" cancelText="取消">
          <Button type="primary" style={{ marginRight: 10 }}>通过</Button>
        </Popconfirm>
        &nbsp;

        <Button type="danger" onClick={() => { this.rejectForm() }} style={{ marginRight: 10 }}>驳回</Button>
        &nbsp;
        <Popconfirm title="确定验证公司证照信息？" onConfirm={() => { this.CheckMatchOfEnterprise({ URL: photo.BusinessLicenseURL, CompanyName: userInfo.CompanyName, CreditCode: userInfo.CreditCode }) }} okText="确定" cancelText="取消">
          <Button type="primary" disabled={userInfo.AuthType === '个人认证'} style={{ marginRight: 10 }}>信息验证</Button>
        </Popconfirm>
      </div>
    )

    if (!this.state.showAction) actionForm = ''
    const VerbalTricks= this.state.VerbalTricks;
		const dataSource = VerbalTricks.map(item=>item.VerbalTrick);
    return (
      <div>
        <Breadcrumb style={{ marginBottom: 10 }}>
          <Breadcrumb.Item><Link to={{pathname:'/realname',state:{formValues:{...Info?.form},pagination:{...Info?.pagination}}}}>审核列表</Link></Breadcrumb.Item>
          <Breadcrumb.Item>详情</Breadcrumb.Item>
        </Breadcrumb>
        <Card style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {step}
        </Card>
        <Card title="用户信息" style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {infoCard}
          {actionForm}
        </Card>
        <Card title="审核日志" bordered={false}>
          <Table
            pagination={false}
            loading={loading}
            dataSource={logs}
            rowKey={record => record.Id}
            columns={columns}
          />
        </Card>
        <Modal
          title="审核驳回"
          visible={this.state.showRejectForm}
          onOk={() => { this.audit(false) }}
          onCancel={() => { this.hideRejectForm() }}
          width={800}
        >
          <Tooltip title={this.state.rejectReason}>
          <AutoComplete
				  style={{ width: "100%" }}
				  dropdownMatchSelectWidth={false}
        	dropdownStyle={{ width: 500 }}
				  dataSource={dataSource.map(item=><Option key={item} value={item} title={item}>
					{item}
				  </Option>)}
				  onChange = {e => { this.inputRejectReason(e)}}
				  placeholder="请填写理由"
          //value={this.state.rejectReason}
				  filterOption={(inputValue, option) =>
					option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
				  }
				/>
          </Tooltip>
        </Modal>
      </div>
    )
  }
}

export default Detail
