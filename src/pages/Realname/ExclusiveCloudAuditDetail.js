import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Breadcrumb, notification, Modal, AutoComplete, Tooltip, message, Popconfirm, Button, Card, Table, Badge } from 'antd'
import ImageViewer from '../../components/ImageViewer'
import StaticFieldBoard from '../../components/StaticFieldBoard'
import moment from 'moment'
import request, { newAuth<PERSON>pi } from '../../utils/request';
const { Option } = AutoComplete;

// 日志列表
const columns = [{
  title: '操作类型',
  dataIndex: 'Id',
  render: (Id) => Id === -1 ? '自动审核' : '合规审核'
}, {
  title: '操作人',
  dataIndex: 'Operator',
  key: 'Operator',
}, {
  title: '执行结果',
  dataIndex: 'Result',
  key: 'Result',
  render: result => result === 1 ? <Badge status="success" text="通过" /> : <Badge status="error" text="驳回" />,
}, {
  title: '操作时间',
  dataIndex: 'CreateTime',
  key: 'CreateTime',
  render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
}, {
  title: '备注',
  dataIndex: 'Remark',
}]

class ExclusiveCloudAuditDetail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showAction: true,
      CompanyId: parseInt(props?.match?.params?.CompanyID),
      TargetEnv: props?.match?.params?.TargetEnv,
      showRejectForm: false,
      rejectReason: '',
      userInfo: {},
      IdentityFile: "",
      logs: [],
      loading: false,
      VerbalTricks: [],
      visible: false,
      isCompanyIDTrue: false,
      detailInfo: {}
    }
  }
  componentDidMount() {
    this.DescribeAuthLog()
    this.DescribeVerbalTricks()
    this.GetAuthAuditDetail()
  }
  rejectForm = () => {
    this.setState({
      showRejectForm: true
    })
  }
  inputRejectReason(e) {
    this.setState({
      rejectReason: e
    })
  }
  hideRejectForm() {
    this.setState({
      showRejectForm: false
    })
  }
  async audit(passed) {
    // 设置loading，关闭驳回对话框
    this.setState({
      showRejectForm: false,
      loading: true,
    })
    const { CompanyId } = this.state
    try {
      request('AuditPrivateAuth', {
        CompanyId: CompanyId,
        Pass: passed,
        RejectReason: this.state.rejectReason
      }).then((resp) => {
        if (resp.RetCode === 0) {
          this.setState({
            showAction: false,
            loading: false
          })
          message.success(passed ? '审核通过' : '完成驳回', 3, () => {
            window.location.reload()
          })
        }else{
          notification['error']({
            message: '审核失败',
            description: resp.Message || '内部错误'
          })
          this.setState({ loading: false })
        }

      })
    } catch (err) {
      notification['error']({
        message: '审核失败',
        description: err.message || '内部错误'
      })
      this.setState({ loading: false })
    }
  }
  DescribeAuthLog() {
    let self = this
    self.setState({ loading: true })
    const { CompanyId } = self.state
    if (!CompanyId) {
      message.error('CompanyId不存在，请返回主页重新进入详情页')
      return
    }

    request('GetVerifyLog', { CompanyId: CompanyId, Source: 3 })
      .then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetVerifyLog获取失败',
            description: resp.message || resp.Message
          })
          return
        }
        self.setState({
          logs: resp.Info,
          loading: false
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: 'DescribeAuthLog获取列表失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        self.setState({
          loading: false,
          logs: []
        })
        return
      })
  }
  GetFileURL = () => {
    const { LivenessFile } = this.state.userInfo;
    if (!LivenessFile) {
      message.error('LivenessFile错误!')
      return
    }
    this.GetUS3File(LivenessFile)
  }
  GetUS3File = (val) => {
    if (!val) {
      message.error('文件不存在！')
      return
    }
    this.setState({ loading: true })
    newAuthApi('GetUS3File', { FileName: val, Source: 'AUTH' })
      .then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetUS3File获取失败',
            description: resp.message || resp.Message
          })
          return
        }
        this.setState({
          loading: false
        }, () => {
          window.open(resp.FileURL)
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: 'GetUS3File获取失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        this.setState({
          loading: false,
        })
        return
      })
  }
  GetAuthAuditDetail() {
    let { CompanyId, TargetEnv } = this.state
    request('GetPrivateAuthAuditDetail', { CompanyId: CompanyId, TargetEnv })
      .then(resp => {
        if (resp.RetCode === 0) {
          let showAction = resp.Data.AuthState === '_LEGAL_IN_AUDIT'
          this.setState({ detailInfo: resp.Data, isCompanyIDTrue: true, showAction, loading: false, IdentityFile: resp.Data.BusinessLicenseDownloadUrl })
          // this.getPicture(resp.Data.BusinessLicenseUS3FileName)
          return
        }
        notification['error']({
          message: '获取列表失败',
          description: resp.Message || '内部错误'
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })
      })
  }
  DescribeVerbalTricks() {
    request('DescribeVerbalTricks', {
      VerbalType: ['Common', 'Company']
    })
      .then(resp => {
        if (resp.RetCode === 0) {
          this.setState({ VerbalTricks: resp.VerbalTricks })
        } else {
          message.error('获取列表失败!')
        }
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
      })
  }
  getAuthStatus = (AuthStatus) => {
    switch (AuthStatus) {
      case 'Manual':
      case 'Prepare':
      case 'Init':
        return 'wait';
      case 'ManualRejected':
        return 'error'
      case 'ManualResolve':
      case 'Abort':
      default:
        return 'finish'
    }
  }
  getPicture(val) {
    if (!val) {
      message.error('文件不存在！')
      return
    }
    newAuthApi('GetUS3File', { FileName: val, Source: 'AUTH' })
      .then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetUS3File获取失败',
            description: resp.message || resp.Message
          })
          return
        }
        this.setState({
          IdentityFile: resp.FileURL
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: 'GetUS3File获取失败',
          description: err.message || '内部错误'
        })
        return
      })
  }
  render() {
    const { loading, IdentityFile, detailInfo, logs, showAction, TargetEnv } = this.state
    // 个人审核信息模板
    const userInfoData = {
      '用户姓名': detailInfo?.UserName,
      "证件号码": detailInfo?.IdentityNo,
      '所在地区': detailInfo?.RegionType
    }
    const companyInfoData = {
      '用户姓名': detailInfo?.UserName,
      '手机号码': detailInfo?.TelephoneNo,
      '公司名称': detailInfo?.CompanyName,
      '企业地址': detailInfo?.CompanyAddress,
      '信用代码': detailInfo?.CreditCode,
      '银行卡号': detailInfo?.BankAccount,
      '所在地区': detailInfo?.RegionType
    }
    const userInfoCard = (
      <div >
        <StaticFieldBoard data={detailInfo.AuthType === 1 ? userInfoData : companyInfoData} />
        {
          TargetEnv==='global'?(
            <div style={{ margin: 30, display: "flex" }}>
              <ImageViewer src={IdentityFile} name="海外证件照" style={{ marginRight: 10 }} />
            </div>
          ):
          detailInfo.AuthType === 1 ? '':(
            <div style={{ margin: 30, display: "flex" }}>
              <ImageViewer src={IdentityFile} name="营业执照" style={{ marginRight: 10 }} />
            </div>
          )
        }

      </div>
    )

    let actionForm = (
      <div style={{ margin: 30 }}>
        <Popconfirm title="确定通过审核？" onConfirm={() => { this.audit(true) }} okText="通过" cancelText="取消">
          <Button type="primary" style={{ marginRight: 10 }}>通过</Button>
        </Popconfirm>
        <Button type="danger" onClick={() => { this.rejectForm() }}>驳回</Button>
      </div>
    )

    if (!showAction) actionForm = ''
    const VerbalTricks = this.state.VerbalTricks;
    const dataSource = Array.from(new Set(VerbalTricks.map(item => item.VerbalTrick)))
    return (
      <div>
        <Breadcrumb style={{ marginBottom: 10 }}>
          <Breadcrumb.Item><Link to='/'>主页</Link></Breadcrumb.Item>
          <Breadcrumb.Item><Link to='/exclusiveCloudAudit'>审核列表</Link></Breadcrumb.Item>
          <Breadcrumb.Item>详情</Breadcrumb.Item>
        </Breadcrumb>
        <Card title="用户信息" style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {userInfoCard}
          {actionForm}
        </Card>
        <Card title="审核日志" bordered={false}>
          <Table
            pagination={false}
            loading={loading}
            dataSource={logs}
            columns={columns}
          />
        </Card>
        <Modal
          title="审核驳回"
          visible={this.state.showRejectForm}
          onOk={() => { this.audit(false) }}
          onCancel={() => { this.hideRejectForm() }}
          width={800}
        >
          <AutoComplete
            style={{ width: "100%" }}
            dropdownMatchSelectWidth={false}
            dropdownStyle={{ width: 500 }}
            dataSource={dataSource.map(item => <Option key={item} value={item} title={item}>
              {item}</Option>)}
            onChange={e => { this.inputRejectReason(e) }}
            placeholder="请填写理由"
            filterOption={(inputValue, option) =>
              option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
            }
          />
          <Tooltip title={this.state.rejectReason}>
          </Tooltip>
        </Modal>
      </div>
    )
  }
}

export default ExclusiveCloudAuditDetail
