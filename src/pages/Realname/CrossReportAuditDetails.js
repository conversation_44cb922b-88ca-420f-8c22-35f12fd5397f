import React, { Component } from 'react'
import './List.css'
import { notification,Popconfirm,Breadcrumb,Toolt<PERSON>, Row, Col, Card, Button,Table, message, AutoComplete } from 'antd';
import ButtonImageViewer from '../../components/ImageViewer/buttonView'
import { Link } from 'react-router-dom';
import request from '../../utils/request'
import moment from 'moment';
import _ from 'lodash';
const { Option } = AutoComplete;
class CrossReportAuditDetails extends Component {
	constructor(props) {
		super(props)
    let  data = this.props?.location?.state;
    let CompanyId
    if(!data){
      CompanyId  = this.props.match.params ? this.props.match.params.companyId : null
      data =  CompanyId ? JSON.parse(localStorage.getItem(CompanyId)) : null
      this.props.location.state = data
    }
		this.state = {
			Data:this.props?.location?.state || {},
			status:this.props?.location?.state?.Status === 1,//审核状态
			CompanyId:this.props?.location?.state?.CompanyId,
			logList:[],
			loading: false,
			Error:this.props?.location?.state?.Error || {},
			VerbalTricks:[],
			PICURL:{},
		}
	}
	// 挂载前查询
	componentDidMount() {
		console.log(this.props)
		this.DescribeVerbalTricks();
		this.GetCrossBorderApplyLog();
	}
	GetCrossBorderApplyLog = () => {
		const CompanyId = this.state.CompanyId
		request('GetCrossBorderApplyLog',{CompanyId:CompanyId})
			.then(resp => {
				if (resp.RetCode === 0) {
					this.setState({
						logList:resp.Logs || []
					})
				}
			})
			.catch(err => {
				message.error(err)
			})
	}
    setError = (field,value)=>{
		let Error = this.state.Error
		Error[field.key] = value
		this.setState({
			Error:Error
		})
	}
	getPicRender = (key)=> {
		const Data = this.state.Data
		let result = Data[key]
		if(key.indexOf('Pic') !== -1 && Data[key]){
			result = <ButtonImageViewer src={Data[key]}/>
		}
    //结束时间支持 “无固定期限” 改为字符串
    if(key === 'BusinessLicenseEndTime'){
      if(isNaN(Number(result))){
        return result
      }
      return <span>{ moment((Data[key] * 1000)).format('YYYY-MM-DD HH:mm:ss')}</span>
    }
		if( key.indexOf('Time') !== -1 && Data[key]){
			result = <span>{ moment((Data[key] * 1000)).format('YYYY-MM-DD HH:mm:ss')}</span>
		}

		return result
	}
	GetPictureUrl = (name) => {
		request('GetPictureUrl', { FileName: name }).then(res=>{
			if(res.RetCode === 0){
				window.open(res.URL)
			}else{
				message.error('接口请求出错！')
			}
		})
	}
	getValueRender = (field)=>{
		const {VerbalTricks,status,Error} = this.state;
		const dataSource = VerbalTricks.map(item=>item.VerbalTrick);
		const isPic = field.key.indexOf('Pic') > -1;
		console.log(field.key,isPic);
		return {
			name:field.label,
			value: this.getPicRender(field.key),
			error:(isPic ? (<Tooltip title={Error[field.key]}>
				<AutoComplete
				  defaultValue ={Error[field.key]}
				  title ={Error[field.key]}
				  style ={{ width: 200 }}
          dataSource={dataSource.map(item=><Option key={item} value={item} title={item}>
            {item}</Option>)}
				  onChange = {(v)=> this.setError(field,v)}
				  disabled={!status}
				  placeholder="请选择错误信息"
				/></Tooltip>) : null)
		}
	}
	// 搜索
	handleSearch = (e)=> {
		e.preventDefault()
	}
	acceptCase = ()=>{
		let  Error = this.state.Error
		for(var i in Error){
			if(!Error[i]){
				delete Error[i]
			}
		}
		console.log('Error',Error)
		if(!_.isEmpty(Error)){
			message.error("请先清空驳回信息")
			return
		}
		this.auditCrossBorderApply({
			Status:2,
			Error: {},
			Id:this.state.Data?.Id
		})
		this.setState({
			status:2,
			Error: {},
		})
	}
	auditCrossBorderApply = (data)=>{
		request('AuditCrossBorderApply',data)
		.then(resp => {
			if(resp.RetCode === 0){
				message.success('请求成功！')
			}else{
				message.error('请求失败！'+ resp.Message || '')
			}
		})
		.catch(err => {
			message.error(err)
		})
	}
	rejectCase = ()=>{
		const Error = this.state.Error
		if(_.isEmpty(Error)){
			message.error("请先输入驳回信息")
			return
		}
		console.log(Error)
		this.auditCrossBorderApply({
			Status:3,
			Error:Error,
			Id:this.state.Data?.Id
		})
		this.setState({
			status:3,
			Error: Error,
		})
	}
	renderAdvancedForm() {
        const leftFields = [
            {
                label: '企业名称',
                key: 'CompanyName'
            },
            {
                label: '发证机构',
                key: 'LicenseIssuingAgency',
            },
            {
                label: '统一社会信用代码',
                key: 'CompanyCode',
            },
            {
                label: '法人',
                key: 'LegalEntityName'
            },
            {
                label: '住所经营场所',
                key: 'BusinessPlace'
            },
            {
                label: '邮政编码',
                key: 'PostalCode'
            },
            {
                label: '营业执照开始时间',
                key: 'BusinessLicenseBeginTime',
            },
			{
                label: '营业执照结束时间',
                key: 'BusinessLicenseEndTime',
            },
            {
                label: '营业执照',
                key: 'BusinessLicensePic'
            }
        ];

        const rightFields = [
            {
                label: '经办人姓名',
                key: 'ManagerName',
            },
            {
                label: '经办人身份证号',
                key: 'ManagerLicenseId'
            },
            {
                label: '经办人地址',
                key: 'ManagerAddress'
            },
            {
                label: '经办人联系电话',
                key: 'ManagerPhone'
            },
						{
                label: '经办人邮箱',
                key: 'ManagerEmail'
            },
            {
                label: '经办人身份证',
                key: 'ManagerLicensePic'
            },
						{
							label: '附件1（联通）跨境云联网服务协议',
							key: 'ServiceProtocolPic'
            },
						{
                label: '授优刻得委托书',
                key: 'UCloudProxyLetterPic',
            },
            {
                label: '经办人介绍信',
                key: 'ProxyLetterPic',
            },
            {
                label: '业务信息安全承诺书',
                key: 'PromiseLetterPic',
            },
						// {
						// 	label: '附件6 专用发票信息采集表',
						// 	key: 'SpecialInvoiceInfo',
            // },
        ];

        let leftItems = [];
        let rightItems = [];
        leftFields.forEach(field => {
            leftItems.push(this.getValueRender(field));
        });

        rightFields.forEach(field => {
            rightItems.push(this.getValueRender(field));
        });
		const columnsAudit = [{
			title: '名称',
			dataIndex: 'name',
		},{
			title: '值',
			dataIndex: 'value',
		},{
			title: '错误',
			dataIndex: 'error',
		}]
		const { status } = this.state;
		return (
			<div>
				<Row>
					<Col span={12} style={{ paddingRight: '16px', borderRight: '1px solid rgb(249 243 243)' }}>
						<h3 style={{ textAlign: 'center' }}>企业信息</h3>
						<Table columns={columnsAudit} dataSource= {leftItems} pagination={false}/>
					</Col>
					<Col span={12} style={{ paddingLeft: '16px', background: '#fff' }}>
						<h3 style={{ textAlign: 'center' }}>经办人信息</h3>
						<Table columns={columnsAudit} dataSource= {rightItems} pagination={false}/>
					</Col>
              </Row>
			  {status ? (<div style={{display:"flex",flexDirection:"row-reverse",marginTop:"28px"}}>
			  <Popconfirm title="高危操作，确定通过？" onConfirm={this.acceptCase} okText="Yes" cancelText="No">
					<Button type="primary"  style={{ margin: "0 28px 28px 28px" }}>通过</Button>
			  </Popconfirm>
			  <Popconfirm title="高危操作，确定驳回？" onConfirm={this.rejectCase} okText="Yes" cancelText="No">
					<Button type="danger">驳回</Button>
			  </Popconfirm>
			  </div>): null }
			</div>

		)
	}
    DescribeVerbalTricks = ()=>{
        request('DescribeVerbalTricks', {VerbalType:['Common','Tunnel']})
        .then(resp => {
          if (resp.RetCode === 0) {
            this.setState({VerbalTricks:resp.VerbalTricks})
          }else{
            message.error('获取列表失败!')
          }
        })
        .catch(err => {
          // 报错
          notification['error']({
            message: '发送失败',
            description: err.message || '内部错误'
          })
        })
    }
	render() {
		const { loading, logList } = this.state
		const columns = [{
			title: '操作人',
			dataIndex: 'Operator',
		},{
			title: '操作时间',
			dataIndex: 'CreateTime',
			render: (val) => <span>{ moment((val * 1000)).format('YYYY-MM-DD HH:mm:ss')}</span>
		},{
			title: '执行',
			dataIndex: 'Remark',
		}]
		return (
			<Card bordered={false}>
				<div>
					<Breadcrumb style={{ marginBottom: 10 }}>
						<Breadcrumb.Item><Link to={{pathname:'/CrossReportAudit'}}>审核列表</Link></Breadcrumb.Item>
						<Breadcrumb.Item>详情</Breadcrumb.Item>
					</Breadcrumb>
					<Card title="审核信息" bordered={true}>
						{this.renderAdvancedForm()}
					</Card>
					<Card title="订单日志" bordered={false}>
						<Table
							loading={loading}
							dataSource={logList}
							columns={columns}
						/>
					</Card>
				</div>
			</Card>
		)
	}
}
export default CrossReportAuditDetails
