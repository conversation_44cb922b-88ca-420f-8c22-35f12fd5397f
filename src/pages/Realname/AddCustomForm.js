import React from 'react';
import { Form,Row, Input, Button, Select} from 'antd';
const FormItem = Form.Item
class AddCustomForm extends React.Component {
    handleSubmit = e => {
        e.preventDefault();
        this.props.form.validateFields((err, values) => {
            if (!err) {
                this.props.handleSubmit(values)
            }
        });
    };
    onCancel = e => {
        e.preventDefault();
        this.props.onCancel()
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const TYPE = this.props.TYPE
        return (
            <Form onSubmit={this.handleSubmit} className="login-form">
                <Form layout="inline" className="ant-advanced-search-form" style={{ background: "#fff", border: 0 }}>
                    <Row key = "type">
                        <FormItem label="类型" required={true}>
                            {getFieldDecorator('VerbalType', {
                                rules: [{ required: true, message: '请选择类型！' }],
                            })(
                                <Select>
                                    {Object.keys(TYPE).map(item => <Select.Option key={item} value={item}>{TYPE[item]}</Select.Option>)}
                                </Select>
                            )}
                        </FormItem>
                    </Row>
                    <Row key = "words">
                        <FormItem label="添加短语" required={true}>
                            {getFieldDecorator('VerbalTrick', {
                                rules: [{ required: true, message: '请添加短语！' }],
                            })(
                                <Input />
                            )}
                        </FormItem>
                    </Row>
                    <Row key = "button">
                        <Button style={{ margin: "0 20px 0 170px" }} onClick={this.onCancel}>取消</Button>
                        <Button type='primary' onClick={this.handleSubmit}>添加</Button>
                    </Row>
                </Form>
            </Form>
        );
    }
}

const AddCustomFormWrapper = Form.create({ name: 'nomal' })(AddCustomForm);

export default AddCustomFormWrapper