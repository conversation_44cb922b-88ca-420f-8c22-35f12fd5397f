import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Breadcrumb, notification, Modal,AutoComplete,Tooltip, message, Popconfirm, Button, Steps, Card, Table } from 'antd'
import ImageViewer from '../../components/ImageViewer'
import StaticFieldBoard from '../../components/StaticFieldBoard'
import moment from 'moment'
import request, {newAuthApi}  from '../../utils/request';
const { Option } = AutoComplete;
const { Step } = Steps;
// 日志列表
const columns = [{
  title: '操作类型',
  dataIndex: 'Type',
}, {
  title: '操作人',
  dataIndex: 'Operator',
  key: 'Operator',
}, {
  title: '执行结果',
  dataIndex: 'ResultStatus',
  key: 'ResultStatus',
}, {
  title: '操作时间',
  dataIndex: 'CreateTime',
  key: 'CreateTime',
  render: val => <span>{val?moment(val * 1000).format('YYYY-MM-DD HH:mm:ss'):"暂无"}</span>,
}, {
  title: '备注',
  dataIndex: 'Remark',
}]

class APPDetail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      showAction: true,
      CompanyId: this.props?.location?.state?.CompanyId,
      FlowId: this.props?.match?.params?.FlowId,
      Id: this.props?.match?.params?.Id,
      showRejectForm: false,
      rejectReason: '',
      userInfo: {},
      BestImage:"",
      logs: [],
      loading: false,
      VerbalTricks:[],
      visible:false,
    }
  }
  componentDidMount() {
    console.log('this.state.Id', this.state.Id)
    this.DescribeAuthLog()
    this.DescribeManualAuthInfo()
    this.DescribeVerbalTricks()
  }
  rejectForm = ()=> {
    this.setState({
      showRejectForm: true
    })
  }
  inputRejectReason(e) {
    this.setState({
      rejectReason: e
    })
  }
  hideRejectForm() {
    this.setState({
      showRejectForm: false
    })
  }
  async audit(passed) {
    // 设置loading，关闭驳回对话框
    this.setState({
      showRejectForm: false,
      loading: true,
    })
    const { CompanyId,FlowId } = this.state
    try {
      const UpdateIdentityStatus = await newAuthApi('AuditFaceAuthWithManualReview', {
        CompanyId:parseInt(CompanyId),
        AuditResult: passed ? 'Yes' : 'No',
        RejectReason: this.state.rejectReason,
        FlowId: FlowId
      })
      Promise.resolve(UpdateIdentityStatus).then(() => {
        this.setState({
          showAction: false,
          loading: false
        })
        message.success(passed ? '审核通过' : '完成驳回', 3, () => {
          window.location.reload()
        })
      })
    } catch(err) {
      notification['error']({
        message: '审核失败',
        description: err.message || '内部错误'
      })
      this.setState({ loading: false })
    }
  }
  DescribeAuthLog(){
    let self = this
    self.setState({ loading: true })
    const FlowId = self.state.FlowId
    if(!FlowId){
      message.error('FlowId不存在，请返回主页重新进入详情页')
      return
    }

    newAuthApi('DescribeAuthLog', {FlowId: FlowId })
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'DescribeAuthLog获取失败',
            description: resp.message || resp.Message
          })
          return
        }
				self.setState({
          logs: resp.DataSet,
					loading: false
				})
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'DescribeAuthLog获取列表失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				self.setState({
					loading: false,
					logs: []
				})
				return
			})
  }
  GetFileURL = () =>{
    const { LivenessFile	 } = this.state.userInfo;
    if(!LivenessFile){
      message.error('LivenessFile错误!')
      return
    }
    this.GetUS3File(LivenessFile)
  }
  GetUS3File = (val)=>{
    if(!val){
      message.error('文件不存在！')
      return
    }
    this.setState({ loading: true })
    newAuthApi('GetUS3File', { FileName:val , Source:'AUTH' })
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetUS3File获取失败',
            description: resp.message || resp.Message
          })
          return
        }
				this.setState({
					loading: false
				},()=>{
          window.open(resp.FileURL)
        })
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'GetUS3File获取失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				this.setState({
					loading: false,
				})
				return
			})
  }
  DescribeManualAuthInfo (){
    this.setState({ loading: true })
    const {FlowId	} = this.state
    if(!FlowId){
      message.error('FlowId不存在')
      return
    }
    newAuthApi('DescribeManualAuthInfo', { FlowId:FlowId})
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'DescribeManualAuthInfo获取失败',
            description: resp.message || resp.Message
          })
          return
        }
        this.setState({
          userInfo: resp.DataSet[0],
          CompanyId: resp.DataSet[0].CompanyId,
          showAction:resp.DataSet[0].FlowStatus === 'Manual',
          loading: false,
          VideoName:resp.DataSet[0].LivenessFile
        })
        this.getBestImage(resp.DataSet[0].BestImage)
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'DescribeManualAuthInfo获取失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				this.setState({
					loading: false,
				})
				return
			})
  }
  DescribeVerbalTricks (){
    request('DescribeVerbalTricks', {VerbalType:['Common','Person','APP']})
    .then(resp => {
      if (resp.RetCode === 0) {
        this.setState({VerbalTricks: resp.VerbalTricks})
      }else{
        message.error('获取列表失败!')
      }
    })
    .catch(err => {
      // 报错
      notification['error']({
        message: '发送失败',
        description: err.message || '内部错误'
      })
    })
  }
  getAuthStatus = (AuthStatus) => {
    switch (AuthStatus) {
      case 'Manual':
      case 'Prepare':
      case 'Init':
        return 'wait';
      case 'ManualRejected':
        return 'error'
      case 'ManualResolve':
      case 'Abort':
      default:
        return 'finish'
    }
  }
  download= ()=>{
    const { RawFile	 } = this.state.userInfo;
    if(!RawFile){
      message.error('RawFile:' + JSON.stringify(RawFile))
      return
    }
    newAuthApi('GetUS3File', { FileName: RawFile , Source:'AUTH' })
    .then(res => {
      if(res.RetCode === 0){
       let content = [];
       content.push("字段,类型,描述\n")
       content.push('RawFile,'+(Object.prototype.toString.call(res.FileURL))+','+res.FileURL+'\n')
       this.dataToCsv(content)
      }else{
       message.error( '请求出错')
      }
  })
  }
  dataToCsv(dataList) {
    var blob = new Blob(dataList, { type: "text/csv,charset=UTF-8" })
    var csvUrl = URL.createObjectURL(blob)
    // return csvUrl
    let link = document.createElement('a');
    link.download = "批量下载.csv"; //文件名字
    link.href = csvUrl;
    // 触发下载
    link.click();
  }
  getBestImage(val){
    if(!val){
      message.error('文件不存在！')
      return
    }
    newAuthApi('GetUS3File', { FileName:val , Source:'AUTH' })
    .then(resp => {
      if (resp.RetCode !== 0) {
        notification['error']({
          message: 'GetUS3File获取失败',
          description: resp.message || resp.Message
        })
        return
      }
      this.setState({
        BestImage: resp.FileURL
      })
    })
    .catch(err => {
      // 报错
      notification['error']({
        message: 'GetUS3File获取失败',
        description: err.message || '内部错误'
      })
      return
    })
  }
  CheckPersonalInfo = ()=>{
    const { userInfo,CompanyId,FlowId } = this.state
		let params = {
      IdCardName: userInfo.IdCardName,
      IdCardNumber: userInfo.IdCardNumber,
      IdPhotoFileName: userInfo.BestImage,
      CompanyId: CompanyId,
      FlowId:FlowId,
      Source:"AUTH"
    }
		newAuthApi('CheckPersonalInfo',params)
		.then(resp => {
			if(resp.IsMatch){
				message.success('请求成功！')
        this.setState({
          visible:true
        })
			}else{
        this.setState({
          visible:false
        })
				message.error('请求失败！'+ resp.Message || '')
			}
		})
		.catch(err => {
      this.setState({
        visible:false
      })
			message.error(err)
		})
	}
  CheckInformation = ()=> {
    let self = this
    self.CheckPersonalInfo()
    // self.VerifyTwoFace()
    setTimeout(()=> {
      self.DescribeAuthLog()
    },3000)
  }
  GetAndUpdateBestPicture = ()=>{
    const { userInfo, Id }  = this.state
    let params = {
      LivenessFileName: userInfo?.LivenessFile,
      Id: Id,
      Source:'AUTH'
    }
    newAuthApi('GetAndUpdateBestPicture',params)
		.then(resp => {
      if (resp.RetCode !== 0) {
        notification['error']({
          message: '获取最佳成像照失败',
          description: resp.message || resp.Message
        })
        return
      }
      this.setState({
        BestImage: resp.BestImageUrl
      })
		})
		.catch(err => {
      this.setState({
        visible:false
      })
			message.error(err)
		})
  }
  render() {
    const { loading,BestImage,userInfo,logs,visible,showAction } = this.state
    const step = (
      <Steps direction={'horizontal'} initial={0}>
        <Step title="用户提交" status='finish'/>
        <Step title="活体审核"  status={this.getAuthStatus(userInfo?.FlowStatus)}/>
      </Steps>
    )
    // 个人审核信息模板
    const userInfoData = {
      '用户姓名': userInfo?.IdCardName,
      '证件号码': userInfo?.IdCardNumber,
      '判断信息': userInfo?.JudgeInfo?.join(','),
      '认证方式':  userInfo?.KYCType
    }
    const userInfoCard = (
      <div >
        <StaticFieldBoard data={userInfoData} />
        <div style={{ margin: 20 , display:"flex"}}>
          <ImageViewer src={BestImage} name="最佳成像照" style={{ marginRight: 10 }} />
        </div>
        <div style={{ margin: 20 }}>
          <span>活体视频：</span>
          <span name="活体视频" style={{ marginLeft: 10,color:'blue',cursor:"pointer" }} onClick={this.GetFileURL}>{userInfo?.LivenessFile	}</span>
        </div>
        <div style={{color:"blue",margin:20}} onClick={this.download}>下载文件</div>
      </div>
    )

    let actionForm = (
        <Popconfirm title="确定通过审核？" onConfirm={() => { this.audit(true) }} okText="通过" cancelText="取消">
          <Button type="primary" style={{ marginRight: 10 }}>通过</Button>
        </Popconfirm>
    )

    if (!showAction) actionForm = ''
    const VerbalTricks= this.state.VerbalTricks;
		const dataSource = Array.from(new Set(VerbalTricks.map(item=>item.VerbalTrick)))
    return (
      <div>
        <Breadcrumb style={{ marginBottom: 10 }}>
          <Breadcrumb.Item><Link to='/'>主页</Link></Breadcrumb.Item>
          <Breadcrumb.Item><Link to='/APPAudit'>活体认证</Link></Breadcrumb.Item>
          <Breadcrumb.Item>详情</Breadcrumb.Item>
        </Breadcrumb>
        <Card style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {step}
        </Card>
        <Card title="用户信息" style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {userInfoCard}
          {visible ? actionForm : null}
          {showAction?<Button type="primary" onClick={this.CheckInformation}>信息验证</Button>:null}
          {showAction?<Button type="danger" style={{marginLeft:20}} onClick={this.rejectForm}>驳回</Button>:null}
          {showAction&&!BestImage?<Button type="primary"  style={{marginLeft:20}} onClick={this.GetAndUpdateBestPicture}>取最佳成像照</Button>:null}
        </Card>
        <Card title="审核日志" bordered={false}>
          <Table
            pagination={false}
            loading={loading}
            dataSource={logs}
            columns={columns}
          />
        </Card>
        <Modal
          title="审核驳回"
          visible={this.state.showRejectForm}
          onOk={() => { this.audit(false) }}
          onCancel={() => { this.hideRejectForm() }}
          width={800}
        >
          <AutoComplete
            style={{ width: "100%" }}
            dropdownMatchSelectWidth={false}
            dropdownStyle={{ width: 500 }}
            dataSource={dataSource.map(item=><Option key={item} value={item} title={item}>
            {item}</Option>)}
            onChange = {e => { this.inputRejectReason(e)}}
            placeholder="请填写理由"
            filterOption={(inputValue, option) =>
            option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
            }
          />
          <Tooltip title={this.state.rejectReason}>
          </Tooltip>
        </Modal>
      </div>
    )
  }
}

export default APPDetail
