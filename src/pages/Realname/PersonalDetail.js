import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Breadcrumb, notification, Modal,AutoComplete,Tooltip, message, Popconfirm, Button, Steps, Card, Badge, Table } from 'antd'
import ImageViewer from '../../components/ImageViewer'
import StaticFieldBoard from '../../components/StaticFieldBoard'
import moment from 'moment'
import request from '../../utils/request'
import { AuthStep, } from '../../utils/config'
const { Option } = AutoComplete;
const { Step } = Steps

// 日志列表
const columns = [{
  title: '操作类型',
  dataIndex: 'Id',
  render: (Id) => Id === -1 ? '自动审核' : '合规审核'  
}, {
  title: '操作人',
  dataIndex: 'Operator',
  key: 'Operator',
}, {
  title: '执行结果',
  dataIndex: 'Result',
  key: 'Result',
  render: result => result === 1 ? <Badge status="success" text="通过" /> : <Badge status="error" text="驳回" />,
}, {
  title: '操作时间',
  dataIndex: 'CreateTime',
  key: 'CreateTime',
  render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
}, {
  title: '备注',
  dataIndex: 'Remark',
}]

class Detail extends Component {
  constructor(props) {
    super(props)
    let CompanyId = this.props.match.params.companyId
    this.state = {
      stepDirection: 'horizontal',
      showAction: true,
      companyId: CompanyId,
      showRejectForm: false,
      rejectReason: '',
      userInfo: {},
      photo: [],
      logs: [],
      loading: false,
      VideoName:"",
      channelList:{},
      VerbalTricks:[]
    }
  }
  rejectForm() {
    this.setState({
      showRejectForm: true
    })
  }
  inputRejectReason(e) {
    this.setState({
      rejectReason: e
    })
  }
  hideRejectForm() {
    this.setState({
      showRejectForm: false
    })
  }
  componentDidMount() {
    this.getTunnelList()
    this.getChannelInfo()
    this.GetAuditedFaceAuthList()
    this.GetVerifyLog()
  }
  getTunnelList = ()=>{
    request('DescribeVerbalTricks', {VerbalType:['Common','Person']})
    .then(resp => {
      if (resp.RetCode === 0) {
        this.setState({VerbalTricks:resp.VerbalTricks})
      }else{
        message.error('获取列表失败!')
      }
    })
    .catch(err => {
      // 报错
      notification['error']({
        message: '发送失败',
        description: err.message || '内部错误'
      })       
    })
  }
  async audit(passed) {
    // 设置loading，关闭驳回对话框
    this.setState({
      showRejectForm: false,
      loading: true,
    })
    let { companyId } = this.state
    try {
      const UpdateIdentityStatus = await request('AuditFaceAuthWithManualReview', {
        CompanyId:parseInt(companyId),
        AuditResult: passed ? 'Yes' : 'No',
        RejectReason: this.state.rejectReason,
      })
      Promise.resolve(UpdateIdentityStatus).then(() => {
        this.setState({
          showAction: false,
          loading: false
        })
        message.success(passed ? '审核通过' : '完成驳回', 3, () => {
          window.location.reload()
        })
      })
    } catch(err) {
      notification['error']({
        message: '审核失败',
        description: err.message || '内部错误'
      })
      this.setState({ loading: false })
    }
  }

  CheckMatchOfEnterprise(options = {}) {
    // 批量发送信息
    request('CheckMatchOfEnterprise', options)
      .then(resp => {
        if (resp.RetCode !== 0) {
          throw Error(JSON.stringify(resp))
        }
        let message = '', description = '';

        let { CompanyNameMatch, CreditCodeMatch } = resp.Record;

        if (CompanyNameMatch && CreditCodeMatch) {
          message = '证照匹配'
        } else {
          message = '证照不匹配';
          description = "请人工复核:";
          if (!CompanyNameMatch) description += '公司名称、'
          if (!CreditCodeMatch) description += '社会信用代码、'
        }

        notification.open({
          message,
          description: description.slice(0, -1),
        });
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }
  GetAuditedFaceAuthList = ()=>{
    let { companyId} = this.state;
    if(!companyId){
      notification['error']({
        message: 'companyId:'+companyId,
        description: 'companyId错误'
      })
      return
    }
    this.setState({ loading: true })
    request('GetAuditedFaceAuthList', {CompanyId: parseInt(companyId) })
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetAuditedFaceAuthList获取失败',
            description: resp.message || resp.Message
          })
          return
        }
				this.setState({
          userInfo: resp.Data[0],
          showAction:resp.Data[0].AuthStatus === '活体待审核',
					loading: false,
          VideoName:resp.Data[0].VideoName
				},()=>{
          this.GetIdentityURL()
        })
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'GetAuditedFaceAuthList获取列表失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				this.setState({
					loading: false,
					userInfo: {}
				})
				return
			})
  }
  GetFileURL = () =>{
    const { VideoName } = this.state;
    if(!VideoName){
      notification['error']({
        message: 'VideoName:'+VideoName,
        description: 'VideoName错误'
      })
      return
    }
    this.setState({ loading: true })
    request('GetFileURL', { FileName:VideoName })
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetFileURL获取失败',
            description: resp.message || resp.Message
          })
          return
        }
				this.setState({
          URL: resp.URL,
					loading: false
				},()=>{
          window.open(resp.URL)
        })
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'GetFileURL获取失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				this.setState({
					loading: false,
				})
				return
			})
  }
  GetVerifyLog = ()=>{
    const { companyId} = this.state;
    if(!companyId){
      notification['error']({
        message: 'companyId:'+companyId,
        description: 'companyId错误'
      })
      return
    }
    this.setState({ loading: true })
    request('GetVerifyLog', { CompanyId:parseInt(companyId) })
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetVerifyLog获取失败',
            description: resp.message || resp.Message
          })
          return
        }
				this.setState({
          logs: resp.Info,
					loading: false
				})
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'GetVerifyLog获取失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				this.setState({
					loading: false,
					userInfo: {}
				})
				return
			})
  } 
  GetIdentityURL = () =>{
    const {  AuthType } = this.state.userInfo
    this.setState({ loading: true })

    request('GetIdentityURL',  { CompanyId:parseInt(this.state.companyId), AuthType, CertificateType :"身份证"})
			.then(resp => {
        if (resp.RetCode !== 0) {
          notification['error']({
            message: 'GetIdentityURL获取失败',
            description: resp.message || resp.Message
          })
          return
        }
				this.setState({
          photo: resp,
					loading: false
				})
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: 'GetFileURL获取失败',
					description: err.message || '内部错误'
				})
				// 清空列表
				this.setState({
					loading: false,
				})
				return
			})
  }
  getChannelInfo = () => {
		request('GetChannelInfo')
			.then(resp => {
				if(resp.ChannelInfo) {
					let channelList = {}
					resp.ChannelInfo.forEach(channel => {
						channelList[channel.ChannelID] = channel.Title
					})
					this.setState({
						channelList
					})
				}
			})
			.catch(() => {
				// 清空列表
				this.setState({
					channelList: {}
				})
				return
			})
	}
  getAuthStatus = (AuthStatus) => {
    switch (AuthStatus) {
      case '活体待审核':
        return 'wait';
      case '活体驳回':
        return 'error'
      default:
        return 'finish'
    }
  }
  render() {
    const { stepDirection,loading,userInfo,photo,logs,VideoName,channelList } = this.state
    const { AuthStatus } = userInfo
    const step = (
      <Steps direction={stepDirection} current={AuthStep[AuthStatus]} initial={0}>
        <Step title="用户提交" status='finish'/>
        <Step title="活体审核"  status={this.getAuthStatus(AuthStatus)}/>
      </Steps>
    )
    // 个人审核信息模板
    const userInfoData = {
      '用户姓名': userInfo.UserName,
      '认证类型': userInfo.AuthType,
      '活体随机数':userInfo.RandomNumber,
      '证件号码': userInfo.IdentityNo,
      '所属渠道': channelList ? channelList[userInfo.ChannelID] : "",
      '认证渠道': userInfo.AuthMethod,
    }
    const userInfoCard = (
      <div style={{ marginBottom: 24 }}>
        <StaticFieldBoard data={userInfoData} />
        <br />
        <div>
          <ImageViewer src={photo.IDPhotoFrontURL} name="证件照正面" style={{ marginRight: 10 }} />
        </div>
        <div style={{ marginTop: 12 }}>
          <span>活体视频：</span>
          <span name="活体视频" style={{ marginLeft: 10,color:'blue',cursor:"pointer" }} onClick={this.GetFileURL}>{VideoName}</span>
        </div>
      </div>
    )
    
    let actionForm = (
      <div>
        <Popconfirm title="确定通过审核？" onConfirm={() => { this.audit(true) }} okText="通过" cancelText="取消">
          <Button type="primary" style={{ marginRight: 10 }}>通过</Button>
        </Popconfirm>
        &nbsp;

        <Button type="danger" onClick={() => { this.rejectForm() }}>驳回</Button>
      </div>
    )

    if (!this.state.showAction) actionForm = ''
    const VerbalTricks= this.state.VerbalTricks;
		const dataSource = VerbalTricks.map(item=>item.VerbalTrick);
    return (
      <div>
        <Breadcrumb style={{ marginBottom: 10 }}>
          <Breadcrumb.Item><Link to='/'>主页</Link></Breadcrumb.Item>
          <Breadcrumb.Item><Link to='/realnamePersonal'>个人认证</Link></Breadcrumb.Item>
          <Breadcrumb.Item>详情</Breadcrumb.Item>
        </Breadcrumb>
        <Card style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {step}
        </Card>
        <Card title="用户信息" style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {userInfoCard}
          {actionForm}
        </Card>
        <Card title="审核日志" bordered={false}>
          <Table
            pagination={false}
            loading={loading}
            dataSource={logs}
            rowKey={record => record.Id}
            columns={columns}
          />
        </Card>
        <Modal
          title="审核驳回"
          visible={this.state.showRejectForm}
          onOk={() => { this.audit(false) }}
          onCancel={() => { this.hideRejectForm() }}
          width={800}
        >
          <Tooltip title={this.state.rejectReason}>
              <AutoComplete
              style={{ width: "100%" }}
              dropdownMatchSelectWidth={false}
        		  dropdownStyle={{ width: 500 }}
				      dataSource={dataSource.map(item=><Option key={item} value={item} title={item}> 
					    {item}
				      </Option>)}
              onChange = {e => { this.inputRejectReason(e)}}
              placeholder="请填写理由"
              //value={this.state.rejectReason}
              filterOption={(inputValue, option) =>
              option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
              }
             />
         </Tooltip>
        </Modal>
      </div>
    )
  }
}

export default Detail
