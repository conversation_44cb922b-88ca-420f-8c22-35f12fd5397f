import React, { Component } from "react";
import RealnameReexamTable from "../../components/RealnameReexamTable";
import "./List.css";
import {
  notification,
  Row,
  Col,
  DatePicker,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Select,
  InputNumber,
  message,
} from "antd";

import request, { newAuthApi } from "../../utils/request";
import moment from "moment";
import MultipleSelect from "@/components/MultipleSelect";
const { RangePicker } = DatePicker;
const FormItem = Form.Item;
const { Option } = Select;
const getValue = (obj) =>
  Object.keys(obj)
    .map((key) => obj[key])
    .join(",");
const enToCn = {
  Init: "初始化",
  PictureUploaded: "营业执照图片已上传",
  OCRCompleted: "OCR已完成",
  AwaitingLivenessInfo: "待确定活体信息",
  AwaitingRechargeInfo: "待确认充值信息",
  ProcessCompleted: "流程已完成",
  ProcessCompletedAwaitingReview: "流程已完成待复核",
  ProcessCanceled: "流程已取消",
  AuditException: "判断为异常",
  LicenseAuditPending: "营业执照审核中",
  LicenseAuditPass: "营业执照审核通过",
  LicenseAuditReject: "营业执照审核不通过",
  AutoTransferPending: "自动打款中",
  AutoTransferTimeout: "待财务确认",
  AwaitingManualTransfer: "待人工打款",
  AwaitConfirmAmount: "待确认打款金额",
  AwaitUploadReceipt: "待上传回执",
  FinancialAuditPending: "财务审核中",
  FinancialAuditReject: "财务审核拒绝",
};
class APPAudit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      expandForm: false, // 是否展开
      formValues: {
        // 查询表单初始值
        FlowStatus: "LicenseAuditPending",
      },
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      loading: false,
      filterText: ["高亮7日未处理", "取消超期高亮"],
      filterValue: false,
      isReexamType: true,
      channelList: {},
    };
  }
  getChannelInfo = () => {
    request("GetChannelInfo")
      .then((resp) => {
        if (resp.ChannelInfo) {
          let channelList = {};
          resp.ChannelInfo.forEach((channel) => {
            channelList[channel.ChannelID] = channel.Title;
          });
          this.setState({
            channelList,
          });
        }
      })
      .catch(() => {
        // 清空列表
        this.setState({
          channelList: {},
        });
        return;
      });
  };
  // 查询列表
  fetch = (options) => {
    // 统一设置分页或者报错
    let self = this;
    self.setState({
      loading: true,
    });
    if (!options.CompanyId) {
      delete options.CompanyId;
    }
    if (!options.IdCardNumber) {
      delete options.IdCardNumber;
    }
    if (options.FlowStatus?.length === 0) {
      delete options.FlowStatus;
    }
    if (typeof(options.time) === 'object') {
      // 格式转换
      options.StartTime = parseInt(
        options.time[0].startOf("day").format("X"),
        10
      );
      options.EndTime = parseInt(options.time[1].endOf("day").format("X"), 10);

      // 查一天的时间实际压力从0点到23点，做startOf与endOf处理
      if (options.StartTime === options.EndTime) {
        options.StartTime = options.time[0].startOf("day").format("X");
        options.EndTime = options.time[1].endOf("day").format("X");
      }
      delete options.time;
    } else {
      delete options.time;
    }
    let { current, pageSize } = this.state.pagination;
    // 初始化options
    options.Offset = (current - 1) * pageSize;
    options.Limit = pageSize;
    const env = require("../../../envConfigs/env").default;
    if (env === "local") {
      options.staff_name_en = "local_user";
    }
    this.setState({
      isReexamType: options.FlowStatus === "LicenseAuditPending",
    });
    if (options.FlowStatus && !Array.isArray(options.FlowStatus)) {
      options.FlowStatus = [options.FlowStatus];
    }
    if(options.FlowStatus && options.FlowStatus.length === Object.keys(enToCn).length) {
      delete options.FlowStatus;
    }
    // 默认只查主渠道
    // options.ChannelId = 1;
    newAuthApi("DescribeCompanyKYCFlowList", options)
      .then((resp) => {
        self.setState({
          list: resp.DataSet,
          pagination: {
            current: current,
            pageSize: pageSize,
            total: resp.TotalCount,
          },
          loading: false,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "获取列表失败",
          description: err.message || "内部错误",
        });
        // 清空列表
        self.setState({
          loading: false,
          list: [],
          pagination: {
            current: 1,
            pageSize: 20,
            total: 0,
          },
        });
        return;
      });
  };
  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  };
  // 挂载前查询
  componentDidMount() {
    const { state } = this.props.location;
    let formValues = state && state["formValues"] ? state.formValues : {};
    console.log("this.props.location", this.props.location);
    // 从URL query中获取CompanyId参数
    const query = new URLSearchParams(this.props.location.search);
    const companyId = query.get("CompanyId");
    const defaultValue = {
      CompanyId: companyId,
    };
    if (defaultValue.time) {
      defaultValue.time = [
        moment(defaultValue.time[0]),
        moment(defaultValue.time[1]),
      ];
    }
    formValues = Object.assign(formValues, defaultValue);
    this.setState(
      (prevState) => {
        return {
          formValues: {
            ...prevState.formValues,
            ...formValues,
          },
          expandForm: defaultValue.expandForm,
        };
      },
      () => {
        this.getChannelInfo();
        this.fetch({ FlowStatus: "LicenseAuditPending", ...formValues });
      }
    );
  }
  // 处理分页
  handleRealnameTableChange = (pagination, filtersArg, sorter) => {
    const { formValues } = this.state;
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      const newObj = { ...obj };
      newObj[key] = getValue(filtersArg[key]);
      return newObj;
    }, {});

    const params = {
      ...formValues,
      ...filters,
      Sort: sorter.order === "ascend" ? "ASC" : "DESC",
    };
    this.setState(
      {
        pagination: {
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
        },
      },
      () => {
        this.fetch(params);
      }
    );
  };

  // 重置搜索框
  handleFormReset = () => {
    this.setState(
      {
        formValues: {
          // 查询表单初始值
          FlowStatus: "LicenseAuditPending",
        },
        list: [], // 列表初始值
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
      },
      () => {
        this.props.form.resetFields();
        this.fetch({
          FlowStatus: "LicenseAuditPending",
          CompanyKYCType: "CompanyRemit",
        });
      }
    );
  };

  // 搜索
  handleSearch = (e) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
        expandForm: this.state.expandForm,
      };
      this.setState(
        {
          formValues: values,
          pagination: {
            current: 1,
            pageSize: 10,
          },
        },
        () => {
          this.fetch(values);
        }
      );
    });
  };

  switchFilter = () => {
    if (!this.state.filterValue) {
      this.filterUntreatedData();
    }
    this.setState({
      filterValue: !this.state.filterValue,
    });
  };
  //筛选7日未处理数据
  filterUntreatedData = () => {
    let hasUnTreatedData = this.state.list.some((value) => {
      const now = moment();
      const flag =
        value.FlowStatus === "ProcessCompletedAwaitingReview" &&
        now.diff(value.CreateTime, "days") > 7;
      return flag;
    });
    if (!hasUnTreatedData) {
      message.success("无超期订单^_^");
      return;
    }
  };
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    const { formValues, expandForm, channelList } = this.state;
    return (
      <Form
        onSubmit={this.handleSearch}
        layout="inline"
        className="ant-advanced-search-form"
      >
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col md={8} sm={24}>
            <FormItem label="状态">
              {getFieldDecorator("FlowStatus", {
                initialValue: formValues.FlowStatus,
              })(
                <MultipleSelect
                  placeholder="请选择认证状态"
                  options={
                    enToCn &&
                    Object.keys(enToCn).map((item) => {
                      return { label: enToCn[item], value: item };
                    })
                  }
                  allowClear
                  maxTagCount={2}
                />
              )}
            </FormItem>
          </Col>
          <Col md={8} sm={24}>
            <FormItem label="认证方式">
              {getFieldDecorator("CompanyKYCType")(
                <Select
                  style={{ width: "100%" }}
                  allowClear
                  placeholder="请选择认证方式"
                >
                  <Option value="CompanyRemit">对公打款</Option>
                  <Option value="UserLegalPersonLive">法人活体</Option>
                  <Option value="UserRecharge">用户充值</Option>
                </Select>
              )}
            </FormItem>
          </Col>
          <Col md={8} sm={24}>
            <FormItem label="公司Id">
              {getFieldDecorator("CompanyId", {
                initialValue: formValues.CompanyId,
              })(
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入公司Id"
                />
              )}
            </FormItem>
          </Col>
          {expandForm ? (
            <span>
              <Col md={8} sm={24}>
                <FormItem label="证件号码">
                  {getFieldDecorator("CompanyCertificateNumber", {
                    initialValue: formValues.CompanyCertificateNumber,
                  })(
                    <Input
                      style={{ width: "100%" }}
                      placeholder="请输入证件号码"
                    />
                  )}
                </FormItem>
              </Col>
              <Col md={8} sm={24}>
                <FormItem label="公司名">
                  {getFieldDecorator("CompanyName", {
                    initialValue: formValues.CompanyName,
                  })(
                    <Input
                      style={{ width: "100%" }}
                      placeholder="请输入公司名"
                    />
                  )}
                </FormItem>
              </Col>
              <Col md={8} sm={24}>
                <FormItem label="时间">
                  {getFieldDecorator("time", {
                    initialValue: formValues.time,
                  })(
                    <RangePicker
                      style={{ width: "100%" }}
                      ranges={{
                        Today: [moment().startOf("day"), moment().endOf("day")],
                        "This Month": [
                          moment().startOf("month"),
                          moment().endOf("month"),
                        ],
                      }}
                      showTime={{
                        defaultValue: [
                          moment("00:00:00", "HH:mm:ss"),
                          moment("23:59:59", "HH:mm:ss"),
                        ],
                      }}
                      format="YYYYMMMMDo"
                    />
                  )}
                </FormItem>
              </Col>
            </span>
          ) : (
            ""
          )}
          <Col md={8} sm={24}>
            <FormItem label="渠道Id">
              {getFieldDecorator("ChannelId")(
                <Select
                  showSearch
                  style={{ width: "100%" }}
                  allowClear
                  placeholder="请选择渠道Id,输入渠道号进行搜索"
                >
                  {Object.keys(channelList).map((channel) => (
                    <Option key={channel} value={channel}>
                      {channelList[channel]}
                    </Option>
                  ))}
                </Select>
              )}
            </FormItem>
          </Col>
          <Col md={8} sm={24}>
            <span style={{ float: "right", marginBottom: 24 }}>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
              <Button style={{ marginLeft: 8 }} onClick={this.handleFormReset}>
                重置
              </Button>
              {expandForm ? (
                <a style={{ marginLeft: 8 }} onClick={this.toggleForm}>
                  收起 <Icon type="up" />
                </a>
              ) : (
                <a style={{ marginLeft: 8 }} onClick={this.toggleForm}>
                  展开 <Icon type="down" />
                </a>
              )}
            </span>
          </Col>
        </Row>
      </Form>
    );
  }

  render() {
    const {
      loading,
      list,
      pagination,
      formValues,
      filterText,
      filterValue,
      isReexamType,
      channelList,
    } = this.state;
    return (
      <Card bordered={false}>
        <div>
          <div>{this.renderAdvancedForm()}</div>
          <div>
            {isReexamType && (
              <Button
                type="danger"
                style={{
                  background: "#fa8025",
                  color: "#fff",
                  marginBottom: "20px",
                }}
                onClick={this.switchFilter}
              >
                {filterText[Number(filterValue)]}
              </Button>
            )}
          </div>
          <RealnameReexamTable
            channelList={channelList}
            enToCn={enToCn}
            loading={loading}
            data={list}
            pagination={pagination}
            onChange={this.handleRealnameTableChange}
            formValues={formValues}
            isFilter={filterValue}
          />
        </div>
      </Card>
    );
  }
}

const APPAuditWrapper = Form.create()(APPAudit);

export default APPAuditWrapper;
