import React, { Component } from 'react';
import { Row, Col, Card, Form, Input, Button, Tabs, notification, Select } from 'antd';
import './DataList.css';
import DataSearchComponent from '../../components/DataSearch/index'
import { DataSearchApi } from '../../utils/request'
const { Option } = Select;
const { TabPane } = Tabs;
const FormItem = Form.Item;
//状态枚举,将英文状态转成中文在前端显示


class DataList extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            companyID: "",
            Tag: [],
            TagList: [],
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
        };

    }
    handleMessageTableChange() {
        let self = this
        if (this.state.companyID) {
            let companyIdArray = this.state.companyID.replace(/\s*/g, "").split(",");
            companyIdArray.forEach((item) => {
                self.GetInfoEvidenceTaskList(item)
            })
        } else {
            self.GetInfoEvidenceTaskList(this.state.companyID)
        }
    }
    // 挂载前查询
    componentDidMount() {
        this.GetInfoEvidenceTaskList(this.state.companyID)
        this.GetTagList();
    }
    GetInfoEvidenceTaskList(companyId) {
        let self = this
        //重发消息
        let action = "GetInfoEvidenceTaskList",
            options = {
                Limit: this.state.pagination.pageSize,
                Offset: this.state.pagination.pageSize * (this.state.pagination.current - 1)
            }
        if (companyId) {
            options['CompanyId'] = parseInt(companyId)
        }
        if (this.state.Tag) {
            options['Tags'] = this.state.Tag;
        }
        self.setState({ loading: true })
        DataSearchApi(action, options)
            .then(resp => {
                let message = '查询成功'
                self.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                    if (resp.InfoEvidenceTaskList) {
                        let {current,pageSize} = this.state.pagination
                        self.setState({
                            list: resp.InfoEvidenceTaskList,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.Count,
                            }
                        },()=>{
                            message = "查询结果暂无数据"
                        })
                        return
                    }
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    GetTagList() {
        let self = this
        //重发消息
        let action = "GetTagList",
            options = {
                Action: action,
            }
        DataSearchApi(action, options)
            .then(resp => {
                let message = '请求成功'
                if (resp.RetCode === 0) {
                    self.setState({
                        TagList: resp.TagList
                    })
                    return
                } else {
                    message = resp.Message || resp.RetCode + "请求失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    CreateEvidenceTaskResponse(companyId) {
        let self = this
        let action = 'CreateInfoEvidenceTask';
        let options = {
            CompanyId: parseInt(companyId),
            Tags: this.state.Tag,
        }
        self.setState({
            loading: true
        })
        DataSearchApi(action, options)
            .then(resp => {
                self.setState({
                    loading: false
                })
                let message = resp.Message || resp.RetCode + "创建任务失败"
                if (resp.RetCode === 0) {
                    self.GetInfoEvidenceTaskList(companyId)
                    message = '创建任务成功'
                    return
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    handleSearch = () => {
        this.handleMessageTableChange()
    }
    handleTask = () => {
        let self = this
        if (this.state.companyID) {
            let companyIdArray = this.state.companyID.replace(/\s*/g, "").split(",");
            companyIdArray.forEach((item) => {
                self.CreateEvidenceTaskResponse(item)
            })
        } else {
            self.CreateEvidenceTaskResponse(this.state.companyID)
        }
    }
    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.handleMessageTableChange()
        })
    }
    onSearch(val) {
        console.log('search:', val);
    }
    //上部份，获取信息
    renderAdvancedForm() {
        let self = this
        return (
            <Form layout="inline" className="ant-advanced-search-form">
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144444}>
                        <FormItem label="公司ID">
                            <Input style={{ width: '100%' }} value={this.state.companyID} placeholder="请输入对应的公司ID" onChange={(e) => { this.setState({ companyID: e.target.value }) }} />
                        </FormItem>
                    </Col>
                    <Col span={10} key={1555}>
                        <FormItem label="违规标签">
                            <Select
                                mode="tags"
                                placeholder="请输入对应违规标签"
                                defaultValue={self.state.Tag}
                                tokenSeparators={[',']}
                                onChange={(e) => {
                                    self.setState({ Tag: e },
                                        () => {
                                            //console.log('self.state.Tag',self.state.Tag)
                                        })
                                }
                                }
                                value={self.state.Tag}
                                style={{ width: '100%' }}
                            >
                                {this.state.TagList.map(item => {
                                    return <Option key={item} value={item}>{item}</Option>
                                })}
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={6} key={2} >
                        <FormItem style={{ width: '100%', marginLeft: "80px" }} label="">
                            <Button style={{ marginRight: "16px" }} onClick={this.handleSearch} htmlType="submit">查询</Button>
                            <Button style={{ marginRight: "16px" }} onClick={this.handleTask} htmlType="submit">生成任务</Button>
                            <Button onClick={() => {
                                window.location.reload();
                                // this.setState({
                                //     companyID: "",
                                //     Tag:[]
                                // })
                            }}>重置</Button>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        );
    }
    render() {
        const { loading, list, pagination, actionName } = this.state
        return (
            <Card bordered={false}>
                <div>
                    <Card title="搜索" style={{ display: this.state.expandForm ? 'none' : 'block', marginBottom: 24 }} bordered={false} >
                        {this.renderAdvancedForm()}
                    </Card>
                    <Tabs defaultActiveKey="TaskList">
                        <TabPane tab="结果" key="GetCopyTaskList">
                            <DataSearchComponent
                                loading={loading}
                                data={list}
                                pagination={pagination}
                                taskName={actionName}
                                onChange={this.handleTableChange}
                            />
                        </TabPane>
                    </Tabs>
                </div>
            </Card>
        )
    }
}

const DataSearch = Form.create()(DataList);
export default DataSearch;
