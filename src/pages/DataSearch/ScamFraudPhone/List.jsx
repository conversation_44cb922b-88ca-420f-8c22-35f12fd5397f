import React, { Component } from "react";
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  Table,
  Modal,
  message,
  Form,
  notification,
} from "antd";
import BatchForm from "./BatchForm";
import { DataSearchApi } from "../../../utils/request";
import exportFile from "../../../components/expoertFile/index";
import "./index.css";
import moment from "moment";
// const { RangePicker } = DatePicker;
const FormItem = Form.Item;
class ScamFraudPhoneList extends Component {
  state = {
    searchCompanyId: "",
    searchTime: [],
    batchList: [],
    batchTotal: 0,
    loading: false,
    pagination: { current: 1, pageSize: 10 },
    batchModalVisible: false,
    selectedBatch: null,
    detailModalVisible: false,
    detailList: [],
    detailTotal: 0,
    detailLoading: false,
    detailPagination: { current: 1, pageSize: 10 },
    noResultTip: "",
  };

  componentDidMount() {
    this.fetchBatchList();
  }

  fetchBatchList = (pagination) => {
    this.setState({ loading: true });
    const { searchCompanyId } = this.state;
    const { current, pageSize } = pagination || this.state.pagination;
    DataSearchApi("GetCheckIssuedPhoneBatchList", {
      // StartTime: searchTime[0] ? searchTime[0].valueOf() : undefined,
      // EndTime: searchTime[1] ? searchTime[1].valueOf() : undefined,
      CompanyId: searchCompanyId ? Number(searchCompanyId) : undefined,
      Limit: pageSize,
      Offset: (current - 1) * pageSize,
    }).then((res) => {
      if (res.RetCode === 0) {
        this.setState({
          batchList: res.checkIssuedPhoneBatchList || [],
          batchTotal: res.checkIssuedPhoneBatchListCount || 0,
          loading: false,
        });
      } else {
        message.error(res.Message || "查询失败");
        this.setState({ loading: false });
      }
    });
  };

  handleSearch = () => {
    this.setState(
      { pagination: { ...this.state.pagination, current: 1 } },
      () => {
        this.fetchBatchList({ ...this.state.pagination, current: 1 });
      }
    );
  };

  handleReset = () => {
    this.setState({ searchCompanyId: "", searchTime: [] }, this.handleSearch);
  };

  handleTableChange = (pagination) => {
    this.setState({ pagination }, () => {
      this.fetchBatchList(pagination);
    });
  };

  openBatchModal = () => {
    this.setState({ batchModalVisible: true });
  };

  closeBatchModal = (refresh) => {
    this.setState({ batchModalVisible: false });
    if (refresh) this.handleSearch();
  };

  openDetailModal = (batch) => {
    this.setState(
      { selectedBatch: batch, detailModalVisible: true },
      this.fetchDetailList
    );
  };

  closeDetailModal = () => {
    this.setState({
      detailModalVisible: false,
      detailList: [],
      selectedBatch: null,
      detailPagination: { current: 1, pageSize: 10 },
    });
  };

  fetchDetailList = (pagination) => {
    const { selectedBatch, detailPagination } = this.state;
    if (!selectedBatch) return;
    this.setState({ detailLoading: true });
    const { current, pageSize } = pagination || detailPagination;
    DataSearchApi("GetCheckIssuedPhoneRecordList", {
      BatchId: selectedBatch._id,
      Limit: pageSize,
      Offset: (current - 1) * pageSize,
    }).then((res) => {
      if (res.RetCode === 0) {
        this.setState({
          detailList: res.checkManagementPhoneRecordList || [],
          detailTotal: res.checkManagementPhoneRecordCount || 0,
          detailLoading: false,
        });
      } else {
        message.error(res.Message || "查询失败");
        this.setState({ detailLoading: false });
      }
    });
  };

  handleDetailTableChange = (pagination) => {
    this.setState({ detailPagination: pagination }, () => {
      this.fetchDetailList(pagination);
    });
  };
  handleExport = (selectedBatch) => {
    if (!selectedBatch) return;
    let result = [];
    DataSearchApi("GetCheckIssuedPhoneRecordList", {
      BatchId: selectedBatch._id,
      Limit: 1000,
      Offset: 0,
    }).then((res) => {
      if (res.RetCode === 0) {
        result = result.concat(res.checkManagementPhoneRecordList) || [];
        if (res.checkManagementPhoneRecordCount <= 1000) {
          this.downloadExcelFile(result, selectedBatch.BatchName);
        } else {
          let page = Math.ceil(res.checkManagementPhoneRecordCount / 1000);
          let promiseAll = [];
          for (let i = 1; i < page; i++) {
            promiseAll.push(
              DataSearchApi("GetCheckIssuedPhoneRecordList", {
                BatchId: selectedBatch._id,
                Limit: 1000,
                Offset: i * 1000,
              })
            );
          }
          Promise.all(promiseAll).then((res) => {
            result = result.concat(
              res
                .filter((item) => item.RetCode === 0)
                .map((item) => item.blockAccountExpiredRecordList || [])
            );
            this.downloadExcelFile(result, selectedBatch.BatchName);
          });
        }
      } else {
        message.error(res.Message || "下载失败");
      }
    });
  };
  downloadExcelFile = async (row = [], Id) => {
    try {
      if (row.length === 0) {
        notification["error"]({
          message: "无下载内容",
          description: "无下载内容",
        });
        return;
      }
      let dataList = row.reduce(
        (prev, item) => {
          prev.push([
            item.CompanyId || "",
            item.CompanyName || "",
            item.Phone || "",
            item.AccountStatus || "",
            moment(item.RegisterTime * 1000).format("YYYY-MM-DD HH:mm:ss") ||
              "",
          ]);
          return prev;
        },
        [["公司ID", "公司名称", "手机号", "账号状态", "注册时间"]]
      );
      let culumnWidthArray = [10, 20, 15, 20, 20];
      let fileName = "手机号注册查询-" + Id + ".xlsx";
      exportFile(dataList, culumnWidthArray, fileName);
    } catch (err) {
      notification["error"]({
        message: "当前下载任务信息失败",
        description: err.message || "内部错误",
      });
    }
  };
  //上部份，获取信息
  renderAdvancedForm() {
    return (
      <Form layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={144444}>
            <FormItem label="公司ID">
              <Input
                placeholder="请输入公司ID"
                value={this.state.searchCompanyId}
                onChange={(e) =>
                  this.setState({ searchCompanyId: e.target.value })
                }
              />
            </FormItem>
          </Col>
          {/* <Col span={10} key="searchTime">
            <FormItem label="时间">
              <RangePicker
                value={this.state.searchTime}
                onChange={(dates) => this.setState({ searchTime: dates })}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col> */}
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={6} key={2}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                style={{ marginRight: "16px" }}
                onClick={this.handleSearch}
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  this.handleReset();
                }}
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }
  render() {
    const {
      // searchCompanyId,
      // searchTime,
      batchList,
      batchTotal,
      loading,
      pagination,
      batchModalVisible,
      detailModalVisible,
      // selectedBatch,
      detailList,
      detailTotal,
      detailLoading,
      detailPagination,
      noResultTip,
    } = this.state;
    const columns = [
      { title: "批次名称", dataIndex: "BatchName", key: "BatchName" },
      {
        title: "批次状态",
        dataIndex: "Status",
        key: "Status",
        render: (val) => {
          return val === 0 ? "进行中" : val === 1 ? "已完成" : "执行出错";
        },
      },
      {
        title: "创建时间",
        dataIndex: "CreatedTime",
        key: "CreatedTime",
        render: (t) => (t ? new Date(t).toLocaleString() : ""),
      },
      {
        title: "批次详情",
        key: "detail",
        render: (_, record) =>
          record.Status !== 1 ? (
            <span>-</span>
          ) : (
            <a onClick={() => this.openDetailModal(record)}>查看</a>
          ),
      },
      {
        title: "导出Excel",
        key: "export",
        render: (_, record) => (
          <Button
            onClick={() => this.handleExport(record)}
            disabled={record.Status !== 1}
          >
            下载
          </Button>
        ),
      },
    ];
    return (
      <Card bordered={false} className="ban-resource-list">
        {this.renderAdvancedForm()}
        <Button type="primary" onClick={this.openBatchModal}>
          生成批次
        </Button>
        <Table
          rowKey="_id"
          columns={columns}
          dataSource={batchList}
          loading={loading}
          pagination={{
            ...pagination,
            total: batchTotal,
            showTotal: (total) => `共${total}条`,
          }}
          onChange={this.handleTableChange}
          locale={{
            emptyText: noResultTip ? (
              <span style={{ color: "red" }}>{noResultTip}</span>
            ) : (
              "暂无数据"
            ),
          }}
        />
        <Modal
          title="批次详情"
          visible={detailModalVisible}
          onCancel={this.closeDetailModal}
          footer={null}
          width={800}
          destroyOnClose={true}
        >
          <Table
            rowKey="_id"
            columns={[
              { title: "公司ID", dataIndex: "CompanyId", key: "CompanyId" },
              {
                title: "公司名称",
                dataIndex: "CompanyName",
                key: "CompanyName",
              },
              {
                title: "手机号",
                dataIndex: "Phone",
                key: "Phone",
              },
              {
                title: "账号状态",
                dataIndex: "AccountStatus",
                key: "AccountStatus",
              },
              {
                title: "注册时间",
                dataIndex: "RegisterTime",
                key: "RegisterTime",
                render: (t) => (t ? new Date(t * 1000).toLocaleString() : ""),
              },
            ]}
            dataSource={detailList}
            loading={detailLoading}
            pagination={{
              ...detailPagination,
              total: detailTotal,
              showTotal: (total) => `共${total}条`,
            }}
            onChange={this.handleDetailTableChange}
            locale={{ emptyText: "暂无数据" }}
          />
        </Modal>
        <BatchForm
          visible={batchModalVisible}
          onCancel={this.closeBatchModal}
          onOk={this.closeBatchModal}
        />
      </Card>
    );
  }
}

export default ScamFraudPhoneList;
