import React, { useState } from "react";
import { Modal, Upload, Icon, message } from "antd";
import request from "../../../utils/request";
const { Dragger } = Upload;
const BatchForm = (props) => {
  const [fileList, setFileList] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    if (!fileList || fileList.length === 0) return;
    let originFileObj = fileList[0].originFileObj;
    const base64Content = await new Promise((resolve) => {
      let reader = new FileReader();
      reader.readAsDataURL(originFileObj);
      reader.onload = function () {
        resolve(reader.result);
      };
    });
    // 检查文件类型是否为csv
    if (!originFileObj.name.toLowerCase().endsWith(".csv")) {
      message.error("请上传csv格式的文件");
      return;
    }
    setLoading(true);
    request("CheckManagementIssuedPhone", {
      csvContent: base64Content.slice(base64Content.indexOf(",") + 1),
    })
      .then((res) => {
        setLoading(false);
        if (res.RetCode === 0) {
          message.success("生成批次成功");
          props.onOk(true);
        } else {
          message.error(res.Message || "生成批次失败");
        }
      })
      .catch((err) => {
        setLoading(false);
        message.error(err.message || "生成批次失败");
      });
  };
  const handleChange = (info) => {
    let fileList = [...info.fileList];

    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    fileList = fileList.slice(-2);

    // 2. Read from response and show file link
    fileList = fileList.map((file) => {
      if (file.response) {
        // Component will show file.url as link
        file.url = file.response.url;
      }
      return file;
    });
    setFileList(fileList);
  };
  const handleCancel = () => {
    setLoading(false);
    props.onCancel();
  };
  const propsUpload = {
    onChange: handleChange,
    beforeUpload: () => {
      return false;
    },
  };
  return (
    <Modal
      title="生成批次"
      visible={props.visible}
      onCancel={handleCancel}
      onOk={handleOk}
      confirmLoading={loading}
      okText="确定"
      cancelText="取消"
      destroyOnClose={true}
    >
      <Dragger {...propsUpload}>
        <p className="ant-upload-drag-icon">
          <Icon type="inbox" />
        </p>
        <p className="ant-upload-text">点击或拖曳上传</p>
        <p className="ant-upload-hint">
          仅支持csv文件,列名必须包含【下发号码】
        </p>
      </Dragger>
    </Modal>
  );
};

export default BatchForm;
