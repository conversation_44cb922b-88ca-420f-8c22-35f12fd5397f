import React, { Component } from "react";
import {
  Breadcrumb,
  Card,
  Form,
  Descriptions,
  notification,
  Table,
  Button,
  Icon,
  Col,
  Input,
  Checkbox,
  message,
} from "antd";
import "./DataList.css";
import { Link } from "react-router-dom";
import { DataSearchApi } from "../../utils/request";
import moment from "moment";
import ImageViewer from "../../components/ImageViewer";
import JSZip from "jszip";
import fileSaver from "file-saver";
import base64 from "base64-js";
import exportFile from "../../components/expoertFile/index";
const FormItem = Form.Item;
const layout = {
  style: {
    marginLeft: 100,
    marginBottom: 100,
  },
};
let baseInformation = "";
let loginInformation = [];
let inpourInformation = [];
let CompanyId;
let isHuoti = false;
//获取url后面的参数
const getQueryString = (name) => {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r !== null) return unescape(r[2]);
  return null;
};
class DataSeachDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      companyId: getQueryString("CompanyId"),
      IDPhotoFrontURL: "",
      IDPhotoBackURL: "",
      BusinessLicenseURL: "",
      loading: false,
      TagList: ["公安取证", "自查发现"],
      getTag: [""],
      Remark: "",
      selfSetTag: "",
      relationResult: [],
      domainList: [],
    };
  }
  // 挂载前查询
  componentDidMount() {
    this.DescribeInfoEvidenceTask(this.state.companyId);
    this.GetTagList();
    this.downloadRelation();
  }
  GetTagList() {
    let self = this;
    //重发消息
    let action = "GetTagList",
      options = {
        Action: action,
      };
    DataSearchApi(action, options)
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode === 0) {
          self.setState({
            TagList: resp.TagList,
          });
          return;
        } else {
          message = resp.Message || resp.RetCode + "请求失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  }
  GetChannelInfo() {
    let self = this;
    //重发消息
    let action = "GetChannelInfo",
      options = {
        Action: action,
      };
    DataSearchApi(action, options)
      .then((resp) => {
        let message = "请求成功";
        self.setState({
          loading: false,
        });
        if (resp.RetCode === 0) {
          return;
        } else {
          message = resp.Message || resp.RetCode + "请求失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  }
  DescribeInfoEvidenceTask = (companyId) => {
    let self = this;
    //重发消息
    let action = "DescribeInfoEvidenceTask",
      options = {
        Action: action,
        CompanyId: parseInt(companyId),
      };
    self.setState({ loading: true });
    DataSearchApi(action, options)
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode === 0) {
          let CloselyInfo = [];
          if (resp.InfoEvidenceTaskList[0].CloselyInfo) {
            let arr = Object.entries(resp.InfoEvidenceTaskList[0].CloselyInfo);
            CloselyInfo = arr.map((item) => {
              item[1].companyId = item[0];
              return item[1];
            });
          }

          this.setState({
            data: resp.InfoEvidenceTaskList,
            loading: false,
            getTag: resp.InfoEvidenceTaskList[0].Tags || [],
            Remark: resp.InfoEvidenceTaskList[0].Remark || "",
            domainList: resp.InfoEvidenceTaskList[0].DomainInfo,
            CloselyInfo: CloselyInfo,
          });
          this.getPictureDownload(resp.InfoEvidenceTaskList[0].AuthInfo);
          return;
        } else {
          message = resp.Message || resp.RetCode + "请求失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  GetIdentityURL(AuthType, CertificateType, IsUnified) {
    let action = "GetIdentityURL";
    let self = this;
    let options = {
      CompanyId: parseInt(this.state.companyId),
      AuthType: AuthType,
      CertificateType: CertificateType,
      IsUnified: IsUnified,
    };
    DataSearchApi(action, options)
      .then((resp) => {
        let message =
          resp.RetCode === 0
            ? "请求成功"
            : (message = resp.Message || resp.RetCode + "请求失败");
        if (resp.RetCode === 0) {
          if (AuthType.indexOf("个人认证") > -1) {
            self.setState({
              IDPhotoFrontURL: resp.IDPhotoFrontURL,
              IDPhotoBackURL: resp.IDPhotoBackURL,
            });
          } else {
            self.setState({
              BusinessLicenseURL: resp.BusinessLicenseURL,
            });
          }
          return;
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  }
  // 面包屑
  breadcrumb() {
    return (
      <div>
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/DataSearch">数据查询</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>详情</Breadcrumb.Item>
        </Breadcrumb>
      </div>
    );
  }
  //上部份，获取信息
  renderAdvancedForm() {
    return (
      <Card title="用户信息" bordered={true}>
        {this.state.data && this.state.data.length > 0
          ? this.state.data.map((item) => {
              return this.descriptions(item);
            })
          : ""}
      </Card>
    );
  }
  submitTag = () => {
    try {
      //重发消息
      let action = "ModifyTagsAndRemark",
        selfSetTag = this.state.selfSetTag
          ? this.state.selfSetTag.replace(/\s*/g, "").split(",")
          : [],
        options = {
          Action: action,
          CompanyId: parseInt(this.state.companyId),
          Tags: this.state.getTag.concat(selfSetTag),
          Remark: this.state.Remark,
        };
      DataSearchApi(action, options)
        .then((resp) => {
          let message = "提交成功";
          if (resp.RetCode === 0) {
            window.location.reload();
          } else {
            message = resp.Message || resp.RetCode + "请求失败";
          }
          notification.open({
            message: message,
          });
        })
        .catch((err) => {
          // 报错
          notification["error"]({
            message: "请求失败",
            description: err.message || "内部错误",
          });
          return;
        });
    } catch (e) {
      console.log(e);
    }
  };
  renderTagForm = () => {
    const { TagList, getTag, selfSetTag, Remark, loading } = this.state;
    return (
      <Col span={24} key={154994}>
        <FormItem label="违规类型">
          <Checkbox.Group
            options={TagList}
            defaultValue={getTag}
            value={getTag}
            onChange={(e) => {
              this.setState(
                {
                  getTag: e,
                },
                () => {
                  console.log(getTag);
                }
              );
            }}
          />
        </FormItem>
        <FormItem label="自定义违规类型">
          <Input
            style={{ width: "100%" }}
            value={selfSetTag}
            placeholder="请输入自定义违规类型，如果是多个违规类型请用英文逗号,隔开"
            onChange={(e) => {
              this.setState({ selfSetTag: e.target.value });
            }}
          />
        </FormItem>
        <FormItem label="备注">
          <Input
            style={{ width: "100%" }}
            value={Remark}
            placeholder="请输入备注信息"
            onChange={(e) => {
              this.setState({ Remark: e.target.value });
            }}
          />
        </FormItem>
        <FormItem label="">
          <Button
            onClick={this.submitTag}
            style={{
              margin: "10px",
              display: loading === true ? "none" : "block",
            }}
          >
            确认提交
          </Button>
        </FormItem>
      </Col>
    );
  };
  renderPicture(AuthType) {
    let content = "";
    let { IDPhotoBackURL } = this.state;
    let IDPhotoFrontURL = (
      <div className="pictureContainer">
        {IDPhotoBackURL ? (
          <ImageViewer src={IDPhotoBackURL} name="证件照反面" />
        ) : null}
      </div>
    );
    if (AuthType.indexOf("个人认证") > -1) {
      content = (
        <div style={{ display: "flex", width: "fit-content" }}>
          <div className="pictureContainer" style={{ marginRight: "20px" }}>
            <ImageViewer src={this.state.IDPhotoFrontURL} name="证件照正面" />
          </div>
          {isHuoti ? "" : IDPhotoFrontURL}
        </div>
      );
    } else {
      content = (
        <div className="pictureContainer">
          <ImageViewer src={this.state.BusinessLicenseURL} name="营业执照" />
        </div>
      );
    }
    return content;
  }
  getPictureDownload(AuthInfo) {
    if (AuthInfo.length > 1) {
      AuthInfo.forEach((item) => {
        this.GetIdentityURL(
          item.AuthType,
          item.CertificateType,
          item.IsUnified
        );
      });
    } else {
      let authInfo = AuthInfo[0];
      this.GetIdentityURL(
        authInfo.AuthType,
        authInfo.CertificateType,
        authInfo.IsUnified
      );
    }
  }
  dataToCsv(dataList) {
    if(Array.isArray(dataList)){
      dataList = dataList.join('');
    }
    let blob = new Blob(["\ufeff", dataList], { type: "text/csv,charset=UTF-8" });
    return blob;
  }
  async GetPictureBase64(url) {
    if(!url) return ''
    let result = "";
    //重发消息
    let action = "GetPictureBase64",
      options = {
        Action: action,
        PictureURL: url,
      };
    await DataSearchApi(action, options)
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode === 0) {
          result = resp.Content;
          return;
        } else {
          message = resp.Message || resp.RetCode + "请求失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
    return result;
  }
  download() {
    let self = this;
    try {
      if (baseInformation && loginInformation) {
        let zip = new JSZip();
        let baseName = "基本信息.csv",
          historyName = "历史登陆信息.csv",
          inpourName = "充值记录.csv";
        zip.file(baseName, self.dataToCsv(baseInformation));
        zip.file(historyName, self.dataToCsv(loginInformation));
        zip.file(inpourName, self.dataToCsv(inpourInformation));

        let promise = [];
        promise.push(self.GetPictureBase64(this.state.IDPhotoFrontURL));
        promise.push(self.GetPictureBase64(this.state.IDPhotoBackURL));
        promise.push(self.GetPictureBase64(this.state.BusinessLicenseURL));

        Promise.all(promise).then((data) => {
          data[0] && zip.file("身份证正面.png", base64.toByteArray(data[0]));
          data[1] &&
            zip.file("身份证反面.png", base64.toByteArray(data[1]));
          data[2] && zip.file("证件照.png", base64.toByteArray(data[2]));
          zip.generateAsync({ type: "blob" }).then(function (content) {
            fileSaver.saveAs(content, CompanyId + "资料包");
          });
        });
      }
    } catch (e) {
      console.log("e", e);
      notification.open({
        message: "下载出错",
        description: e,
      });
    }
  }
  downloadRelation = () => {
    //重发消息
    let action = "FeatchRelevanceCompanyList",
      options = {
        Action: action,
        CompanyId: parseInt(this.state.companyId),
      };
    DataSearchApi(action, options)
      .then((resp) => {
        let message = "请求成功";
        if (resp.RetCode === 0) {
          this.setState({
            relationResult: resp.RelevanceCompanyList,
          });
          return;
        } else {
          message = resp.Message || resp.RetCode + "请求失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  // 基础信息,个人认证展示证件号证件图片，企业认证展示信用代码营业执照，两个认证都有都展示，个人认证-活体认证只展示身份证正面
  descriptions = (item) => {
    let content;
    let mainAccount = [];
    let adminArray = item.UserInfo.map((ite) => {
      if (ite.Admin === "是") {
        mainAccount.push(ite.UserEmail);
      }
      return ite;
    });
    let UserPhone = adminArray.length > 0 ? adminArray[0].UserPhone : "无";
    mainAccount = mainAccount.length > 0 ? mainAccount.join(",") : "无";
    //子账户邮箱
    let notAdminArray = [];
    item.UserInfo.forEach((it) => {
      if (it.Admin !== "是") {
        notAdminArray.push(it.UserEmail);
      }
    });
    notAdminArray = notAdminArray.length > 0 ? notAdminArray.join(",") : "无";
    let lastArray = item.AccessInfo[item.AccessInfo.length - 1];
    //判断是否是个人活体认证
    if (item.AuthInfo.length > 1) {
      item.AuthInfo[1].AuthMethod === "活体认证"
        ? (isHuoti = true)
        : (isHuoti = false);
    } else {
      item.AuthMethod === "活体认证" ? (isHuoti = true) : (isHuoti = false);
    }
    CompanyId = item.CompanyId;
    let authType =
        item.AuthType +
        "(" +
        item.AuthMethod +
        "-" +
        item.AuditState +
        ")" +
        (item.AuthInfo.length > 1
          ? "|" +
            item.AuthInfo[1].AuthType +
            "(" +
            item.AuthInfo[1].AuthMethod +
            "-" +
            item.AuthInfo[1].AuditState +
            ")"
          : ""),
      createTime = moment(item.Created * 1000).format("YYYY-MM-DD HH:mm:ss"),
      lastLoginTime = lastArray
        ? moment(lastArray.CreateTime * 1000).format("YYYY-MM-DD HH:mm:ss")
        : "",
      identityNo =
        item.AuthInfo.length > 1
          ? item.AuthInfo[1].IdentityNo
          : item.AuthInfo[0].IdentityNo,
      creditCode = item.AuthInfo[0].CreditCode || "",
      companyName =
        item.AuthType.indexOf("个人认证") > -1
          ? item.UserName
          : item.CompanyName,
      loginAddress = lastArray
        ? lastArray.CountryName +
          lastArray.RegionName +
          (lastArray.RegionName === lastArray.CityName
            ? ""
            : lastArray.CityName)
        : "",
      isPersonal =
        item.AuthType.indexOf("个人认证") > -1 || item.AuthInfo.length > 1,
      isCompony =
        item.AuthType.indexOf("企业认证") > -1 || item.AuthInfo.length > 1;
    if (item) {
      content = (
        <Descriptions title="">
          <Descriptions.Item label="公司Id">{item.CompanyId}</Descriptions.Item>
          <Descriptions.Item label="公司名称">{companyName}</Descriptions.Item>
          <Descriptions.Item label="联系人姓名">
            {item.UserName}
          </Descriptions.Item>
          <Descriptions.Item label="注册手机号">{UserPhone}</Descriptions.Item>
          <Descriptions.Item label="客户登陆IP">
            {lastArray ? lastArray.AccessIp : ""}
          </Descriptions.Item>
          <Descriptions.Item label="端口号">
            {lastArray ? lastArray.ClientPort : ""}
          </Descriptions.Item>
          <Descriptions.Item label="登陆地">{loginAddress}</Descriptions.Item>
          <Descriptions.Item label="最新登陆时间">
            {lastLoginTime}
          </Descriptions.Item>
          <Descriptions.Item label="认证类型">{authType}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{createTime}</Descriptions.Item>
          <Descriptions.Item label="所属渠道">{item.Channel}</Descriptions.Item>
          <Descriptions.Item label="主账号邮箱">
            {mainAccount}
          </Descriptions.Item>
          {isPersonal ? (
            <Descriptions.Item label="证件号">{identityNo}</Descriptions.Item>
          ) : (
            ""
          )}
          {isCompony ? (
            <Descriptions.Item label="信用代码">{creditCode}</Descriptions.Item>
          ) : (
            ""
          )}
          {isPersonal ? (
            <Descriptions.Item label="证件图片">
              {this.renderPicture(
                item.AuthInfo.length > 1 ? "个人认证" : item.AuthType
              )}
            </Descriptions.Item>
          ) : (
            ""
          )}
          {isCompony ? (
            <Descriptions.Item label="营业执照图片">
              {this.renderPicture(item.AuthType)}
            </Descriptions.Item>
          ) : (
            ""
          )}
        </Descriptions>
      );
      //生成csv文件数据
      let base =
        item.CompanyId +
        "," +
        companyName +
        "," +
        item.UserName +
        "," +
        UserPhone +
        "," +
        authType +
        "," +
        createTime +
        "," +
        item.Channel +
        "," +
        mainAccount +
        "," +
        notAdminArray +
        "," +
        identityNo +
        "," +
        creditCode;
      let baseColumn =
        "公司Id,公司名称,联系人姓名,注册手机号,认证类型,创建时间,所属渠道,主账号邮箱,子账户邮箱,证件号,信用代码\n";
      baseInformation = [baseColumn, base];
      let loginColumn = "登陆时间,客户登陆IP,端口号,登陆地\n";
      let loginData = "";
      if (loginInformation.length === 0) {
        loginInformation.push(loginColumn);
        item.AccessInfo.forEach((item) => {
          loginData =
            moment(item.CreateTime * 1000).format("YYYY-MM-DD HH:mm:ss") +
            "," +
            item.AccessIp +
            "," +
            item.ClientPort +
            "," +
            item.CountryName +
            item.RegionName +
            (item.RegionName === item.CityName ? "" : item.CityName) +
            "\n";
          loginInformation.push(loginData);
        });
      }
      if (
        inpourInformation.length === 0 &&
        item.InpourDetail
      ) {
        // 增加第一行，标题行
        inpourInformation.push(
          [
            "公司ID",
            // '内部充值交易号',
            "充值流水号",
            "金额（单位元）",
            "充值来源",
            "付款人",
            "银行卡号",
            // "开户行",
            "创建时间",
          ].join(",") + "\n"
        );
        item.InpourDetail.forEach((item) => {
          let inpourData =
            [
              item.CompanyId,
              // item.TradeNo,
              '" ' + item.SerialNo + ' "',
              '" ' + item.TradeAmount / 100 + ' "', // 默认单位为分，转成元
              { ALIPAY: "支付宝", BANKPAY: "银行卡转账", WXPAY: "微信" }[
                item.FromType
              ], // 支付方式
              item.PayAccountName,
              item.FromType === "BANKPAY"
                ? '" ' + item.PayAccountNo + ' "'
                : "空", // 非网银转账，银行卡号置为空
              //   item.PayBankName,
              moment(item.CreateTime * 1000).format("YYYY-MM-DD HH:mm:ss"),
            ].join(",") + "\n";
          inpourInformation.push(inpourData);
        });
      }
    }
    return content;
  };
  //复制关联信息到粘贴板
  copyRelation = () => {
    const { relationResult } = this.state;
    const input = document.createElement("input");
    document.body.appendChild(input);
    input.setAttribute("value", relationResult.join(","));
    input.select();
    document.execCommand("copy");
    document.body.removeChild(input);
    message.success("复制成功");
  };
  exportCloseLyInfo = () => {
    let { CloselyInfo, companyId } = this.state;
    let dataList = CloselyInfo.reduce(
      (prev, item) => {
        prev.push([
          item.companyId || "",
          item.CompanyName || "",
          item.Remark || "",
          item.Behavior || "",
        ]);
        return prev;
      },
      [["公司Id", "公司名称", "关联维度", "风险行为"]]
    );
    let culumnWidthArray = [8, 15, 15, 20];
    let fileName = `${companyId}_强关联账户.xlsx`;
    exportFile(dataList, culumnWidthArray, fileName);
  };
  render() {
    const columns = [
      {
        title: "IP",
        dataIndex: "IP",
      },
      {
        title: "归属地",
        dataIndex: "Zone",
      },
      {
        title: "域名",
        dataIndex: "Domain",
      },
      {
        title: "资源创建时间",
        dataIndex: "Created",
        render: (val) => (
          <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
    ];

    const relateAccountColumns = [
      {
        title: "公司Id",
        dataIndex: "companyId",
      },
      {
        title: "公司名称",
        dataIndex: "CompanyName",
        render: (val) => {
          if (!val) return "无";
          return val;
        },
      },
      {
        title: "关联维度",
        dataIndex: "Remark",
        render: (val,record) => {

          if (!val) return "无";
          // 关联维度增加打款人信息
          let index =  val.indexOf("打款账户")+4
          return record.PayAccountName?.length > 0 ? `${val.slice(0,index)}(${record.PayAccountName.join(',')})${val.slice(index)}` : val

        },
      },
      {
        title: "风险行为",
        dataIndex: "Behavior",
        render: (val) => {
          if (!val) return "无";
          return val;
        },
      },
    ];

    const { domainList, loading, CloselyInfo } = this.state;
    return (
      <Card bordered={false}>
        <div>
          <Card bordered={false}>{this.breadcrumb()}</Card>
          <Card bordered={false}>
            {this.renderAdvancedForm()}
            <div
              style={{
                display: this.state.loading === true ? "block" : "none",
              }}
            >
              <Icon type="loading" spin {...layout} />
            </div>
          </Card>
          <Card bordered={true} title="违规类型" style={{ margin: 20 }}>
            {this.renderTagForm()}
          </Card>
          <Card bordered={true} title="域名列表" style={{ margin: 20 }}>
            <Table
              loading={loading}
              rowKey={(record) => record._id}
              dataSource={domainList}
              columns={columns}
            />
          </Card>
          <Card bordered={true} title="关联账户" style={{ margin: 20 }}>
            <div className="flex-box">
              <h4>强关联账户（相同手机号、实名主体、打款账户）</h4>
              <Button onClick={this.exportCloseLyInfo}>导出</Button>
            </div>
            <Table
              loading={loading}
              rowKey={(record) => record._id}
              dataSource={CloselyInfo}
              columns={relateAccountColumns}
            />
            <h4 style={{ marginTop: 18 }}>一般关联账户（相同登录IP）</h4>
            <div style={{ wordWrap: "break-word" }}>
              {this.state.relationResult.join(",")}
            </div>
          </Card>
          <Card title="下载" bordered={true} style={{ margin: 20 }}>
            <div style={{ display: "flex" }}>
              <Button
                type="primary"
                onClick={this.download.bind(this)}
                style={{
                  marginLeft: "25px",
                  display: this.state.loading === true ? "none" : "block",
                }}
              >
                资料包
              </Button>
            </div>
          </Card>
        </div>
      </Card>
    );
  }
}

const DataSeachDetailForm = Form.create()(DataSeachDetail);
export default DataSeachDetailForm;
