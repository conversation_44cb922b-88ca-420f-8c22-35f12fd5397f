import React, { useState } from "react";
import { Modal, Form, Radio, Checkbox, message, Select } from "antd";
import request from "../../../utils/request";

const BatchForm = (props) => {
  const [scope, setScope] = useState("FullCheck");
  const [resourceTypes, setResourceTypes] = useState([1]);
  const [companyIds, setCompanyIds] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    if (resourceTypes.length === 0) {
      message.error("请选择资源类型");
      return;
    }
    setLoading(true);
    if (scope === "IncrementalCheck") {
      // 检查是否存在过期资源
      message.loading("检查是否存在过期资源中...");
      let checkRes = await request("CheckCompanyExpiredResourceIsExist", {
        CheckTYpe: resourceTypes,
        CompanyIds: companyIds,
      });
      if (checkRes.RetCode === 0) {
        if (checkRes.Message) {
          message.destroy();
          message.error(checkRes.Message);
          return;
        }
      }
    }
    request("GenerateCheckExpiredAccountProduct", {
      CheckScope: scope,
      CheckTYpe: resourceTypes,
      ...(scope === "IncrementalCheck" && { CompanyIds: companyIds }),
    }).then((res) => {
      setLoading(false);
      if (res.RetCode === 0) {
        message.success("生成批次成功");
        props.onOk(true);
      } else {
        message.error(res.Message || "生成批次失败");
      }
    });
  };

  const handleCancel = () => {
    setCompanyIds([]);
    setScope("FullCheck");
    setResourceTypes([1]);
    setLoading(false);
    props.onCancel();
  };

  return (
    <Modal
      title="生成批次"
      visible={props.visible}
      onCancel={handleCancel}
      onOk={handleOk}
      confirmLoading={loading}
      okText="确定"
      cancelText="取消"
      destroyOnClose={true}
    >
      <Form layout="inline" className="ant-advanced-search-form">
        <Form.Item label="查询范围">
          <Radio.Group value={scope} onChange={(e) => setScope(e.target.value)}>
            <Radio value="FullCheck">全量封号账号</Radio>
            <Radio value="IncrementalCheck">自定义</Radio>
          </Radio.Group>
        </Form.Item>
        {scope === "IncrementalCheck" && (
          <Form.Item label="公司ID">
            <Select
              mode="tags"
              style={{ width: "100%" }}
              value={companyIds}
              onChange={setCompanyIds}
              placeholder="请输入公司ID，多个ID用逗号分隔"
            />
          </Form.Item>
        )}
        <Form.Item label="资源类型">
          <Checkbox.Group
            value={resourceTypes}
            onChange={setResourceTypes}
            options={[
              { label: "云主机", value: 1 },
              { label: "EIP", value: 10 },
            ]}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BatchForm;
