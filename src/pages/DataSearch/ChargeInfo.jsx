import React, { Component } from "react";
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Button,
  Tabs,
  notification,
  Select,
  message,
  Table,
  Tooltip,
} from "antd";
import moment from "moment";
import "./DataList.css";
import { DataSearchApi } from "../../utils/request";
const { Option } = Select;
const { TabPane } = Tabs;
const FormItem = Form.Item;
//状态枚举,将英文状态转成中文在前端显示

class ChargeInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], // 列表初始值,
      loading: false,
      CompanyId: "",
      FromType: [],
      FromTypeMap: {
        ALIPAY: "支付宝",
        BANKPAY: "银行卡转账",
        WXPAY: "微信",
      },
      FromTypeList: ["ALIPAY", "BANKPAY", "WXPAY"],
      Status: 1,
      StatusList: [-1, 1, 2],
      StatusMap: {
        "-1": "全部充值记录",
        "1": "充值成功",
        "2": "充值异常",
      },
      pagination: {
        current: 1,
        pageSize: 100,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
      },
      selectType: "ListCompanyInpour", //查询类型
      sorter: {},
    };
  }
  // 获取充值记录详细信息
  ListCompanyInpour = async (CompanyId) => {
    let action = "ListCompanyInpour",
      options = {
        Limit: 100,
        Offset: 0,
      };
    if (CompanyId) {
      options["CompanyId"] = parseInt(CompanyId);
    } else {
      message.error("公司Id必填");
      return;
    }
    if (this.state.FromType && this.state.FromType.length) {
      options["FromType"] = this.state.FromType;
    }
    this.setState({ loading: true });
    try {
      let resp = await DataSearchApi(action, options);
      let message = "查询成功";
      if (resp.RetCode === 0) {
        let dataList = resp.Result.Data;
        if (resp.Result.Count <= 100) {
          if (resp?.Result?.Data) {
            let { current, pageSize } = this.state.pagination;
            resp.Result.Data.sort((a, b) => {
              return parseInt(b.CreateTime) - parseInt(a.CreateTime);
            });
            this.setState({
              list: resp.Result.Data,
              pagination: {
                current: current,
                pageSize: pageSize,
                total: resp.Result.Count,
              },
              loading: false,
            });
            return;
          }
        } else {
          for (let i = 1; i < Math.ceil(resp.Result.Count / 100); i++) {
            options = {
              ...options,
              Limit: 100,
              Offset: i * 100,
            };
            let resp = await DataSearchApi(action, options);
            if (resp.RetCode === 0) {
              if (resp?.Result?.Data) {
                resp.Result.Data.sort((a, b) => {
                  return parseInt(b.CreateTime) - parseInt(a.CreateTime);
                });

                dataList = dataList.concat(resp.Result.Data);
                if (i === (Math.ceil(resp.Result.Count / 100) - 1)) {
                  this.setState({
                    list: dataList,
                    pagination: {
                      current: 1,
                      pageSize: 20,
                      total: resp.Result.Count,
                    },
                    loading: false,
                  });
                }
              }
            }
          }
        }
      } else {
        message = resp.Message || resp.RetCode + "查询失败";
        this.setState({loading: false})
      }
      notification.open({
        message: message,
      });
    } catch (err) {
      // 报错
      this.setState({loading: false})
      notification["error"]({
        message: "请求失败",
        description: err.message || "内部错误",
      });
      return;
    }
  };
  // 查询网银充值
  ListCompanyBankInpour = async (CompanyId) => {
    let action = "ListCompanyBankInpour",
      options = { Status: this.state.Status, Limit: 100, Offset: 0 };
    if (CompanyId) {
      options["CompanyId"] = parseInt(CompanyId);
    } else {
      message.error("公司Id必填");
      return;
    }
    this.setState({ loading: true });
    let dataList;
    try {
      let resp = await DataSearchApi(action, options);
      let message = "查询成功";
      if (resp.RetCode === 0) {
        dataList = resp.Result.Infos;
        if (resp.Result.Count <= 100) {
          if (resp?.Result?.Infos) {
            resp.Result.Infos.sort((a, b) => {
              return parseInt(b.CreateTime) - parseInt(a.CreateTime);
            });
            this.setState({
              list: resp.Result.Infos,
              loading: false,
            });
            return;
          }
        } else {
          for (let i = 1; i < Math.ceil(resp.Result.Count / 100); i++) {
            options = {
              ...options,
              Limit: 100,
              Offset: i * 100,
            };
            let resp = await DataSearchApi(action, options);
            if (resp.RetCode === 0) {
              if (resp?.Result?.Infos) {
                // resp.Result.Infos.sort((a, b) => {
                //   return parseInt(b.CreateTime) - parseInt(a.CreateTime);
                // });
                dataList = dataList.concat(resp.Result.Infos);

                if (i === (Math.ceil(resp.Result.Count / 100) - 1)) {
                  this.setState({
                    list: dataList,
                    pagination: {
                      current: 1,
                      pageSize: 20,
                      total: resp.Result.Count,
                    },
                    loading: false,
                  });
                }
              }
            }
          }
        }
      } else {
        message = resp.Message || resp.RetCode + "查询失败";
        this.setState({loading: false})
      }
      notification.open({
        message: message,
      });
    } catch (err) {
      this.setState({loading: false})
      // 报错
      notification["error"]({
        message: "请求失败",
        description: err.message || "内部错误",
      });
    }
  };
  handleSearch = () => {
    const { selectType, CompanyId } = this.state;
    if (selectType === "ListCompanyInpour") {
      this.ListCompanyInpour(CompanyId);
    } else {
      this.ListCompanyBankInpour(CompanyId);
    }
  };
  handleTableChange = (pagination, filters, sorter) => {
    let oldSorter = this.state.sorter;
    console.log(pagination, filters, sorter);
    if (
      JSON.stringify(sorter) !== "{}" &&
      JSON.stringify(oldSorter) !== JSON.stringify(sorter.order)
    ) {
      pagination.current = 1;
    }
    this.setState({
      pagination: {
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: this.state.pagination.total,
      },
      sorter: sorter.order,
    });
  };
  resetField = () => {
    this.setState({
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
      },
      list: [],
      loading: false,
      CompanyId: "",
      FromType: [],
      Status: 1,
    });
  };
  //上部份，获取信息
  renderAdvancedForm() {
    let self = this;
    return (
      <Form layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={144444}>
            <FormItem label="公司ID">
              <Input
                style={{ width: "100%" }}
                value={this.state.CompanyId}
                placeholder="请输入对应的公司ID"
                onChange={(e) => {
                  this.setState({ CompanyId: e.target.value });
                }}
              />
            </FormItem>
          </Col>
          {self.state.selectType === "ListCompanyInpour" ? (
            <Col span={10} key={1555}>
              <FormItem label="充值来源">
                <Select
                  mode="multiple"
                  placeholder="请选择充值来源"
                  defaultValue={self.state.FromType}
                  tokenSeparators={[","]}
                  onChange={(e) => {
                    self.setState({ FromType: e });
                  }}
                  value={self.state.FromType}
                  style={{ width: "100%" }}
                >
                  {this.state.FromTypeList.map((item) => {
                    return (
                      <Option key={item} value={item}>
                        {this.state.FromTypeMap[item]}
                      </Option>
                    );
                  })}
                </Select>
              </FormItem>
            </Col>
          ) : (
            <Col span={10} key={1555}>
              <FormItem label="充值状态">
                <Select
                  defaultValue={self.state.Status}
                  onChange={(e) => {
                    self.setState({ Status: e });
                  }}
                  value={self.state.Status}
                  style={{ width: "100%" }}
                >
                  {this.state.StatusList.map((item) => {
                    return (
                      <Option key={item} value={item}>
                        {this.state.StatusMap[item]}
                      </Option>
                    );
                  })}
                </Select>
              </FormItem>
            </Col>
          )}
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={6} key={2}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                style={{ marginRight: "16px" }}
                onClick={this.handleSearch}
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  this.resetField();
                }}
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }

  render() {
    const { loading, list, pagination, FromTypeMap, selectType } = this.state;
    const columns1 = [
      {
        title: "内部充值交易号",
        dataIndex: "TradeNo",
      },
      {
        title: "充值流水号",
        dataIndex: "SerialNo",
      },
      {
        title: "金额(元)",
        dataIndex: "TradeAmount",
        render: (val) => {
          return parseInt(val) / 100;
        },
        // defaultSortOrder: 'descend',
        sorter: (a, b) => parseInt(a.TradeAmount) - parseInt(b.TradeAmount),
      },
      {
        title: "充值来源",
        dataIndex: "FromType",
        render: (val) => {
          return FromTypeMap[val];
        },
      },
      {
        title: "付款人 (或开户名）",
        dataIndex: "PayAccountName",
      },
      {
        title: "银行卡号",
        dataIndex: "PayAccountNo",
      },
      // {
      //   title: "开户行",
      //   dataIndex: "PayBankName",
      // },
      {
        title: "创建时间",
        dataIndex: "CreateTime",
        render: (val) => (
          <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
    ];
    const columns2 = [
      // {
      //   title: "交易日期",
      //   dataIndex: "TradeDate",
      // },
      // {
      //   title: "交易时刻",
      //   dataIndex: "TradeTime",
      // },
      // {
      //   title: "银行交易流水号",
      //   dataIndex: "BankTradeNo",
      //   // ellipsis: true,
      //   render:(val)=>{
      //     return <Tooltip title={val}>
      //     {val}
      //   </Tooltip>
      //   }
      // },
      {
        title: "银行账号ID",
        dataIndex: "BankAccountId",
        ellipsis: true,
        render: (val) => {
          return <Tooltip title={val}>{val}</Tooltip>;
        },
      },
      {
        title: "充值账号",
        dataIndex: "SubAccountNo",
        ellipsis: true,
        render: (val) => {
          return <Tooltip title={val}>{val}</Tooltip>;
        },
      },
      {
        title: "匹配银行子账号",
        dataIndex: "MatchNo",
      },
      {
        title: "充值金额(元)",
        dataIndex: "Amount",
        render: (val) => val / 100,
        // defaultSortOrder: 'descend',
        sorter: (a, b) => parseInt(a.Amount) - parseInt(b.Amount),
      },
      {
        title: "转账账号",
        dataIndex: "PayAccountNo",
        ellipsis: true,
        render: (val) => {
          return <Tooltip title={val}>{val}</Tooltip>;
        },
      },
      {
        title: "转账账户名称",
        dataIndex: "PayAccountName",
      },
      {
        title: "交易摘要",
        dataIndex: "Summary",
      },
      {
        title: "创建时间",
        dataIndex: "CreateTime",
        render: (val) => (
          <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
      {
        title: "更新时间",
        dataIndex: "UpdateTime",
        render: (val) => (
          <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
      {
        title: "备注",
        dataIndex: "Note",
      },
      {
        title: "内部充值流水号",
        dataIndex: "TradeNo",
        ellipsis: true,
        render: (val) => {
          return <Tooltip title={val}>{val}</Tooltip>;
        },
      },
      {
        title: "到账金额(元)",
        dataIndex: "OriginalAmount",
        render: (val, row) => {
          const currencyMap = {
            HKD: "港币",
            CNY: "人民币",
            USD: "美元",
          };
          return `${val / 100}(${currencyMap[row.Currency]})`;
        },
        // defaultSortOrder: 'descend',
        sorter: (a, b) =>
          parseInt(a.OriginalAmount) - parseInt(b.OriginalAmount),
      },

      // {
      //   title: "银行交易汇率(对比人民币)",
      //   dataIndex: "ExchangeRate",
      // },
      // {
      //   title: "汇率设置日期",
      //   dataIndex: "ExchangeRateTime",
      // },

      // {
      //   title: "到账手续费,人民币,单位为分",
      //   dataIndex: "Fee",
      // },
      // {
      //   title: "来源",
      //   dataIndex: "From",
      //   render: (val)=>{
      //     const FromMap = {
      //       1: "普通银行转账",
      //       2: "专属账号转账"
      //     }
      //     return FromMap[val];
      //   }
      // },
      // {
      //   title: "异常信息",
      //   dataIndex: "Exception",
      // },

      // {
      //   title: "操作人",
      //   dataIndex: "Operator",
      // },
    ];
    const onTabChange = (newTab) => {
      let CompanyId = this.state.CompanyId;
      this.resetField();
      this.setState({
        selectType: newTab,
        CompanyId,
      });
    };

    return (
      <Card bordered={false}>
        <div>
          <Card
            title="搜索"
            style={{
              display: this.state.expandForm ? "none" : "block",
              marginBottom: 24,
            }}
            bordered={false}
          >
            <Tabs defaultActiveKey="1" onChange={onTabChange}>
              <TabPane tab="查询全部充值记录" key="ListCompanyInpour" />
              <TabPane tab="查询网银充值" key="ListCompanyBankInpour" />
            </Tabs>
            {this.renderAdvancedForm()}
          </Card>
          <Tabs defaultActiveKey="TaskList">
            <TabPane tab="结果" key="GetCopyTaskList">
              <Table
                loading={loading}
                rowKey={(record) => record.TradeNo}
                dataSource={list}
                columns={
                  selectType === "ListCompanyInpour" ? columns1 : columns2
                }
                pagination={pagination}
                onChange={this.handleTableChange}
              />
            </TabPane>
          </Tabs>
        </div>
      </Card>
    );
  }
}

const ChargeInfoForm = Form.create()(ChargeInfo);
export default ChargeInfoForm;
