import React, { Component } from 'react';
import { Breadcrumb, notification, Select, Row, Col, DatePicker, Card, Form, Input, Button,Tabs  } from 'antd';
import PoliceRegisterTable from '../../components/PoliceCopyTable'
import './List.css';
import { policeCopy } from '../../utils/request'
// import Upload from 'rc-upload';
import moment from 'moment';
import { Link } from 'react-router-dom'
const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const Option = Select.Option;
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',');

//状态枚举,将英文状态转成中文在前端显示

class List extends Component {
  constructor(props) {
    super(props)
    this.state = {
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      loading: false,
      fileList: [],
      formValues: {},
      updateType: '1',
      actionName:'GetCopyTaskList'
    };
    this.fetch = this.fetch.bind(this)
  }

  // 查询列表
  fetch(options = {}) {
    //设置分页
    console.log("options",options)
    let action = this.state.actionName;
    options["Offset"] = (options.Page-1) * options.Limit;
    policeCopy(action, options)
      .then(resp => {
        if (resp.RetCode !== 0) {
          return Promise.reject({
            message: resp.Message
          })
        }
        this.setState({
          list: (action ==='GetCopyTaskList' ? ((resp.CopyTaskList || [])) : (resp.BackupTaskList || [])),
          pagination: {
            current: options.Page,
            pageSize: options.Limit,
            total: resp.TotalCount||0
          },
         loading: false
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        this.setState({
          loading: false,
          list: [],
          pagination: {
            current: 1,
            pageSize: 20,
            total: 0
          }
        })
        return;
    })
  }

  // 挂载前查询
  componentDidMount() {
    this.fetch({})
  }

  // 处理分页
  handleMessageTableChange(pagination, filtersArg) {
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      obj[key] = getValue(filtersArg[key]);
      return obj;
    }, {});

    const params = {
      Page: pagination.current,
      Limit: pagination.pageSize,
      ...filters,
    };
    this.fetch(params)
  }


  // 重置搜索框
  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();
    this.setState({
      formValues: {},
    });
    this.fetch()
  }

  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  }

  // 搜索
  handleSearch = (e) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
      };

      if (typeof (values.Time) === 'object') {
        let BeginTime, EndTime
        // 格式转换
        BeginTime = moment(values.Time[0]._d).startOf('day').format("X")
        EndTime = moment(values.Time[1]._d).endOf('day').format("X")

        values.BeginTime = parseInt(BeginTime, 10)
        values.EndTime = parseInt(EndTime, 10)

      }

      this.setState({
        formValues: values
      })

      delete values.Time
      delete values.Type
      //去搜索条件中的前后空格， 没写
      this.fetch(values)
    });
  }

  // 面包屑
  breadcrumb() {
    return (
      <div>
        <Breadcrumb>
          <Breadcrumb.Item><a href="/message">首页</a></Breadcrumb.Item>
          <Breadcrumb.Item><Link to="/PoliceCopy/BatchList">公安取证</Link></Breadcrumb.Item>
        </Breadcrumb>
      </div>
    );
  }

  // 更改上传文件的类型
  typeSelector(values) {
    this.setState({
      updateType: values
    })
  }

  //复杂搜索框
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    return (
      <Form onSubmit={this.handleSearch} layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={8} key={'CompanyId'} >
            <FormItem label="公司Id">
              {getFieldDecorator('CompanyId')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={'Committer'} >
            <FormItem label="提交人">
              {getFieldDecorator('Committer')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={'Operation'} style={{width:"20%"}}>
            <FormItem style={{ width: '100%' }} label="状态: ">
              <Select defaultValue="首次通知" style={{ width: "100%" }} onChange={this.typeSelector.bind(this)}>
                <Option value="Finished">完成</Option>
                <Option value="Error">异常</Option>
                <Option value="Wait">等待</Option>
                <Option value="Doing">进行中</Option>
                <Option value="Terminated">已终止</Option>
              </Select>
            </FormItem>
          </Col>

          {/* <Col span={6} key={'taskType'} style={{ overflow: 'hidden' ,width:"20%"}} >
            <FormItem style={{ width: '100%' }} label="类型">
            {getFieldDecorator('taskType')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col> */}
        <Col span={8} key={'Time'}  >
          <FormItem style={{ width: '100%' }} label="时间">
            {getFieldDecorator('Time')(
              <RangePicker onChange={this.timeSelector} />
            )}
          </FormItem>
        </Col>
        <Col span={16} >
        <FormItem style={{ width: '100%' }} label="">
          <Button style={{ marginRight: "16px" }} htmlType="submit" type='primary'>查询</Button>
          <Button style={{ marginRight: "16px" }} onClick={this.handleFormReset}>重置</Button>
          <Button style={{ marginRight: "16px" }} target="xxxx" href="BatchCreate" > 创建任务 </Button>
          <Button style={{ marginRight: "16px" }} target="xxxx" href="PoliceCreateCopyTask" > 创建备份 </Button>
        </FormItem>
        </Col>

        </Row>

      </Form>
    );
  }

  // 根据是否展开获取对应的表单形式
  renderForm() {
    return this.renderAdvancedForm()
  }
  //列表tab切
  getTable(key){
    this.setState({
      actionName:key
    },()=>{
      this.fetch()
    })
  }
  render() {
    let TableList = ()=>{
      return <PoliceRegisterTable
      loading={this.state.loading}
      data={this.state.list}
      pagination={this.state.pagination}
      taskName = {this.state.actionName}
      onChange={this.handleMessageTableChange.bind(this)}
     />
    }
    return (
      <Card bordered={false}>
        <div>
          <Card title="搜索" style={{ display: this.state.expandForm ? 'none' : 'block', marginBottom: 24 }} bordered={false} >
            {this.renderForm()}
          </Card>
          <div>
          <Tabs defaultActiveKey="TaskList" onChange={this.getTable.bind(this)}>
                <TabPane tab="拷贝列表" key="GetCopyTaskList">
                 <TableList/>
                </TabPane>
                <TabPane tab="备份列表" key="GetBackupTaskList">
                 <TableList/>
                </TabPane>
          </Tabs>
          </div>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(List);
export default ListForm;
