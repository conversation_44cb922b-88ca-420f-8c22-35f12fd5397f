import React, { Component } from 'react';
import { Breadcrumb, notification, Select, Row, Col, Card, Form, Input, Icon, Button, Transfer,Radio } from 'antd';
import './List.css';
import { policeCopy } from '../../utils/request'
import Upload from 'rc-upload';
import { Link } from 'react-router-dom'
import PoliceCreateTable from '../../components/PoliceCopyTable/create_task'
import _ from 'lodash'

const FormItem = Form.Item;
const Option = Select.Option;
const optionsList = [ '服务器本地盘', '备份盘' ]
//状态枚举,将英文状态转成中文在前端显示


class List extends Component {
  constructor(props) {
    super(props)
    this.state = {
      sourceSelect: optionsList[0],
      IsBackup: false ,
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      taskList: [],
      zoneList: [],
      loading: false,
      fileList: [],
      DiskCopyTaskList: [

      ],
      TargetDiskList: [],
      targetKeys: [],
      formValues: {},
      updateType: '1',
      taskName:"创建任务"
    };
    // this.getTargetDiskList = this.getTargetDiskList.bind(this)

  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  };

  handleChange(value) {
    console.log(`selected ${value}`);
  }

  handleChanget = targetKeys => {
    this.setState({ targetKeys });
  };

  // reload刷新数据用
  getTargetDiskList = () => {
    // 取信息
    let params = this.props.form.getFieldsValue()

    policeCopy('GetTargetDiskList', { Keyword: params.Requestor, ZoneId: parseInt(params.Zone,10) })
      .then(resp => {


        if (resp.RetCode !== 0) {
          return Promise.reject({
            message: resp.Message
          })
        }
        let list = []

        resp.TargetDiskList.forEach(element => {
          list.push({
            key: element.Id,
            title: element.IP + element.Path,
            description: element.Capacity,
            dataSet: element,
            chosen: Math.random() * 2 > 1,
          })
        });

        this.setState({ TargetDiskList: list });

      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })
        return;
      })


    // this.setState({ TargetDiskList,targetKeys });
  }

  getZoneList() {

    policeCopy('GetZoneList', {})
      .then(resp => {

        // 太多了，还是过滤下CN吧
        let zoneList = resp.ZoneList
        // 'cn-xj-01'.indexOf('cn') === 0
        zoneList = _.filter(zoneList, function (o) { return o.Name.indexOf('cn') === 0 })

        this.setState({ zoneList: zoneList });
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })


        return;
      })
  }

  // 挂载前查询
  componentDidMount() {
    this.getZoneList({});
  }


  handleLogoUpload(batchFile) {
    //文件上传，使用FileReader读文件
    let reader = new FileReader();

    reader.readAsDataURL(batchFile);
    reader.onloadend = () => {
      //处理loadend事件。该事件在读取操作结束时（要么成功，要么失败）触发
      let fileBase64 = reader.result.split(",")[1]
      //reader.result中存放Base64编码,需要对原数据做处理，导入数据格式"data:text/csv;base64,aXAs5Z+f5ZCNLA0KMS4xLjEuMSxhdmkucWlhbmp1bnllLmNvbSwNCjIuMi4yLjIsYXYucWlhbmp1bnllLmNvbSwNCg=="
      policeCopy('CreatePoliceRegisterBatch', {
        File: fileBase64,
        Description: batchFile.name,
        Type: Number(this.state.updateType),
      })
        .then(resp => {
          if (resp.RetCode !== 0) {
            notification['error']({
              message: '上传失败',
              description: resp.Message || '内部错误'
            })
          }
          this.fetch({})
        })
        .catch(err => {
          // 报错
          // message.info("上传失败", err)
          notification['error']({
            message: '上传失败',
            description: err.message || '内部错误'
          })
        })
    };
    //Prevent file uploading
    return false;
  }

  // 重置搜索框
  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();
    this.setState({
      formValues: {},
    });

    this.fetch()
  }

  handleFormSearch = () => {
    const { form } = this.props;

    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
      };

      // let keyId = _.uniq([this.state.targetKeys])
      // 取出TaskList
      let TargetDiskList = []
      this.state.TargetDiskList.forEach(element => {

        if (_.indexOf(this.state.targetKeys, element.key) !== -1) {
          TargetDiskList.push(element.dataSet)
        }
      });
      // let params = { Requestor: values.Requestor, HostIdList: values.ResourceList, TargetDiskList: values.TargetDiskList, IsBackup: values.IsBackup }
      // 开始预生成任务
      policeCopy('PrepareCopyTask', {
        Requestor: values.Requestor,
        TargetDiskList: TargetDiskList,
        HostIdList: values.ResourceList,
        IsBackup: this.state.IsBackup
      })
        .then(resp => {
          if (resp.RetCode !== 0) {
            return Promise.reject({
              message: resp.Message
            })
          }
          // 对结果做格式化
          let allocActionList = []

          resp.DiskAllocationList.forEach(element => {
            // 确定主机Id在不在
            let diskIndex = _.findIndex(allocActionList, { 'HostId': element.Disk.HostId })
            if (diskIndex === -1) {
              // 如果记录不存在
              allocActionList.push({
                CompanyId: element.Disk.CompanyId,
                Requestor: this.props.form.getFieldsValue().Requestor,
                HostId: element.Disk.HostId,
                Capacity: element.Disk.Capacity,
                DiskList: [{
                  DiskId: element.Disk.Id,
                  DiskName: element.Disk.Name,
                  Capacity: element.Disk.Capacity,
                  Type: element.Disk.Type,
                  TargetDiskCapacity: element.TargetDisk.Capacity,
                  TargetDiskIP: element.TargetDisk.IP,
                  TargetDiskId: element.TargetDisk.Id,
                  ZoneId: element.TargetDisk.ZoneId,
                  TargetDiskPath: element.TargetDisk.Path
                }]
              })
            } else {
              // 如果存在，总容量相关，DiskPush过去
              console.log(allocActionList[diskIndex], diskIndex, allocActionList, element.Disk.Capacity)
              allocActionList[diskIndex].Capacity = allocActionList[diskIndex].Capacity + element.Disk.Capacity
              allocActionList[diskIndex].DiskList.push({
                DiskId: element.Disk.Id,
                DiskName: element.Disk.Name,
                Capacity: element.Disk.Capacity,
                Type: element.Disk.Type,
                TargetDiskCapacity: element.TargetDisk.Capacity,
                TargetDiskId: element.TargetDisk.Id,
                TargetDiskIP: element.TargetDisk.IP,
                TargetDiskPath: element.TargetDisk.Path
              })
            }
          })

          this.setState(
            {
              taskList: allocActionList,
              loading: false
            });

        })
        .catch(err => {
          // 报错
          console.log(err)
          notification['error']({
            message: '预生成任务失败',
            description: err.message || '内部错误'
          })


          return;
        })




    });
    // form.resetFields();
    // this.setState({
    //   formValues: {},
    // });

    // this.fetch()
  }

  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  }

  // 面包屑
  breadcrumb() {
    return (
      <div>
        <Breadcrumb>
          <Breadcrumb.Item><a href="/message">首页</a></Breadcrumb.Item>
          <Breadcrumb.Item><Link to="/PoliceCopy/BatchList">公安取证</Link></Breadcrumb.Item>
        </Breadcrumb>
      </div>
    );
  }

  // 更改上传文件的类型
  typeSelector(values) {
    this.setState({
      updateType: values
    })
  }

  renderFooter = () => (
    <Button size="small" style={{ float: 'right', margin: 5 }} onClick={this.getTargetDiskList}>
      reload
    </Button>
  );

  //上部份，获取信息
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    const children = [];

    const formItemLayout = {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 }
    }
    function handleChange(value) {
      console.log(`selected ${value}`);
    }
    //选择是否是备份盘
    function resourceSelect(value){
      this.setState({
        sourceSelect: value.target.value,
        IsBackup:value.target.value === optionsList[0] ? false : true
      },()=>{
       // console.log(this.state.IsBackup)
      })
    }

    // 渲染可用区列表
    const optionChildren = [];

    for (let index = 0; index < this.state.zoneList.length; index++) {
      const element = this.state.zoneList[index]
      optionChildren.push(<Option value={element.Id.toString()}>{element.CName}</Option>)
    }
    return (
      <Form layout="inline" className="ant-advanced-search-form">

        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={3} /* style={{ display: 'block' }} */>
            <FormItem label="需求方">
              {getFieldDecorator('Requestor', {
                rules: [{ required: true, message: '需求方' }],
              })(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row >
          <Col span={10} key={4} /* style={{ display: 'block' }} */>
            <FormItem  {...formItemLayout} label="资源列表">
              {/* tokenSeparators 支持分隔符 */}
              <div className="sourcesSelect">
              <Radio.Group options={optionsList} defaultValue={optionsList[0]} onChange={resourceSelect.bind(this)} />
              </div>
              {getFieldDecorator('ResourceList', {
                rules: [{ required: true, message: '资源列表' }],
              })(<Select mode="tags" style={{ width: '100%' }} onChange={handleChange} tokenSeparators={[',', ' ', '\t', '\n']}>
                {children}
              </Select>)}
            </FormItem>

          </Col>
          <Col span={10} key={5}  style={{ display: 'block',paddingTop: 43 }}>
            <Upload
              action="temp/"
              listType="picture-card"
              fileList={this.state.fileList}
              beforeUpload={this.handleLogoUpload.bind(this)}
            >
              <Button>
                <Icon type="upload" /> 上传
                </Button>
            </Upload>

          </Col>
        </Row>


        <Row >
          <Col span={10}   /* style={{ display: 'block' }} */>
            <Form.Item label="目标盘">
              {/* </Form.Item>
            <Form.Item label="目标盘地址"> */}
              {getFieldDecorator('Zone', {
                rules: [{ required: true, message: '可用区' }],
              })(
                <Select placeholder="可用区">
                  {optionChildren}
                </Select>
              )}
              {getFieldDecorator('TargetDiskList', {
                rules: [{ required: true, message: '目标盘' }],
              })(
                <Transfer
                  dataSource={this.state.TargetDiskList}
                  showSearch
                  listStyle={{
                    width: 250,
                    height: 300,
                  }}
                  operations={['to right', 'to left']}
                  targetKeys={this.state.targetKeys}
                  onChange={this.handleChanget}
                  render={item => `${item.title}-${item.description}`}
                  footer={this.renderFooter}
                />
              )}
            </Form.Item>

          </Col>
          <Col span={6} key={1} /* style={{ display: 'block' }} */>


          </Col>
        </Row>

        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>

          <Col span={6} key={2} style={{ overflow: 'hidden' }} >

            <FormItem style={{ width: '100%', marginLeft: "127px" }} label="">
              <Button style={{ marginRight: "50px" }} onClick={this.handleFormSearch} htmlType="submit">查询</Button>
              <Button  onClick={this.handleFormReset}>重置</Button>

            </FormItem>
          </Col>

        </Row>
      </Form>
    );
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    })
  };


  render() {
    return (
      <Card bordered={false}>
        <div>
          <Card bordered={false}>
            {this.breadcrumb()}
          </Card>

          <Card title="创建任务" style={{ display: this.state.expandForm ? 'none' : 'block', marginBottom: 24 }} bordered={false} >
            {this.renderAdvancedForm()}
          </Card>

          <Card title="验证信息" style={{ display: this.state.expandForm ? 'none' : 'block', marginBottom: 24 }} bordered={false} >
            <PoliceCreateTable
              loading={this.state.loading}
              data={this.state.taskList}
              pagination={this.state.pagination}
              IsBackup={this.state.IsBackup} 
              taskName = {this.state.taskName}
            // onChange={this.handleMessageTableChange.bind(this)}
            />
          </Card>

        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(List);
export default ListForm;
