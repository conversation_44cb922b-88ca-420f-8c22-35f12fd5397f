import React, { Component } from 'react';
import { <PERSON> } from 'react-router-dom'
import { Breadcrumb, notification, Row, Col, Card, Form, Button, Popconfirm, Descriptions, Table, Icon, Tooltip } from 'antd';
// import PoliceRegisterDetailTable from '../../components/PoliceRegisterDetailTable'
import './List.css';
import { policeCopy } from '../../utils/request'
import exportFile from '../../components/expoertFile/index'
const defaultActionName = 'GetCopyTaskList'
class BatchDetail extends Component {
  state = {
    loading: false,
    selectedRowKeys: [],
    selectedIdList: [],
    TaskDetail: [],
    DiskCopyTaskList: [],
    taskId: this.props.match.params.Id,
    batchStatus: "",
    batchInfo: {},
    batchInfoLoading: false,
    allSendSuccess: true,
    expandForm: false, // 是否展开
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    },
    Progress: 0,
    TargetDiskCapacity: 0,
    taskName: this.props.location.search.split('taskName=')[1]
  };

  // 查询列表
  async fetch(options = {}) {
    try {
      //备份盘任何详情和拷贝任务详情分别调取不同的接口，取各自的值，拷贝接口：'GetCopyTaskDetail' ，备份接口： 'GetBackupTaskDetail'
      let detailActionName = this.state.taskName === defaultActionName ? 'GetCopyTaskDetail' : 'GetBackupTaskDetail'
      let infos = await policeCopy(detailActionName, options)
      // 取磁盘详情
      let diskCopyList = []
      //拷贝盘
      if(this.state.taskName === defaultActionName ){
        infos.CopyTask.DiskCopyTaskList.forEach(element => {
          diskCopyList.push({
            Id: element.Id,
            CompanyId: element.Disk.CompanyId,
            HostId: element.Disk.HostId,
            DiskId: element.Disk.Id,
            MD5: element.MD5,
            DiskName: element.Disk.Name,
            DiskCapacity: element.Disk.Capacity,
            TargetDiskPath: element.TargetDisk.Path,
            Progress: element.Progress,
            Status: element.Status,
           // IsBackup: element.IsBackup
          })
        });
        this.setState({
          TaskDetail: infos.CopyTask,
          DiskCopyTaskList: diskCopyList,
          Progress: infos.CopyTask.Progress ,
          TargetDiskCapacity:  infos.CopyTask.TargetDiskCapacity
        })
      }else{
        //备份盘
        infos.BackupTask.DiskBackupTaskList.forEach(element => {
          diskCopyList.push({
            Id: infos.BackupTask.Id,
            CompanyId: element.Disk.CompanyId,
            HostId: element.Disk.HostId,
            DiskId: element.Disk.Id,
            MD5: element.MD5,
            DiskName: element.Disk.Name,
            DiskCapacity: element.Disk.Capacity,
            TargetDiskPath: element.FileName,
            Progress: element.Progress,
            Status: element.Status,
           // IsBackup: element.IsBackup
          })
        });
        this.setState({
          TaskDetail: infos.BackupTask,
          DiskCopyTaskList: diskCopyList,
          Progress:  infos.BackupTask.Progress,
          TargetDiskCapacity: infos.BackupTask.DiskCapacity
        },()=>{
          //console.log(this.state)
        })
      }
    } catch (err) {
      notification['error']({
        message: '获取当前批次信息失败',
        description: err.message || '内部错误'
      })
    }
  }

  async retryTask(options = {}) {
    // 初始化options
    try {
      if (options.IdList.length === 0) {
        notification['error']({
          message: '未选择任务',
          description: '未选择任务'
        })
        return
      }
      let retryAction = this.state.taskName===defaultActionName?"RetryDiskCopyTask":"RetryDiskBackupTask"
      let result = await policeCopy(retryAction, options)
      // 取磁盘详情
      if (result.RetCode === 0) {
        notification['success']({
          message: '重试任务成功',
          description: "重试任务成功"
        })

        this.fetch({ "Id": this.props.match.params.Id })
      } else {
        notification['error']({
          message: '重试任务失败',
          description: result.Message
        })
      }

    } catch (err) {
      notification['error']({
        message: '获取当前批次信息失败',
        description: err.message || '内部错误'
      })
    }
  }
  //下载excel
  async downloadExcelFile(options = {}) {
    try {
      if (options.IdList.length === 0) {
        notification['error']({
          message: '无下载内容',
          description: '无下载内容'
        })
        return
      }
      let dataList = options.IdList.reduce((prev, item) => {
        prev.push([
          item.CompanyId || '',
          item.HostId || '',
          item.DiskId || '',
          item.DiskName || '',
          item.DiskCapacity || '',
          item.MD5 || '',
          item.TargetDiskPath || '',
          item.Progress || '',
          item.Status || '',
        ]);
        return prev;
      }, [["公司Id", "云主机资源Id", "数据盘Id", "资源名称", "容量", "MD5", "目标地址", "进度", "状态"]]);
      let culumnWidthArray = [10, 20, 20, 30, 10, 40, 10, 10];
      let fileName = '公安取证' + this.state.TaskDetail.Id + ".xlsx";
      exportFile(dataList, culumnWidthArray, fileName);
    } catch (err) {
      notification['error']({
        message: '当前下载任务信息失败',
        description: err.message || '内部错误'
      })
    }
  }


  start = () => {
    this.setState({ loading: true });
    // ajax request after empty completing
    setTimeout(() => {
      this.setState({
        selectedRowKeys: [],
        loading: false,
      });
    }, 1000);
  };

  onSelectChange = selectedRowKeys => {
    // 从列表中取需要重试的值
    let selectedIdList = []
    selectedRowKeys.map(index => (
      selectedIdList.push(this.state.DiskCopyTaskList[index].Id)
    ))
    this.setState({ selectedRowKeys, selectedIdList });
  };

  columns = [{
    title: '任务Id',
    dataIndex: 'Id',
    key: "Params",
    render: (value) => {
      if (value && (value.length > 5)) {
        return (
          <Tooltip title={value} >
            <span>{value.substring(0, 5) + '..'}</span>
          </Tooltip>
        )
      } else {
        return (
          <span>{value}</span>
        )
      }
    }
  }, {
    title: '公司Id',
    dataIndex: 'CompanyId',
  }, {
    title: '云主机资源Id',
    dataIndex: 'HostId',
  }, {
    title: '数据盘Id',
    dataIndex: 'DiskId',
  }, {
    title: '资源名称',
    dataIndex: 'DiskName',
  }, {
    title: '容量',
    dataIndex: 'DiskCapacity',
  }, {
    title: 'MD5',
    dataIndex: 'MD5',
  }, {
    title: '目标地址',
    dataIndex: 'TargetDiskPath',
  }, {
    title: '进度',
    dataIndex: 'Progress',
  }, {
    title: '状态',
    dataIndex: 'Status',
  }, {
    title: '操作',
    render: (val, row) => {
      return (
        <span>
          <Popconfirm title="是否确认重新执行任务？" loading={this.state.loading} onConfirm={() => this.retryTask({ IdList: [row.Id] })}>
            <Button   >
              重试任务
              </Button>
          </Popconfirm>
        </span>
      )
    },
  },
  ];


  // 挂载前查询
  componentDidMount() {
    this.fetch({ "Id": this.props.match.params.Id })
  }

  // 导航
  breadcrumb() {
    return (
      <Row>
        <Col xs={24} sm={12} md={12} lg={12}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/PoliceCopy/BatchList">公安取证</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              公安取证详情
            </Breadcrumb.Item>
          </Breadcrumb>
        </Col>
      </Row>
    );
  }

  // 基础信息
  descriptions() {
    return (
      <Descriptions title="基础信息">
        <Descriptions.Item label="任务Id">{this.state.TaskDetail.Id}</Descriptions.Item>
        <Descriptions.Item label="需求方">{this.state.TaskDetail.Requestor}</Descriptions.Item>
        {/* <Descriptions.Item label="目标盘类型">{this.state.TaskDetail.Id}</Descriptions.Item>
        <Descriptions.Item label="目标盘地址">{this.state.TaskDetail.Id}</Descriptions.Item>
        <Descriptions.Item label="目标盘容量">{this.state.TaskDetail.Id}</Descriptions.Item> */}
        <Descriptions.Item label="数据容量">{this.state.TaskDetail.DiskCapacity}</Descriptions.Item>
        <Descriptions.Item label="备注">
          {this.state.TaskDetail.Note}
        </Descriptions.Item>
      </Descriptions>
    );
  }

  pace() {
    if(this.state.taskName===defaultActionName){
      return (
        <Descriptions title="迁移进度">
          <Descriptions.Item label="迁移进度">{this.state.Progress}</Descriptions.Item>
          <Descriptions.Item label="目标盘容量">{this.state.TargetDiskCapacity}G</Descriptions.Item>
        </Descriptions>
      );
    }
    return (
      <Descriptions title="备份进度">
        <Descriptions.Item label="备份进度">{this.state.Progress}</Descriptions.Item>
        <Descriptions.Item label="备份盘容量">{this.state.TargetDiskCapacity}G</Descriptions.Item>
      </Descriptions>
    );
  }
  bulkRetry() {
    return (
      <Popconfirm title="是否确认批量重试？" onConfirm={() => this.retryTask.bind(this)({ IdList: this.state.selectedIdList })}>
        <Button type="primary"   >
          {'批量重试'}
        </Button>
      </Popconfirm>
    )

  }
  downloadFile() {
    return (
      <Button type="download" className="downloadFile" onClick={() => this.downloadExcelFile.bind(this)({ IdList: this.state.DiskCopyTaskList })} ><Icon type="download" />
          下载
      </Button>
    )
  }


  render() {
    const { selectedRowKeys } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    // const hasSelected = selectedRowKeys.length > 0;
    return (
      <Card bordered={false}>
        <div>
          <Card bordered={false}>
            {this.breadcrumb()}
          </Card>

          <Card title="任务详情" style={{ marginBottom: 24 }} bordered={false} >
            {this.descriptions()}
          </Card>

          <Card style={{ marginBottom: 24 }} bordered={false} >
            {this.pace()}

          </Card>

          <Card bordered={false} >
            {this.bulkRetry()}
            {this.downloadFile()}
            <Table rowSelection={rowSelection} columns={this.columns} dataSource={this.state.DiskCopyTaskList} />

          </Card>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(BatchDetail);
export default ListForm;
