import React, { Component } from 'react';
import { Breadcrumb, notification, Select, Row, Col, Card, Form, Input, Icon, Button } from 'antd';
import './List.css';
import { policeCopy } from '../../utils/request'
import Upload from 'rc-upload';
import { Link } from 'react-router-dom'
import _ from 'lodash'

const FormItem = Form.Item;
const Option = Select.Option;

//状态枚举,将英文状态转成中文在前端显示


class List extends Component {
  constructor(props) {
    super(props)
    this.state = {
      taskName: "创建备份任务",
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      taskList: [],
      zoneList: [],
      loading: false,
      fileList: [],
      targetKeys: [],
      formValues: {},
      updateType: '1',
      remark: ''
    };

  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  };

  handleChange(value) {
    console.log(`selected ${value}`);
  }

  handleChanget = targetKeys => {
    this.setState({ targetKeys });
  };


  getZoneList() {
    policeCopy('GetZoneList', {})
      .then(resp => {
        // 太多了，还是过滤下CN吧
        let zoneList = resp.ZoneList
        // 'cn-xj-01'.indexOf('cn') === 0
        zoneList = _.filter(zoneList, function (o) { return o.Name.indexOf('cn') === 0 })
        this.setState({ zoneList: zoneList });
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })
        return;
      })
  }

  // 挂载前查询
  componentDidMount() {
    this.getZoneList({});
  }


  handleLogoUpload(batchFile) {
    //文件上传，使用FileReader读文件
    let reader = new FileReader();
    reader.readAsDataURL(batchFile);
    reader.onloadend = () => {
      //处理loadend事件。该事件在读取操作结束时（要么成功，要么失败）触发
      let fileBase64 = reader.result.split(",")[1]
      //reader.result中存放Base64编码,需要对原数据做处理，导入数据格式"data:text/csv;base64,aXAs5Z+f5ZCNLA0KMS4xLjEuMSxhdmkucWlhbmp1bnllLmNvbSwNCjIuMi4yLjIsYXYucWlhbmp1bnllLmNvbSwNCg=="
      policeCopy('CreatePoliceRegisterBatch', {
        File: fileBase64,
        Description: batchFile.name,
        Type: Number(this.state.updateType),
      })
        .then(resp => {
          if (resp.RetCode !== 0) {
            notification['error']({
              message: '上传失败',
              description: resp.Message || '内部错误'
            })
          }
          this.fetch({})
        })
        .catch(err => {
          // 报错
          // message.info("上传失败", err)
          notification['error']({
            message: '上传失败',
            description: err.message || '内部错误'
          })
        })
    };

    return false;
  }
  onChangeInput = (event) => {
    this.setState({
      remark: event.target.value
    })
  }
  handleForm = () => {
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const values = {
        ...fieldsValue,
      };
      // 开始预生成任务
      policeCopy('CreateBackupTask', {
        Requestor: values.Requestor,
        ZoneId: parseInt(values.Zone),
        HostIdList: values.ResourceList,
        remarkInformation: this.state.remark
      }).then(resp => {
        let message = resp.RetCode === 0 ? '正在生成任务中' : resp.Message || resp.RetCode + "生成失败"
        notification.open({
          message: message,
        });
        let taskId = resp.BackupTask.Id
        window.location.href = 'BatchDetail/' + taskId + '?taskName=GetBackupTaskList';
      })
        .catch(err => {
          // 报错
          notification['error']({
            message: '发送失败失败',
            description: err.message || '内部错误'
          })
          return;
        })
    });
  }
  // 面包屑
  breadcrumb() {
    return (
      <div>
        <Breadcrumb>
          <Breadcrumb.Item><a href="/message">首页</a></Breadcrumb.Item>
          <Breadcrumb.Item><Link to="/PoliceCopy/BatchList">公安取证</Link></Breadcrumb.Item>
        </Breadcrumb>
      </div>
    );
  }

  // 更改上传文件的类型
  typeSelector(values) {
    this.setState({
      updateType: values
    })
  }

  //上部份，获取信息
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    const children = [];
    const formItemLayout = {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 }
    }
    function handleChange(value) {
      console.log(`selected ${value}`);
    }
    // 渲染可用区列表
    const optionChildren = [];
    for (let index = 0; index < this.state.zoneList.length; index++) {
      const element = this.state.zoneList[index]
      optionChildren.push(<Option value={element.Id.toString()}>{element.CName}</Option>)
    }
    return (
      <Form layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={144444} /* style={{ display: 'block' }} */>
            <FormItem label="需求方">
              {getFieldDecorator('Requestor', {
                rules: [{ required: true, message: '需求方' }],
              })(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row >
          <Col span={10} key={13333333} /* style={{ display: 'block' }} */>
            <FormItem  {...formItemLayout} label="资源列表">
              {/* tokenSeparators 支持分隔符 */}
              {getFieldDecorator('ResourceList', {
                rules: [{ required: true, message: '资源列表' }],
              })(<Select mode="tags" style={{ width: '100%' }} onChange={handleChange} tokenSeparators={[',', ' ', '\t', '\n']}>
                {children}
              </Select>)}
            </FormItem>

          </Col>
          <Col span={10} key={1555555} style={{ display: 'block', marginTop: 5 }} >
            <Upload
              action="temp/"
              listType="picture-card"
              fileList={this.state.fileList}
              beforeUpload={this.handleLogoUpload.bind(this)}
            >
              <Button>
                <Icon type="upload" /> 上传
              </Button>
            </Upload>

          </Col>
        </Row>
        <Row >
          <Col span={10}   /* style={{ display: 'block' }} */>
            <Form.Item label="备份盘">
              {getFieldDecorator('Zone', {
                rules: [{ required: true, message: '可用区' }],
              })(
                <Select placeholder="可用区">
                  {optionChildren}
                </Select>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row >
          <Col span={11} style={{ display: 'block' }} >
            <Form.Item label="备注信息：" style={{ paddingRight: 67 }}>
              <Input type="text" style={{ width: '100%' }} onChange={this.onChangeInput} value={this.state.remark} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={6} key={2} style={{ overflow: 'hidden' }} >
            <FormItem style={{ width: '100%', marginLeft: "127px" }} label="">
              {/* <Button style={{ marginRight: "50px"  }} onClick={this.handleFormSearch} htmlType="submit">查询</Button>
              <Button style={{ marginRight: "16px" }} onClick={this.handleFormReset}>重置</Button> */}
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }
  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys, selectedRows)
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    })
  };
  render() {
    return (
      <Card bordered={false}>
        <div>
          <Card bordered={false}>
            {this.breadcrumb()}
          </Card>
          <Card title="创建备份任务" style={{ display: this.state.expandForm ? 'none' : 'block', marginBottom: 24 }} bordered={false} >
            {this.renderAdvancedForm()}
          </Card>
          <Button type="primary" onClick={this.handleForm.bind(this)} style={{ margin: 16 }}>
            生成备份
          </Button>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(List);
export default ListForm;
