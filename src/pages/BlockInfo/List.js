import React, { Component } from 'react'
import { notification, Row, Col, Card, Form, Input, Select, Drawer, Button ,Icon} from 'antd'
import IPBlockTable from '../../components/IPBlockTable'
import './List.css'
import request from '../../utils/request'

const FormItem = Form.Item
const { Option } = Select
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',')


class SearchForm extends Component {
	render() {
		const {handleSearch,handleFormReset} = this.props
		const { getFieldDecorator } = this.props.form
		return (
			<Form onSubmit={handleSearch} layout="inline" className="ant-advanced-search-form">
				<Row gutter={{ md: 20, lg: 40, xl: 90 }}>
					<Col md={8} sm={24}>
						<FormItem label="渠道">
							{getFieldDecorator('SelectChannel',	{
									// 默认为“全部”，方便日常处理使用
									initialValue: '0'
								})(
							
								<Select style={{ width: '100%' }}>
									<Option value="0">全部</Option>
									<Option value="1">云X</Option>
									<Option value="2">IP解绑</Option>
									<Option value="3">傲盾</Option>
								</Select>
							)}
						</FormItem>
					</Col>
					<Col md={8} sm={24}>
					<FormItem label="SelectIP">
							{getFieldDecorator('SelectIP')(
								<Input style={{ width: '100%' }} />
							)}
						</FormItem>
					</Col>
					<Col md={8} sm={24}>
						<div className="ant-row ant-form-item" style={{ marginTop: 4, display: 'inline-block', float: 'right' }}>
							<Button type="primary" htmlType="submit">查询</Button> 
							<Button style={{ marginLeft: 8 }} onClick={handleFormReset}>重置</Button>
						</div>
						</Col>
				</Row>
			</Form>
		)
	}
}
const SearchFormComponent = Form.create()(SearchForm)

class CreateForm extends Component{
	render(){
		const {getFieldDecorator} = this.props.form
		const {onSubmit,onClose} = this.props
		return (
			<Form layout="vertical" hideRequiredMark className="ant-advanced-search-form">
				<Row gutter={16}>
					<Col span={12}>
					<Form.Item label="IP">
							{getFieldDecorator('IP', {
								rules: [{ required: true, message: '请输入需要记录的违禁IP' }],
							})(<Input placeholder="请输入需要记录的违禁IP" />)}
						</Form.Item>
					</Col>

				</Row>
				<Row gutter={16}>
					<Col span={12}>
						<Form.Item label="渠道">
							{getFieldDecorator('Channel', {
								rules: [{ required: true, message: '请选择封禁渠道' }],
							})(
								<Select placeholder="请选择封禁渠道">
									<Option value="1">云X系统</Option>
									<Option value="2">IP解绑</Option>
									<Option value="3">傲盾</Option>
								</Select>
							)}
						</Form.Item>
					</Col>
				</Row>
				<Row>	
					<Col span={12}>
						<Form.Item label="违禁类型">
							{getFieldDecorator('ViolationType', {
								rules: [{ required: true, message: '请选择违禁类型' }],
							})(
								<Select placeholder="请选择违禁类型">
									<Option value="1">未备案</Option>
									<Option value="2">内容违规</Option>
									<Option value="3">运营商下发</Option>
									<Option value="4">未实名</Option>
								</Select>
							)}
						</Form.Item>
					</Col>
				</Row>
				<Row>
					<Col span={12}>
						<Form.Item label="备注">
							{getFieldDecorator('Remark', {
								rules: [
									{
										required: true,
										message: '请输入封禁相关信息',
									},
								],
							})(<Input.TextArea rows={4} placeholder="请输入封禁相关信息" />)}
						</Form.Item>
					</Col>
				</Row>
				<div
				style={{
					position: 'absolute',
					left: 0,
					bottom: 0,
					width: '100%',
					borderTop: '1px solid #e9e9e9',
					padding: '10px 16px',
					background: '#fff',
					textAlign: 'right',
				}}
				>

				<Button onClick={onClose} style={{ marginRight: 8 }}>
					取消
				</Button>

				<Button onClick={onSubmit} type="primary">
					提交
				</Button> 
				</div>
			</Form>
		)
	}
}
const CreateFormComponent = Form.create()(CreateForm)
class List extends Component {
	constructor(props) {
		super(props)
		this.state = {
			expandForm: false, // 是否展开
			formValues: { // 查询表单初始值
			},
			list: [], // 列表初始值
			pagination: {
				current: 1,
				pageSize: 20,
				total: 0
			},
			loading: false
		}
		this.handleFormReset = this.handleFormReset.bind(this)
		this.handleSearch = this.handleSearch.bind(this)

		this.fetch = this.fetch.bind(this)
		this.update = this.update.bind(this)
		this.create = this.create.bind(this)
	}

	// 更新信息
	update (options = {}){
		//重发消息
		let self = this
		
		// 统一设置分页或者报错
		const CHANNEL_LIST = {
			'云X': 1,
			'IP解绑': 2,
			'傲盾': 3
		}
		self.setState({ loading: true })

		let action =  options.Action
		delete options.Action
		// Loading Modal 
		options.Channel= CHANNEL_LIST[options.Channel]

		request(action, options)
			.then(resp => {

				let message = resp.RetCode === 0 ? '设置成功' : resp.Message || resp.RetCode + "设置失败"

				notification.open({
					message: message,
				});
				self.setState({
					loading: false
				})

			})
			.then(self.fetch({SelectIP:options.IP,SelectChannel:options.Channel}))
			.catch(err => {
				// 报错
				notification['error']({
					message: '设置失败',
					description: err.message || '内部错误'
				})
				return;
			})
	}

		
	// 查询列表
	fetch(options = {}) {

		// 统一设置分页或者报错
		let self = this

		// 初始化options
		options.IP = options.SelectIP
		if (options.SelectChannel !=="0" ) {
			options.Channel = parseInt(options.SelectChannel, 10) 
		}

		delete options.SelectIP
		delete options.SelectChannel
		// Loading Modal 
		self.setState({ loading: true })

		
		request('SelectIPBlockInfo', options)
			.then(resp => {

				self.setState({
					list: resp.Data || [],
					pagination: {
						current: options.Offset / options.Limit + 1,
						pageSize: options.Limit,
						total: resp.TotalCount || 0
					},
					loading: false
				})
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: '获取列表失败',
					description: err.message || '内部错误'
				})

				// 清空列表
				self.setState({
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				})
				return
			})
	}

	create(options = {}) {
		// 统一设置分页或者报错
		let self = this

		// 初始化options
		// Loading Modal 
		self.setState({ loading: true })
		options.Channel = parseInt(options.Channel, 10)
		options.ViolationType =parseInt(options.ViolationType, 10)

		request('CreateIPBlockInfo', options)
			.then(resp => {
				if (resp.RetCode === 0) {
					this.setState({
						visible: false,
					})
					 		notification['success']({
						 			message: '操作成功',
							})
				} 

				// 清空列表
			self.setState({
				loading: false,
				list: [],
				pagination: {
					current: 1,
					pageSize: 20,
					total: 0
				}
			})				
			})
			.catch(err => {
				// 报错
				notification['error']({
					message: '获取列表失败',
					description: err.message || '内部错误'
				})

				// 清空列表
				self.setState({
					loading: false,
					list: [],
					pagination: {
						current: 1,
						pageSize: 20,
						total: 0
					}
				})
			})
	}
	// 挂载前查询
	// componentDidMount() {
	// 	this.fetch({ AuditState: 'PENDING' })
	// }

	// 处理分页
	handleIPBlockTableChange(pagination, filtersArg) {
		const { formValues } = this.state

		const filters = Object.keys(filtersArg).reduce((obj, key) => {
			const newObj = { ...obj }
			newObj[key] = getValue(filtersArg[key])
			return newObj
		}, {})

		const params = {
			...formValues,
			...filters,
		}

		params.Offset = (pagination.current - 1) * pagination.pageSize
		params.Limit = pagination.pageSize

		this.fetch(params)
	}

	showDrawer = () => {
    this.setState({
      visible: true,
    });
  };

  onClose = () => {
    this.setState({
      visible: false,
    });
	};
	
	onSubmit = () => {
		// const { form } = this.props

		this.createForm.validateFields((err, fieldsValue) => {
			if (err) return

			const values = {
				...fieldsValue,
			}

			this.setState({
				formValues: values,
			})

			this.create(values)
		})
  };

	// 重置搜索框
	handleFormReset() {
		// const { form } = this.props
		// let self = this
		this.searchForm.resetFields()

		this.setState({
			formValues: {
				AuditState: 'PENDING',
				Offset: 0,
				Limit: 20
			},
		}, () => {
			// self.fetch(self.state.formValues)
		})
	}

	// 展开、收起搜索框
	toggleForm() {
		this.setState({
			expandForm: !this.state.expandForm,
		})
	}

	// 搜索
	handleSearch(e) {
		e.preventDefault()

		// const { form } = this.props

		this.searchForm.validateFields((err, fieldsValue) => {
			if (err) return
			const values = {
				...fieldsValue,
			}

			this.setState({
				formValues: values,
			})

			this.fetch(values)
		})
	}


	// handleSumbit(e) {
	// 	console.log(111111)
	// 	e.preventDefault()

	// 	// const { form } = this.props

	// 	this.searchForm.validateFields((err, fieldsValue) => {
	// 		if (err) return

	// 		const values = {
	// 			...fieldsValue,
	// 		}

	// 		this.setState({
	// 			formValues: values,
	// 		})

	// 		this.fetch(values)
	// 	})
	// }


	renderSimpleForm() {
		// const { getFieldDecorator } = this.props.form
		return (
			<div style={{display:"inline-block",width:'100%'}}>
								
				<Drawer
				title="生成新的记录"
				width={720}
				onClose={this.onClose}
				visible={this.state.visible}
				style={{
				overflow: 'auto',
				height: 'calc(100% - 108px)',
				paddingBottom: '108px',
				}}
				>
				<CreateFormComponent ref={_ref=>this.createForm=_ref} onClose={this.onClose} onSubmit={this.onSubmit}/>
				</Drawer>
				<SearchFormComponent ref={(_ref)=>this.searchForm = _ref} handleFormReset={this.handleFormReset} handleSearch={this.handleSearch}/>
				<div className="ant-row ant-form-item" style={{ marginTop: 4, display: 'inline-block', float: 'right' }}>
				<Button type="primary" onClick={this.showDrawer}>
				<Icon type="plus" /> 添加
				</Button>
				</div>
			</div>
	)
	}

	// 根据是否展开获取对应的表单形式
	renderForm() {
		return this.renderSimpleForm()
	}

	render() {
		return (
			<Card bordered={false}>
				<div>
					<div 	style={{display:'block'}}>
					{this.renderForm()}
					</div>
					<IPBlockTable
						loading={this.state.loading}
						updateInfo = {this.update}
						data={this.state.list}
						pagination={this.state.pagination}
						onChange={this.handleIPBlockTableChange.bind(this)}
					/>
				</div>
			</Card>
		)
	}
}

// const ListForm = Form.create()(List)

export default List

