// data-set 可以按需引入，除此之外不要引入别的包
import React from 'react';
import moment from 'moment'
import {
    Chart,
    Geom,
    Axis,
    Tooltip,
    Legend,
    Coord
} from "bizcharts";
import { Card, Row, Col, DatePicker, Spin, Divider } from 'antd';
import DataSet from '@antv/data-set';
import api from '../../utils/request';

const { RangePicker } = DatePicker

const map = {
    'days': 'YYYY-MM-DD',
    'weeks': 'YYYY-MM-DD',
    'months': 'YYYY-MM',
    'years': 'YYYY',
}
const modeMap = {
    'minutes': ['time', 'time'],
    'hours': ['time', 'time'],
    'days': ['date', 'date'],
    'weeks': ['date', 'date'],
    'months': ['month', 'month'],
    'years': ['year', 'year'],
}

class EmailProcessChart extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            step: 'days',
            format: map['days'],
            mode: modeMap['days'],
            isopen: false,
            value: [moment().subtract(7, 'days'), moment()],
            emailData: [],
            loading: false
        }
    }

    componentDidMount() {
        this.getEmailData()
    }

    getEmailData = () => {
        const { step, value } = this.state
        this.setState({ loading: true })
        

        let action = "GetEmailSumStatistics",
            options = {
                Action: {
                    Action: 'GetEmailSumStatistics',
                    StartTime: value[0].unix(),
                    EndTime: value[1].unix(),
                    Step: step
                },
            }
        api(action, options).then(res => {
            if (res.RetCode === 0) {
                const timeSeriesData = res.TimeSeriesStatistics || []
                // 转换数据格式以适配图表
                const chartData = []
                timeSeriesData.forEach(item => {
                   item.TypeStatistics.forEach(typeItem => {
                            chartData.push({
                                Time: item.Time,
                                Type: typeItem.Type,
                                Count: typeItem.Count,
                                TotalCount: item.TotalCount,
                                Percentage: typeItem.Percentage
                            })
                        })
                })
                this.setState({
                    emailData: chartData,
                    TimeSeriesStatistics: res.TimeSeriesStatistics,
                    TypeStatistics: res.TypeStatistics,
                    loading: false
                })
            } else {
                console.error('API返回错误:', res.data.Message || '未知错误')
                this.setState({ loading: false })
            }
        }).catch(err => {
            console.error('获取邮件数据失败:', err)
            this.setState({ loading: false })
        })
    }

    changeStep = (step) => {
        this.setState({
            step,
            format: map[step],
            mode: modeMap[step]
        }, () => {
            this.getEmailData()
        })
    }

    onPanelChange = (value) => {
        this.setState({
            value,
            isopen: false
        }, () => {
            this.getEmailData()
        })
    }

    render() {
        const { emailData, loading, TypeStatistics } = this.state
        let { step, format, mode, isopen, value } = this.state
        console.log("emailData",emailData)

        // 准备饼图数据
        const typeCountMap = {}
        let totalData = 0

        emailData.forEach(item => {
            if (typeCountMap[item.Type]) {
                typeCountMap[item.Type] += item.Count
            } else {
                typeCountMap[item.Type] = item.Count
            }
            totalData += item.Count
        })

        const pieData = TypeStatistics?.map(item => ({
            type: item.Type,
            value: item.Count,
            percentage: item.Percentage
        }))

        var ds = new DataSet();
        var dvForEmail = ds
            .createView("emailProcessData", {
                watchingStates: []
            })
            .source(emailData);

        dvForEmail
            .transform({
                type: "fold",
                fields: ['Count'],
                key: "processType",
                value: "count"
            })
            .transform({
                type: "map",
                callback: function (row) {
                    row.count = parseInt(row.count, 10);
                    return row;
                }
            });
        console.log("dvForEmail",dvForEmail)
        return (
            <Card style={{ marginTop: 15 }}
                title={'邮件处理统计'}
                extra={
                    <div>
                        <div className="search">
                            <a
                                onClick={() => this.changeStep('days')}
                                className={step === 'days' ? 'active' : ''}
                            >
                                <span>日</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('weeks')}
                                className={step === 'weeks' ? 'active' : ''}
                            >
                                <span>周</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('months')}
                                className={step === 'months' ? 'active' : ''}
                            >
                                <span>月</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('years')}
                                className={step === 'years' ? 'active' : ''}
                            >
                                <span>年</span>
                            </a>
                        </div>
                        <RangePicker
                            allowClear={false}
                            format={format}
                            mode={mode}
                            isopen={isopen}
                            value={value}
                            onOpenChange={(status) => {
                                if (status) {
                                    this.setState({ isopen: true })
                                } else {
                                    this.setState({ isopen: false })
                                }
                            }}
                            onPanelChange={this.onPanelChange}
                            onChange={this.onPanelChange}
                        />
                    </div>}
            >
                <Row gutter={16}>
                    <Col span={12}>
                        <Spin spinning={loading}>
                            <div style={{ textAlign: 'center' }}>
                                <h3>邮件类型分布统计</h3>
                                <p>(Total: {totalData || 0})</p>
                            </div>
                            <Chart
                                data={pieData}
                                height={400}
                                forceFit
                            >
                                <Coord type="theta" radius={0.75} />
                                <Axis name="percentage" />
                                <Legend
                                    position="bottom-center"
                                    itemTpl='<li class="g2-legend-list-item item-{index} {checked}" data-color="{originColor}" data-value="{originValue}" style="cursor: pointer; font-size: 14px;"><i class="g2-legend-marker" style="width:10px;height:10px;border-radius:50%;display:inline-block;margin-right:10px;background-color: {color};"></i><span class="g2-legend-text">{value}</span></li>'
                                />
                                <Tooltip
                                    showTitle={false}
                                    itemTpl='<li><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}封 ({percentage}%)</li>'
                                />
                                <Geom
                                    type="intervalStack"
                                    position="percentage"
                                    color={['type']}
                                    tooltip={[
                                        'type*value*percentage',
                                        (type, value, percentage) => ({
                                            name: type,
                                            value: value,
                                            percentage: percentage ? percentage.toFixed(1) : '0.0'
                                        })
                                    ]}
                                    style={{
                                        lineWidth: 1,
                                        stroke: '#fff',
                                    }}
                                    select={false}
                                />
                            </Chart>
                            <Divider type="vertical" />
                        </Spin>
                    </Col>
                    <Col span={12}>
                        <Spin spinning={loading}>
                            <Chart
                                data={dvForEmail}
                                height={400}
                                style={{marginTop: 40}}
                                forceFit
                                scale={{
                                    Time: {
                                        type: 'cat',
                                    },
                                    count: {
                                        type: "linear",
                                        min: 0,
                                        alias: '邮件数量'
                                    },
                                }}
                                // padding={{ left: 40, right: 65, top: 20, bottom: 60 }}
                            >
                                <Axis name='Time' title={false} />
                                <Axis name="count" title />
                                <Tooltip />
                                <Legend />
                                <Geom
                                    type="interval"
                                    position="Time*count"
                                    color={['Type', ['#f5222d', '#1890ff', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                    adjust={[
                                        {
                                            type: 'dodge',
                                            marginRatio: 0.1
                                        }
                                    ]}
                                    tooltip={[
                                        "Time*count*Type",
                                        (Time, count, Type) => ({
                                            title: Time,
                                            name: Type,
                                            value: count + '封邮件'
                                        })
                                    ]}
                                    size={emailData.length <= 10 ? 30 : 15}
                                />
                            </Chart>
                        </Spin>
                    </Col>
                </Row>
                <Divider style={{ margin: '0' }} />
                <Row gutter={16}>
                    <Col span={12}>
                        <Spin spinning={loading}>
                            <Chart
                                data={dvForEmail}
                                height={400}
                                style={{marginTop: 40}}
                                forceFit
                                scale={{
                                    Time: {
                                        type: 'cat',
                                    },
                                    count: {
                                        type: "linear",
                                        min: 0,
                                        alias: '邮件总数',
                                    },
                                }}
                                // padding={{ left: 40, right: 65, top: 20, bottom: 60 }}
                            >
                                <Axis name='Time' title={false} />
                                <Axis name="count" title />
                                <Tooltip />
                                <Legend />
                                <Geom
                                    type="interval"
                                    position="Time*count"
                                    color={['Type', ['#f5222d', '#1890ff', '#faad14', '#722ed1', '#eb2f96', '#13c2c2']]}
                                    adjust={[
                                        {
                                            type: 'dodge',
                                            marginRatio: 0.1
                                        }
                                    ]}
                                    tooltip={[
                                        "Time*count*Type",
                                        (Time, count, Type) => ({
                                            title: Time,
                                            name: Type,
                                            value: count + '封邮件'
                                        })
                                    ]}
                                    size={emailData.length <= 10 ? 30 : 15}
                                />
                            </Chart>
                        </Spin>
                    </Col>
                    <Col span={12}></Col>
                </Row>
            </Card>
        );
    }
}

export default EmailProcessChart;
