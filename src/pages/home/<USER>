import React from "react"
import { useState, useEffect } from "react"
import { Table, Row, Col, Card, Radio, Tooltip } from "antd"
import WhiteManager from '../../components/WhiteManager'
export default function RiskRankTable({RiskRank}) {
  const [RiskRankType, setRiskRankType] = useState("WeibeianRisk")
  const [windowWidth, setWindowWidth] = useState(typeof window !== "undefined" ? window.innerWidth : 1200)
  const data = RiskRank?RiskRank[RiskRankType].slice(0,10).map((item, index) => ({
    ...item,
    rank: index + 1,
  })):[]
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  let extra = (
    <Row
      style={{ height: 57, width: 500, margin: -16 }}
      type="flex"
      align="middle"
    >
      <Col span={18} >
        <Radio.Group
          defaultValue="WeibeianRisk"
          buttonStyle="solid"
          value={RiskRankType}
          onChange={e=>{setRiskRankType(e.target.value)}}
        >
          <Radio.Button value="WeibeianRisk">未备案</Radio.Button>
          <Radio.Button value="IllRisk">违规</Radio.Button>
          <Radio.Button value="MingPoolRisk">挖矿</Radio.Button>
          <Radio.Button value="PoliceRisk">调证</Radio.Button>
          <Radio.Button value="LoginRisk">风险登录</Radio.Button>
        </Radio.Group>
      </Col>
      <Col span={2} offset={1}>
        <WhiteManager></WhiteManager>
      </Col>
    </Row>
  );
  // 表格列定义
  const columns = [
    {
      title: "排名",
      dataIndex: "rank",
      key: "rank",
      width: 50,
    },
    {
      title: "公司ID",
      dataIndex: "CompanyId",
      key: "CompanyId",
      width: 100,
    },
    {
      title: "公司名称",
      dataIndex: "CompanyName",
      key: "CompanyName",
      ellipsis: true,
      render: (text) => {
        return (
          <Tooltip placement="topLeft" title={text}>
            {text || "-"}
          </Tooltip>
        );
      },
    },
    {
      title: "次数",
      dataIndex: "count",
      key: "count",
      width: 50,
      render: (_, record) => {
        let countKey =
        record &&
          Object.keys(record).find((k) => k.includes("Count"));
        return record[countKey]
      }
    },
  ]

  // 根据屏幕宽度拆分数据
  const isDesktop = windowWidth >= 1024

  // 在大屏幕上分为两列显示
  const half = Math.ceil(data.length / 2)
  const leftData = data.slice(0, half).map((item, index) => ({
    ...item,
    rank: index + 1,
  }))
  const rightData = data.slice(half).map((item, index) => ({
    ...item,
    rank: index + half + 1,
  }))

  // 响应式设置
  const getTableScroll = () => {
    if (windowWidth < 768) {
      return { x: "max-content" }
    }
    return {}
  }

  return (
    <Card title="风险客户榜单" extra={extra}>
      {isDesktop ? (
        // 桌面布局 - 两列并排
        <Row gutter={16}>
          <Col span={12}>
          <Table
                columns={columns}
                dataSource={leftData}
                pagination={false}
                size="middle"
                bordered
                scroll={getTableScroll()}
              />
          </Col>
          <Col span={12}>
          <Table
                columns={columns}
                dataSource={rightData}
                pagination={false}
                size="middle"
                bordered
                scroll={getTableScroll()}
              />
          </Col>
        </Row>
      ) : (
        // 移动/平板布局 - 单列带分页
          <Table
            columns={columns}
            dataSource={data}
            pagination={{
              pageSize: 5,
              showSizeChanger: false,
              showQuickJumper: false,
              size: "small",
            }}
            size="middle"
            bordered
            scroll={getTableScroll()}
          />
      )}
    </Card>
  )
}
