# GetEmailSumStatistics API 文档

## 接口概述

**接口名称**: `hegui.GetEmailSumStatistics`  
**功能描述**: 获取邮件类别和对应的处理数据总量统计，支持按时间范围进行时间序列统计  
**请求方式**: GET/POST  
**数据库**: `weibeian2`

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| StartTime | integer | 否 | 开始时间戳（Unix timestamp） | 1640995200 |
| EndTime | integer | 否 | 结束时间戳（Unix timestamp） | 1643673600 |
| Step | string | 否 | 时间间隔单位 | "days", "weeks", "months", "years", "hours", "minutes" |

### Step 参数说明

支持 moment.js 支持的所有时间单位：
- `years`: 按年统计
- `months`: 按月统计
- `weeks`: 按周统计  
- `days`: 按日统计
- `hours`: 按小时统计
- `minutes`: 按分钟统计
- 其他 moment.js 支持的时间单位

## 响应格式

### 成功响应 (RetCode: 0)

```json
{
  "RetCode": 0,
  "Total": 25564,
  "TypeStatistics": [
    {
      "Type": "攻击",
      "TypeId": 1,
      "Count": 13395,
      "Percentage": 52.4
    },
    {
      "Type": "其他",
      "TypeId": 0,
      "Count": 11678,
      "Percentage": 45.68
    },
    {
      "Type": "垃圾邮件",
      "TypeId": 4,
      "Count": 419,
      "Percentage": 1.64
    },
    {
      "Type": "侵权",
      "TypeId": 2,
      "Count": 66,
      "Percentage": 0.26
    },
    {
      "Type": "滥用",
      "TypeId": 3,
      "Count": 5,
      "Percentage": 0.02
    },
    {
      "Type": "数据库信息暴露",
      "TypeId": 10,
      "Count": 1,
      "Percentage": 0
    }
  ],
  "TimeSeriesStatistics": [
    {
      "Time": "2020-05-07",
      "TotalCount": 22306,
      "TypeStatistics": [
        {
          "Type": "攻击",
          "Count": 12436,
          "Percentage": 55.75
        },
        {
          "Type": "侵权",
          "Count": 35,
          "Percentage": 0.16
        },
        {
          "Type": "其他",
          "Count": 9595,
          "Percentage": 43.02
        },
        {
          "Type": "垃圾邮件",
          "Count": 237,
          "Percentage": 1.06
        },
        {
          "Type": "滥用",
          "Count": 3,
          "Percentage": 0.01
        }
      ]
    },
    {
      "Time": "2021-05-07",
      "TotalCount": 3258,
      "TypeStatistics": [
        {
          "Type": "攻击",
          "Count": 959,
          "Percentage": 29.44
        },
        {
          "Type": "其他",
          "Count": 2083,
          "Percentage": 63.93
        },
        {
          "Type": "垃圾邮件",
          "Count": 182,
          "Percentage": 5.59
        },
        {
          "Type": "侵权",
          "Count": 31,
          "Percentage": 0.95
        },
        {
          "Type": "滥用",
          "Count": 2,
          "Percentage": 0.06
        },
        {
          "Type": "数据库信息暴露",
          "Count": 1,
          "Percentage": 0.03
        }
      ]
    },
    {
      "Time": "2022-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    },
    {
      "Time": "2023-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    },
    {
      "Time": "2024-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    },
    {
      "Time": "2025-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    }
  ]
}
```

### 错误响应

#### 方法不存在 (RetCode: 10001)
```json
{
  "RetCode": 10001,
  "Message": "No Such Method"
}
```

#### 数据库查询错误 (RetCode: 33001)
```json
{
  "RetCode": 33001,
  "Message": "Database query error"
}
```

#### 参数验证错误 (RetCode: 10003)
```json
{
  "RetCode": 10003,
  "Message": "StartTime, EndTime, and Step are required parameters"
}
```

## 字段说明

### 响应字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| RetCode | integer | 返回码，0表示成功 |
| Total | integer | 总邮件数量 |
| TypeStatistics | array | 按类型统计的数据数组 |
| TimeSeriesStatistics | array | 时间序列统计数据数组 |

### TypeStatistics 字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| Type | string | 邮件类型名称 |
| TypeId | integer | 邮件类型ID |
| Count | integer | 该类型邮件数量 |
| Percentage | number | 该类型邮件占比（百分比） |

### TimeSeriesStatistics 字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| Time | string | 时间段标识（格式：YYYY-MM-DD） |
| TotalCount | integer | 该时间段总邮件数量 |
| TypeStatistics | array | 该时间段按类型统计的数据 |

## 使用示例

### 1. 按年统计示例

**请求**:
```
GET /?Action=hegui.GetEmailSumStatistics&StartTime=1588809600&EndTime=1735689600&Step=years
```

**响应**:
```json
{
  "RetCode": 0,
  "Total": 25564,
  "TypeStatistics": [
    {
      "Type": "攻击",
      "TypeId": 1,
      "Count": 13395,
      "Percentage": 52.4
    },
    {
      "Type": "其他",
      "TypeId": 0,
      "Count": 11678,
      "Percentage": 45.68
    },
    {
      "Type": "垃圾邮件",
      "TypeId": 4,
      "Count": 419,
      "Percentage": 1.64
    },
    {
      "Type": "侵权",
      "TypeId": 2,
      "Count": 66,
      "Percentage": 0.26
    },
    {
      "Type": "滥用",
      "TypeId": 3,
      "Count": 5,
      "Percentage": 0.02
    },
    {
      "Type": "数据库信息暴露",
      "TypeId": 10,
      "Count": 1,
      "Percentage": 0
    }
  ],
  "TimeSeriesStatistics": [
    {
      "Time": "2020-05-07",
      "TotalCount": 22306,
      "TypeStatistics": [
        {
          "Type": "攻击",
          "Count": 12436,
          "Percentage": 55.75
        },
        {
          "Type": "侵权",
          "Count": 35,
          "Percentage": 0.16
        },
        {
          "Type": "其他",
          "Count": 9595,
          "Percentage": 43.02
        },
        {
          "Type": "垃圾邮件",
          "Count": 237,
          "Percentage": 1.06
        },
        {
          "Type": "滥用",
          "Count": 3,
          "Percentage": 0.01
        }
      ]
    },
    {
      "Time": "2021-05-07",
      "TotalCount": 3258,
      "TypeStatistics": [
        {
          "Type": "攻击",
          "Count": 959,
          "Percentage": 29.44
        },
        {
          "Type": "其他",
          "Count": 2083,
          "Percentage": 63.93
        },
        {
          "Type": "垃圾邮件",
          "Count": 182,
          "Percentage": 5.59
        },
        {
          "Type": "侵权",
          "Count": 31,
          "Percentage": 0.95
        },
        {
          "Type": "滥用",
          "Count": 2,
          "Percentage": 0.06
        },
        {
          "Type": "数据库信息暴露",
          "Count": 1,
          "Percentage": 0.03
        }
      ]
    },
    {
      "Time": "2022-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    },
    {
      "Time": "2023-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    },
    {
      "Time": "2024-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    },
    {
      "Time": "2025-05-07",
      "TotalCount": 0,
      "TypeStatistics": []
    }
  ]
}
```


### 2. 按日统计示例

**请求**:
```
GET /?Action=hegui.GetEmailSumStatistics&StartTime=1640995200&EndTime=1643673600&Step=days
```

**响应**:
```json
{
  "RetCode": 0,
  "Total": 25564,
  "TypeStatistics": [
    {
      "Type": "攻击",
      "TypeId": 1,
      "Count": 13395,
      "Percentage": 52.4
    },
    {
      "Type": "其他",
      "TypeId": 0,
      "Count": 11678,
      "Percentage": 45.68
    },
    {
      "Type": "垃圾邮件",
      "TypeId": 4,
      "Count": 419,
      "Percentage": 1.64
    },
    {
      "Type": "侵权",
      "TypeId": 2,
      "Count": 66,
      "Percentage": 0.26
    },
    {
      "Type": "滥用",
      "TypeId": 3,
      "Count": 5,
      "Percentage": 0.02
    },
    {
      "Type": "数据库信息暴露",
      "TypeId": 10,
      "Count": 1,
      "Percentage": 0
    }
  ],
  "TimeSeriesStatistics": [
    {
      "Time": "2024-01-01",
      "TotalCount": 150,
      "TypeStatistics": [
        {
          "Type": "攻击",
          "Count": 80,
          "Percentage": 53.33
        },
        {
          "Type": "其他",
          "Count": 65,
          "Percentage": 43.33
        },
        {
          "Type": "垃圾邮件",
          "Count": 3,
          "Percentage": 2.00
        },
        {
          "Type": "侵权",
          "Count": 2,
          "Percentage": 1.33
        }
      ]
    },
    {
      "Time": "2024-01-02",
      "TotalCount": 180,
      "TypeStatistics": [
        {
          "Type": "攻击",
          "Count": 95,
          "Percentage": 52.78
        },
        {
          "Type": "其他",
          "Count": 80,
          "Percentage": 44.44
        },
        {
          "Type": "垃圾邮件",
          "Count": 4,
          "Percentage": 2.22
        },
        {
          "Type": "侵权",
          "Count": 1,
          "Percentage": 0.56
        }
      ]
    }
  ]
}
```


## 注意事项

1. **必填参数**: `StartTime`、`EndTime`、`Step` 三个参数都是必填的
2. **Step 支持**: Step 参数支持 moment.js 支持的所有时间单位，包括 years、months、weeks、days、hours、minutes 等
3. **数据范围**: 时间戳使用 Unix timestamp 格式（秒级）
4. **百分比计算**: Percentage 字段保留两位小数
5. **数据库**: 接口查询 `weibeian2` 数据库中的邮件记录表
6. **时间格式**: 时间序列统计中的 Time 字段格式根据 Step 参数自动调整
7. **参数验证**: 如果缺少任一必填参数，会返回 RetCode: 10003 错误

