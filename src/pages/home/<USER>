// data-set 可以按需引入，除此之外不要引入别的包
import React from 'react';
import moment from 'moment'
import {
    Chart,
    Geom,
    Axis,
    Tooltip,
    Legend
} from "bizcharts";
import { Card, Row, Col, DatePicker, Spin } from 'antd';
import DataSet from '@antv/data-set';

const { RangePicker } = DatePicker

const map = {
    'days': 'YYYY-MM-DD',
    'weeks': 'YYYY-MM-DD',
    'months': 'YYYY-MM',
    'years': 'YYYY',
}
const modeMap = {
    'minutes': ['time', 'time'],
    'hours': ['time', 'time'],
    'days': ['date', 'date'],
    'weeks': ['date', 'date'],
    'months': ['month', 'month'],
    'years': ['year', 'year'],
}


class Demo extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            step: 'weeks',
            value: [moment().weekday(-28), moment()],
            format: 'YYYY-MM-DD',
            mode: ['date', 'date'],
            startTime: moment().weekday(-28).format('YYYY-MM-DD'),
            endTime: moment().startOf('week').format('YYYY-MM-DD')
        }
    }
    componentDidMount() {
        this.getData()
    }
    changeStep = (step) => {
        this.setState({
            step,
            format: map[step],
            mode: modeMap[step]
        }, () => {
            this.getData()
        })
    }

    onPanelChange = (v) => {
        const { step } = this.state
        this.setState({
            value: v,
            startTime: v[0].format(map[step]),
            endTime: v[1].format(map[step]),
            isopen: false
        }, () => {
            this.getData()
        })
    }

    getData = () => {
        const { startTime, endTime, step } = this.state
        if (startTime && endTime) {
            this.props.getPersonalAuthData({
                StartTime: moment(startTime).unix(),
                EndTime: moment(endTime).unix(),
                Step: step,
                AuthType: 0
            })
            this.props.getCompanyAuthData({
                StartTime: moment(startTime).unix(),
                EndTime: moment(endTime).unix(),
                Step: step,
                AuthType: 1
            })
        }
    }
    render() {
        const { personalAuthData = [], companyAuthData = [] } = this.props
        let { step, format, mode, isopen, value } = this.state
        var ds = new DataSet();
        var dvForAll = ds
            .createView("countByAuthType", {
                watchingStates: [] // 用空数组，使得这个实例不监听 state 变化
            }) // 在 DataSet 实例下创建名为 countByAuthType 的数据视图
            .source(personalAuthData);
        dvForAll
            .transform({
                // 合并列
                type: "fold",
                fields: [
                    'ArtificialCount',
                    'AutoCount'
                ],
                key: "authType",
                value: "count"
            })
            .transform({
                type: "map",
                callback: function (row) {
                    row.count = parseInt(row.count, 10);
                    return row;
                }
            });
        var dvForAll2 = ds
            .createView("countByAuthType2", {
                watchingStates: [] // 用空数组，使得这个实例不监听 state 变化
            }) // 在 DataSet 实例下创建名为 countByAuthType 的数据视图
            .source(companyAuthData);
        dvForAll2
            .transform({
                // 合并列
                type: "fold",
                fields: [
                    'ArtificialCount',
                    'AutoCount'
                ],
                key: "authType",
                value: "count"
            })
            .transform({
                type: "map",
                callback: function (row) {
                    row.count = parseInt(row.count, 10);
                    return row;
                }
            });
        return (
            <Card style={{ marginTop: 15 }}
                title={'实名认证'}
                extra={
                    <div>
                        <div className="search">
                            <a
                                onClick={() => this.changeStep('days')}
                                className={step === 'days' ? 'active' : ''}
                            >
                                <span>日</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('weeks')}
                                className={step === 'weeks' ? 'active' : ''}
                            >
                                <span>周</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('months')}
                                className={step === 'months' ? 'active' : ''}
                            >
                                <span>月</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('years')}
                                className={step === 'years' ? 'active' : ''}
                            >
                                <span>年</span>
                            </a>
                        </div>
                        <RangePicker
                            allowClear={false}
                            format={format}
                            mode={mode}
                            isopen={isopen}
                            value={value}
                            onOpenChange={(status) => {
                                if (status) {
                                    this.setState({ isopen: true })
                                } else {
                                    this.setState({ isopen: false })
                                }
                            }}
                            onPanelChange={this.onPanelChange}
                            onChange={this.onPanelChange}
                        />
                    </div>}
            >
                <Row gutter={16}>
                    <Col span={12}>
                        <h3>个人实名认证审核率</h3>
                        <Spin spinning={personalAuthData.length === 0}>
                            <Chart
                                data={dvForAll}
                                height={400}
                                forceFit
                                scale={{
                                    Time: {
                                        type: 'cat',
                                    },
                                    percent: {
                                        type: "linear",
                                        tickCount: 6,
                                        formatter: (v) => v + '%'
                                    },
                                }}
                                padding={{ left: 40, right: 65, top: 20, bottom: 60 }}
                            >
                                <Axis name='Time' title={false} />
                                <Axis name="count" />
                                <Tooltip />
                                <Geom
                                    type="intervalStack"
                                    position="Time*count"
                                    color={['authType', ['#2ec264', '#1792fb']]}
                                    tooltip={[
                                        "Time*count*authType",
                                        (Time, count, authType) => ({
                                            title: Time,
                                            name: authType === 'AutoCount' ? '自动审核' : '人工审核',
                                            value: count
                                        })
                                    ]}
                                    size={personalAuthData.length <= 10 ? 50 : 20}
                                />
                                <Tooltip />
                                <Axis name="percent" min={0} max={100} />
                                <Legend />
                                <Geom type="line"
                                    position="Time*percent"
                                    shape="smooth"
                                    size={2}
                                    color={'#f04864'}
                                    tooltip={[
                                        "Time*percent",
                                        (Time, percent) => ({
                                            name: '自动审核通过率',
                                            value: percent + "%"
                                        })
                                    ]}
                                />
                                <Geom
                                    type="point"
                                    position="Time*percent"
                                    size={4}
                                    shape={"circle"}
                                    color={'#f04864'}
                                    style={{
                                        stroke: "#fff",
                                        lineWidth: 1
                                    }}
                                    tooltip={[
                                        "Time*percent",
                                        (Time, percent) => ({
                                            name: '自动审核通过率',
                                            value: percent + "%"
                                        })
                                    ]}
                                />
                            </Chart>
                        </Spin>
                    </Col>
                    <Col span={12}>

                        <h3>企业实名认证审核率</h3>
                        <Spin spinning={companyAuthData.length === 0}>
                            <Chart
                                data={dvForAll2}
                                height={400}
                                forceFit
                                scale={{
                                    Time: {
                                        type: 'cat',
                                    },
                                    percent: {
                                        type: "linear",
                                        tickCount: 6,
                                        formatter: (v) => v + '%'
                                    },
                                }}
                                padding={{ left: 40, right: 65, top: 20, bottom: 60 }}
                            >
                                <Axis name='Time' title={false} />
                                <Axis name="count" />
                                <Tooltip />
                                <Geom
                                    type="intervalStack"
                                    position="Time*count"
                                    color={['authType', ['#2ec264', '#1792fb']]}
                                    tooltip={[
                                        "Time*count*authType",
                                        (Time, count, authType) => ({
                                            title: Time,
                                            name: authType === 'AutoCount' ? '自动审核' : '人工审核',
                                            value: count
                                        })
                                    ]}
                                    size={personalAuthData.length <= 10 ? 50 : 20}
                                />
                                <Tooltip />
                                <Axis name="percent" min={0} max={100} />
                                <Legend />
                                <Geom type="line"
                                    position="Time*percent"
                                    shape="smooth"
                                    size={2}
                                    color={'#f04864'}
                                    tooltip={[
                                        "Time*percent",
                                        (Time, percent) => ({
                                            name: '自动审核通过率',
                                            value: percent + "%"
                                        })
                                    ]}
                                />
                                <Geom
                                    type="point"
                                    position="Time*percent"
                                    size={4}
                                    shape={"circle"}
                                    color={'#f04864'}
                                    style={{
                                        stroke: "#fff",
                                        lineWidth: 1
                                    }}
                                    tooltip={[
                                        "Time*percent",
                                        (Time, percent) => ({
                                            name: '自动审核通过率',
                                            value: percent + "%"
                                        })
                                    ]}
                                />
                            </Chart>

                        </Spin>
                    </Col>
                </Row>
            </Card>
        );
    }
}

export default Demo
