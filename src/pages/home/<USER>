import React, { Component } from 'react'
import <PERSON>Traffic from './computerTraffic'
import Icp<PERSON><PERSON> from './icp<PERSON>hart'
import PersonalA<PERSON><PERSON><PERSON> from './personalAuth<PERSON>hart'
import EmailProcess<PERSON>hart from './emailProcess<PERSON>hart'
import Seal<PERSON>ie<PERSON>hart from './sealPie<PERSON>hart'
import AutoRateChart from './autoRate<PERSON>hart'
import TagCloud from './tagCloud'
import {
    Row, Col, Card, Icon,
    Slider, Popover, notification
} from "antd"
import PayInterface<PERSON>hart from './payInterfaceChart'
import Texty from 'rc-texty';
import moment from 'moment'
import 'rc-texty/assets/index.css';
import './index.css'
import api from '../../utils/request'
import {getTodayStartTime,getTodayEndTime, getWeekStartTime,getWeekEndTime} from '../../utils/getTime'
import IPMap from '../../components/IPMap'
import RiskRankTable from './riskRankTable'
export default class Home extends Component {
    constructor(props) {
        super(props)
        this.state = {
            GetRegionBlockRatio: [
                {
                    "TaskName": "北京-合规拦截成功率监控",
                    "TaskId": "2271019",
                    "Uprate": "1.12%",
                    "LastCheckTime": "2021-06-21 17:02:07",
                    "BlockRaito": 0.9888,
                    "Region": "北京"
                },
                {
                    "TaskName": "上海-合规拦截成功率监控",
                    "TaskId": "2271041",
                    "Uprate": "0%",
                    "LastCheckTime": "2021-06-21 17:02:07",
                    "BlockRaito": 1,
                    "Region": "上海"
                },
                {
                    "TaskName": "广东-合规拦截成功率监控",
                    "TaskId": "2278401",
                    "Uprate": "0%",
                    "LastCheckTime": "2021-06-21 17:02:07",
                    "BlockRaito": 1,
                    "Region": "广东"
                }
            ],
            TaskId: null,
            GetRegionBlockRatioForISP: {
                "电信": 0.9516129032258065,
                "联通": 0.9672131147540983,
                "移动": 0.868421052631579
            },
            RiskRankType: 'WeibeianRisk',
            RiskRank:{
              WeibeianRisk: [],
              MingPoolRisk: [],
              IllRisk: [],
              PoliceRisk: [],
              LoginRisk: []
            },
            Time: "1624265883",
            unBeianStatistic:{
              Count: 0,
              Rate: 0
            }
        }
    }
    componentDidMount() {
        this.props.getRealTimeData()
        this.props.getSealRecordData({
            StartTime: moment().startOf('year').unix(),//moment().subtract(60,'days').unix(),
            EndTime: moment().unix(),
            Step: "years"
        })
        let self = this
        let action = "GetRegionBlockRatio",
            options = {
                Action: action,
            }
        api(action, options)
            .then(resp => {
                let message = '请求成功'
                if (resp.RetCode === 0) {
                    self.setState({
                        GetRegionBlockRatio: resp.DataSet
                    })
                    return
                } else {
                    message = resp.Message || resp.RetCode + "请求失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
          this.GetRiskPrompt()
          this.GetTodayUnbeianStatistics()
    }
    GetRegionBlockRatio = (e) => {
        let TaskId = e.currentTarget.id
        this.setState({
            TaskId: TaskId
        }, () => {
            let self = this
            let action = "GetRegionBlockRatioForISP",
                options = {
                    TaskId: this.state.TaskId,
                    Action: action,
                }
            api(action, options)
                .then(resp => {
                    let message = '请求成功'
                    if (resp.RetCode === 0) {
                        self.setState({
                            GetRegionBlockRatioForISP: resp.DataSet,
                            Time: resp.Time,
                        })
                        return
                    } else {
                        message = resp.Message || resp.RetCode + "请求失败"
                    }
                    notification.open({
                        message: message,
                    });
                }).catch(err => {
                    // 报错
                    notification['error']({
                        message: '请求失败',
                        description: err.message || '内部错误'
                    })
                    return;
                })
        })

    }
    GetRiskPrompt(){
      api('GetRiskPrompt').then(resp => {
                let message = '请求成功'
                if (resp.RetCode === 0) {
                    this.setState({RiskRank: resp})
                    return
                } else {
                    message = resp.Message || resp.RetCode + "请求失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    GetTodayUnbeianStatistics(){
      api('GetTodayUnbeianStatistics').then(resp => {
        let message = '请求成功'
        if (resp.RetCode === 0) {
            delete resp.RetCode
            this.setState({unBeianStatistic: {...resp}})
            return
        } else {
            message = resp.Message || resp.RetCode + "请求失败"
        }
        notification.open({
            message: message,
        });
      }).catch(err => {
          // 报错
          notification['error']({
              message: '请求失败',
              description: err.message || '内部错误'
          })
          return;
      })
    }
    render() {
        const {
            getIllegalICPData,
            getNoICPData,
            noICPData,
            noICPLoading,
            illegalICPLoading,
            illegalICPData,
            aoDunData,
            getAoDunData,
            getPersonalAuthData,
            getCompanyAuthData,
            personalAuthData,
            companyAuthData,
            sealData,
            AuthData,
            NotifyData
        } = this.props
        const { RiskRank, unBeianStatistic } = this.state;

        return (
            <div style={{ padding: 10 }}>
                <Row type="flex" className='realTimeData'>
                    <Col span={18}>
                        <div>
                            <div className='nowDataTitle'>
                                实时审核数
                                <div className='currentTraffic'>
                                    机房流量：
                                    上海
                                    <Slider defaultValue={50} disabled marks={{ 50: '50' }} />
                                    <span>100%
                                        <Icon type="arrow-up" className='upIcon' />
                                    </span>
                                    北京
                                    {/* <Tooltip title="30%"> */}
                                    <Slider defaultValue={70} disabled marks={{ 50: '50' }} />
                                    <span>
                                        80%<Icon type="arrow-down" className='downIcon' />
                                    </span>

                                    广东
                                    {/* <Tooltip title="30%"> */}
                                    <Slider defaultValue={60} disabled marks={{ 50: '50' }} />
                                    <span>
                                        90%<Icon type="arrow-up" className='upIcon' />
                                    </span>
                                    {/* </Tooltip> */}
                                </div>
                            </div>
                            <Row gutter={[16, 16]} style={{ paddingBottom: 15 }}>
                                <Col span={8}>
                                    <Card bordered={false} style={{ paddingBottom: 15 }} hoverable >
                                        <div className='nowDataSubTitle'>VPN</div>
                                        <div className='nowData'>
                                            <Texty delay={400} type='bottom' mode='smooth'>1,028</Texty>
                                        </div>
                                        <div className='nowDataAuto'>
                                            <span>
                                                <Icon className='autoIcon' type="setting" />
                                                0
                                            </span>
                                            <span>
                                                <Icon className='personalIcon' type="user" />
                                                1,028
                                            </span>
                                            0%
                                            <Popover content={<AutoRateChart />} trigger="hover" placement="right">
                                                <Icon type="arrow-up" className='personalIcon' />
                                            </Popover>
                                        </div>
                                        {/* <div className='nowAutoRate'>
                                            自动检测率 日同比 11%
                                            <Icon type="caret-up" className='personalIcon' />
                                        </div> */}
                                    </Card>
                                </Col>
                                <Col span={8}>
                                    <Card bordered={false} style={{ paddingBottom: 15 }} hoverable>
                                        <div className='nowDataSubTitle'>黄赌毒</div>
                                        <div className='nowData'>
                                            <Texty delay={400} type='bottom' mode='smooth'>1,426</Texty>
                                        </div>
                                        <div className='nowDataAuto'>
                                            <span>
                                                <Icon className='autoIcon' type="setting" />
                                                0
                                            </span>
                                            <span>
                                                <Icon className='personalIcon' type="user" />
                                                1,426
                                            </span>
                                            0%
                                            <Popover content={<AutoRateChart />} trigger="hover" placement="right">
                                                <Icon type="arrow-up" className='personalIcon' />
                                            </Popover>
                                        </div>
                                        {/* <div className='nowAutoRate'>
                                            自动检测率 日同比 11%
                                            <Icon type="caret-up" className='personalIcon' />
                                        </div> */}
                                    </Card>
                                </Col>
                                <Col span={8}>
                                    <Card bordered={false} style={{ paddingBottom: 15 }} hoverable>
                                        <div className='nowDataSubTitle'>涉政</div>
                                        <div className='nowData'>
                                            <Texty delay={400} type='bottom' mode='smooth'>549</Texty>
                                        </div>
                                        <div className='nowDataAuto'>
                                            <span>
                                                <Icon className='autoIcon' type="setting" />
                                                0
                                            </span>
                                            <span>
                                                <Icon className='personalIcon' type="user" />
                                                549
                                            </span>
                                            0%
                                            <Popover content={<AutoRateChart />} trigger="hover" placement="right">
                                                <Icon type="arrow-up" className='personalIcon' />
                                            </Popover>
                                        </div>

                                        {/* <div className='nowAutoRate'>
                                            自动检测率 日同比 11%
                                            <Icon type="caret-up" className='personalIcon' />
                                        </div> */}
                                    </Card>
                                </Col>
                            </Row>
                            <Row gutter={16} style={{ paddingBottom: 15 }}>
                                <Col span={8}>
                                    <Card bordered={false} hoverable>
                                        <div className='nowDataSubTitle'>邮件审核</div>
                                        <div className='nowData'>
                                            <Texty delay={400} type='bottom' mode='smooth'>16</Texty>
                                        </div>
                                        <div className='nowDataAuto'>
                                            <span>
                                                <Icon className='autoIcon' type="setting" />
                                                0
                                            </span>
                                            <span>
                                                <Icon className='personalIcon' type="user" />
                                                16
                                            </span>
                                            0%
                                            <Popover content={<AutoRateChart />} trigger="hover" placement="right">
                                                <Icon type="arrow-up" className='personalIcon' />
                                            </Popover>
                                        </div>
                                    </Card>
                                </Col>
                                <Col span={8}>
                                    <Card bordered={false} hoverable>
                                        <div className='nowDataSubTitle'>实名认证</div>
                                        <div className='nowData'>
                                            <Texty delay={400} type='bottom' mode='smooth'>
                                                {
                                                    AuthData.AutoCount +
                                                    AuthData.ArtificialCount + ''
                                                }
                                            </Texty>
                                        </div>
                                        <div className='nowDataAuto'>
                                            <span>
                                                <Icon className='autoIcon' type="setting" />
                                                {
                                                    AuthData.AutoCount
                                                }
                                            </span>
                                            <span>
                                                <Icon className='personalIcon' type="user" />
                                                {
                                                    AuthData.ArtificialCount
                                                }
                                            </span>
                                            {
                                                AuthData.AutoRate ? (AuthData.AutoRate * 100).toFixed(2) + '%' : '0%'
                                            }
                                            <Popover content={<AutoRateChart />} trigger="hover" placement="right">
                                                <Icon type={
                                                    (AuthData.AutoCount / (AuthData.ArtificialCount + AuthData.AutoCount)) < AuthData.AutoRate ?
                                                        'arrow-up' : 'arrow-down'
                                                } className={
                                                    (AuthData.AutoCount / (AuthData.ArtificialCount + AuthData.AutoCount)) < AuthData.AutoRate ?
                                                        'upIcon' : 'downIcon'
                                                }
                                                />
                                            </Popover>
                                        </div>
                                    </Card>
                                </Col>
                                <Col span={8}>
                                    <Card bordered={false} hoverable>
                                        <div className='nowDataSubTitle'>未备案</div>
                                        <div className='nowData'>
                                            <Texty delay={400} type='bottom' mode='smooth'>{unBeianStatistic.Count+''}</Texty>
                                        </div>
                                        <div className='nowDataAuto'>
                                            <span>
                                                <Icon className='autoIcon' type="setting" />
                                                {/* <Icon type="plus-circle" className='autoIcon'/> */}
                                                0
                                            </span>
                                            <span>
                                                <Icon className='personalIcon' type="user" />
                                                {/* <Icon className='personalIcon' type="check-circle" theme="twoTone" twoToneColor="#52c41a" /> */}
                                                {unBeianStatistic.Count+''}
                                            </span>
                                            {unBeianStatistic.Rate+''}%
                                            <Popover content={<AutoRateChart />} trigger="hover" placement="right">
                                                <Icon type={unBeianStatistic.Rate>0?'arrow-up':'arrow-down'} className='personalIcon' />
                                            </Popover>
                                        </div>
                                    </Card>
                                </Col>
                            </Row>
                        </div>
                        <div style={{ paddingBottom: 15 }}>
                          <RiskRankTable RiskRank={RiskRank}/>
                        </div>
                    </Col>
                    <Col span={6}>
                        <Card title="实时通知数" style={{ marginLeft: 15 }}>
                            <Card.Grid style={{ width: '100%' }}>
                                <Card.Meta
                                    title={<span>
                                        <Icon className='personalIcon' type="mail" theme="twoTone" />
                                        邮件 {NotifyData.TodayCount}
                                        <span style={{ fontWeight: 'normal', marginLeft: 8, fontSize: 14, color: 'rgba(0,0,0,.65)' }}>
                                            <Icon type="close-circle" className='upIcon' />失败 {NotifyData.FailedRate}
                                            <span style={{ marginLeft: 8 }}>昨日 {NotifyData.YesterdayCount}</span>
                                        </span>
                                    </span>
                                    }
                                />
                            </Card.Grid>
                            <Card.Grid style={{ width: '100%' }}>
                                <Card.Meta
                                    title={<span>
                                        <Icon className='personalIcon' type="message" theme="twoTone" twoToneColor="#52c41a" />
                                        短信 {NotifyData.TodayCount}
                                        <span style={{ fontWeight: 'normal', marginLeft: 8, fontSize: 14, color: 'rgba(0,0,0,.65)' }}>
                                            <Icon type="close-circle" className='upIcon' />失败 {NotifyData.FailedRate}
                                            <span style={{ marginLeft: 8 }}>昨日 {NotifyData.YesterdayCount}</span>
                                        </span>
                                    </span>
                                    }
                                // description={
                                //     <div className='nowDataAuto'>
                                //         <span>自动检测 12,101</span>
                                //         <span>人工筛选 208</span>
                                //     </div>
                                // }
                                />
                            </Card.Grid>
                        </Card>
                        <SealPieChart sealData={sealData} />
                        <TagCloud data={[{ "name": "上海市", "value": 33, "type": 1 },
                        { "name": "肇庆市", "value": 53, "type": 1 },
                        { "name": "上海市", "value": 53, "type": 2 },
                        { "name": "舟山市", "value": 25, "type": 2 },
                        { "name": "吴忠市", "value": 98, "type": 1 },
                        { "name": "石嘴山市", "value": 78, "type": 0 },
                        { "name": "吉林市", "value": 99, "type": 0 },
                        { "name": "景德镇市", "value": 25, "type": 0 },
                        { "name": "舟山市", "value": 72, "type": 0 },
                        { "name": "兰州市", "value": 45, "type": 2 },
                        { "name": "延安市", "value": 57, "type": 2 },
                        { "name": "鄂尔多斯市", "value": 90, "type": 2 },
                        { "name": "离岛", "value": 10, "type": 2 },
                        { "name": "黑河市", "value": 79, "type": 0 },
                        { "name": "扬州市", "value": 91, "type": 1 },
                        { "name": "盐城市", "value": 37, "type": 0 },
                        { "name": "红河哈尼族彝族自治州", "value": 8, "type": 2 },
                        { "name": "吉安市", "value": 19, "type": 1 },
                        { "name": "金华市", "value": 44, "type": 1 },
                        { "name": "莱芜市", "value": 98, "type": 0 },
                        { "name": "抚顺市", "value": 84, "type": 1 },
                        { "name": "泰州市", "value": 65, "type": 1 },
                        { "name": "新余市", "value": 43, "type": 1 },
                        { "name": "宿迁市", "value": 4, "type": 2 },
                        { "name": "上海市", "value": 42, "type": 1 },
                        { "name": "桃园县", "value": 30, "type": 1 },
                        { "name": "阜新市", "value": 48, "type": 1 },
                        { "name": "毕节市", "value": 15, "type": 0 },
                        { "name": "重庆市", "value": 47, "type": 1 },
                        { "name": "屏东县", "value": 27, "type": 1 },
                        { "name": "淮安市", "value": 79, "type": 1 },
                        { "name": "东营市", "value": 91, "type": 1 },
                        { "name": "自贡市", "value": 30, "type": 2 },
                        { "name": "重庆市", "value": 57, "type": 2 },
                        { "name": "北京市", "value": 11, "type": 2 }]}
                            htight={300}
                        />
                    </Col>
                </Row>
                <IPMap></IPMap>
                <IcpChart getIllegalICPData={getIllegalICPData} getNoICPData={getNoICPData} noICPData={noICPData} illegalICPData={illegalICPData} noICPLoading={noICPLoading} illegalICPLoading={illegalICPLoading}/>
                <ComputerTraffic aoDunData={aoDunData} getAoDunData={getAoDunData} />
                <Row gutter={16} style={{ marginTop: 15 }}>
                    <Col span={12}>
                        <Card title='近一周'>
                        <iframe src={"https://tianliang.ucloudadmin.com/grafana/d-solo/YLXrrwm7k/ben-zhou-lan-jie-qing-kuang?orgId=1&from="+ getWeekStartTime() +"&to="+getWeekEndTime()+"&theme=light&panelId=2"} width="100%" height="500" frameBorder="0" loading="lazy" />
                        </Card>
                    </Col>
                    <Col span={12}>
                        <Card title={'当日'}>
                        <iframe src={"https://tianliang.ucloudadmin.com/grafana/d-solo/tAEyZsmnz/he-gui-lan-jie-cheng-gong-lu?orgId=1&from="+getTodayStartTime()+"&to="+getTodayEndTime()+"&theme=light&panelId=2"} width="100%" height="500" frameBorder="0" loading="lazy" />
                        </Card>
                    </Col>
                </Row>
                <PersonalAuthChart getPersonalAuthData={getPersonalAuthData} personalAuthData={personalAuthData} getCompanyAuthData={getCompanyAuthData} companyAuthData={companyAuthData} />
                <EmailProcessChart />
                {/* <Row gutter={16} style={{marginTop: 15}}>
                    <Col span={12}>
                        <Card title={'机房流量分布'}>
                            <MapChart/>
                        </Card>
                    </Col>
                </Row> */}
                <Row gutter={16} style={{ marginTop: 15 }}>
                    <Col span={12}>
                        <Card title='付费接口'>
                            <PayInterfaceChart />
                        </Card>
                    </Col>
                    <Col span={12}>
                        <Card title={'收费接口'}>
                            <PayInterfaceChart />
                        </Card>
                    </Col>
                </Row>
            </div>
        );
    }
}
