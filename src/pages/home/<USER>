import React, { Component } from 'react'
import { Card, DatePicker } from 'antd'
import moment from 'moment'
import {
    Chart,
    Geom,
    Axis,
    Tooltip,
    Legend,
} from "bizcharts";
import './index.css'

const { RangePicker } = DatePicker


export default class Home extends Component {
    constructor(props) {
        super(props)
        this.state = {
            aoDunStep: 'minutes',
            aoDunValue: [moment().subtract('hours', 2).set('minute', 0), moment().set('minute', 0)],
            aoDunFormat: 'YYYY-MM-DD HH:mm',
            aoDunStartTime: moment().subtract('hours', 2).set('minute', 0).format('YYYY-MM-DD HH:mm'),
            aoDunEndTime: moment().set('minute', 0).format('YYYY-MM-DD HH:mm')
        }
    }
    componentDidMount() {
        const { aoDunStep, aoDunStartTime, aoDunEndTime } = this.state
        this.props.getAoDunData({
            StartTime: moment(aoDunStartTime).unix(),
            EndTime: moment(aoDunEndTime).unix(),
            Step: aoDunStep
        })
    }
    changeStep = (step) => {
        this.setState({
            aoDunStep: step,
        })
    }

    onAoDunChange = (v) => {
        this.setState({
            aoDunValue: v,
            aoDunStartTime: v[0].unix(),
            aoDunEndTime: v[1].unix()
        })
    }
    onAoDunOk = (v) => {
        const { aoDunStep } = this.state
        this.setState({
            aoDunValue: v,
            aoDunStartTime: v[0].unix(),
            aoDunEndTime: v[1].unix()
        }, () => {
            this.props.getAoDunData({
                StartTime: v[0].unix(),
                EndTime: v[1].unix(),
                Step: aoDunStep
            })
        })
    }

    render() {

        let { aoDunFormat, aoDunStep, aoDunValue } = this.state

        const tooltip2 = [
            'Time*RealtimeFlow*Flow*Name*Ratio',
            (Time, RealtimeFlow, Flow = [], Name, Ratio) => {
                let detail = Flow.map(item => `<li>${item.Name}：${item.Flow['realtime-flow']}</li>`).join('')
                if (Name.includes('封堵')) {
                    return {
                        name: Name,
                        value: Name + '：' + (Ratio * 100).toFixed(2) + '%'
                    }
                } else if(Name.includes('傲盾')){
                    return {
                        name: '傲盾机房',
                        value: Time + Name + '：' + RealtimeFlow,
                        detail
                    }
                }else {
                    return {
                        name: Name,
                        value: Time + Name + '：' + RealtimeFlow
                    }
                }
            }]
        const cols2 = {
            month: {
                range: [0, 1]
            },
            Ratio: {
                formatter: (v) => (v * 100).toFixed(2) + '%' 
            },
            RealtimeFlow:{
                // min: 0
            }
        };
        return (
            <Card style={{ marginTop: 15 }}
                title='机房流量'
                bordered={false}
                extra={
                    <div>
                        <div className="search">
                            <a
                                onClick={() => this.changeStep('days')}
                                className={aoDunStep === 'days' ? 'active' : ''}
                            >
                                <span>日</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('hours')}
                                className={aoDunStep === 'hours' ? 'active' : ''}
                            >
                                <span>时</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('minutes')}
                                className={aoDunStep === 'minutes' ? 'active' : ''}
                            >
                                <span>分</span>
                            </a>
                        </div>
                        <RangePicker
                            allowClear={false}
                            format={aoDunFormat}
                            showTime={{ format: 'HH:mm' }}
                            value={aoDunValue}
                            onChange={this.onAoDunChange}
                            onOk={this.onAoDunOk}
                        />
                    </div>}
            >
                <Chart
                    height={450}
                    data={this.props.aoDunData}
                    scale={cols2}
                    forceFit
                    filter={[['Name', (t) => {
                        if (t.includes('北京')) return true;
                        return false;
                    }]]}
                    padding={{left: 40, right: 70, top:20, bottom: 80}}
                >
                    <Legend />
                    <Axis name="Time" />
                    <Axis
                        name="RealtimeFlow"
                    />
                    <Axis
                        name="Ratio"
                        min={0}
                        max={100}
                    />
                    <Tooltip
                        crosshairs={{
                            type: "y"
                        }}
                        showTitle={{
                            Ratio: false
                        }}
                    />
                    <Geom
                        type="area"
                        position="Time*RealtimeFlow"
                        size={2}
                        color={'Name'}
                        shape={"smooth"}
                        tooltip={tooltip2}
                    />
                    <Tooltip
                        useHtml={true}
                        htmlContent={(title, items) => {
                            let str = ''
                            items.forEach((item) => {
                                if (item.value && item.value.includes('封堵')) {
                                    str += `<div class='g2TooltipTitle'>${item.value} </div>`
                                } else if (item.name && item.name.includes('封堵') ) {
                                    str += ''
                                } else if (item.name && item.name.includes('机房流量') ) {
                                    str += `<div class='g2TooltipTitle'>${item.value} </div>`
                                } else {
                                    str += `<div class='g2TooltipTitle'>${item.value} </div>
                                        <ul>
                                        ${item.detail}
                                        </ul>`
                                }
                            })
                            return `<div class='g2Tooltip'>${str}</div>`
                        }}
                    />
                    <Geom
                        type="point"
                        position="Time*RealtimeFlow"
                        size={4}
                        shape={"circle"}
                        color={'Name'}
                        style={{
                            stroke: "#fff",
                            lineWidth: 1
                        }}
                        tooltip={tooltip2}
                    />
                    <Geom
                        type="line"
                        position="Time*Ratio"
                        size={2}
                        color={'Name'}
                        shape={"smooth"}
                        // tooltip={tooltip2}
                    />
                    <Geom
                        type="point"
                        position="Time*Ratio"
                        size={4}
                        shape={"circle"}
                        color={'Name'}
                        style={{
                            stroke: "#fff",
                            lineWidth: 1
                        }}
                        tooltip={tooltip2}
                    />
                </Chart>
            </Card>
        );
    }
}