import React from 'react';
import {
  <PERSON>,
  Chart,
  <PERSON>eom,
  <PERSON><PERSON><PERSON>,
  <PERSON>ord,
  Label,
} from "bizcharts";
import { Card } from 'antd';

class Slider<PERSON>hart extends React.Component {
  render() {

    return (
      <Card title={'账号封堵分析'} style={{ marginLeft: 15, marginTop: 15, height: 'auto' }}>
      <Chart
        data={this.props.sealData}
        forceFit
        height={135}
        padding={[0, 0, 0, 75]}
      >
        <Coord transpose />
          <Axis name="type" />
          <Axis name="value" visible={false} />
          <Tooltip />
          <Geom type="interval" position="type*value" color={['type', ['#facb37', '#f04663', '#8546db', '#1792fb', '#10c2c2', '#2ec264']]}>
            <Label/>
          </Geom>
      </Chart>
      </Card>
    );
  }
}

export default SliderChart
