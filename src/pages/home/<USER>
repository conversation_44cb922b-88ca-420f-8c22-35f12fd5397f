import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from "bizcharts";

class Colorrose extends React.Component {
  render() {
    const data = [
      {
        type: "2001",
        population: 41.8
      },
      {
        type: "2002",
        population: 38
      },
      {
        type: "2003",
        population: 33.7
      },
      {
        type: "2004",
        population: 30.7
      },
      {
        type: "2005",
        population: 25.8
      },
      {
        type: "2006",
        population: 31.7
      },
      {
        type: "2007",
        population: 33
      },
      {
        type: "2008",
        population: 46
      },
      {
        type: "2009",
        population: 38.3
      },
      {
        type: "2010",
        population: 28
      },
      {
        type: "2011",
        population: 42.5
      },
      {
        type: "2012",
        population: 30.3
      }
    ];
    return (
      <div>
        <Chart height={400} data={data} padding="auto" forceFit>
          <Coord type="polar" />
          <Tooltip />
          <Legend
            position="right"
            offsetY={-50}
            offsetX={-110}
          />
          <Geom
            type="interval"
            color="type"
            position="type*population"
            style={{
              lineWidth: 1,
              stroke: "#fff"
            }}
          />
        </Chart>
      </div>
    );
  }
}
export default Colorrose
