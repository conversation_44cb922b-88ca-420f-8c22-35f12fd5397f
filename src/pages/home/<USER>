import React from "react";
import {
  <PERSON>,
  <PERSON>eo<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>
} from "bizcharts";

class Basic extends React.Component {
  render() {
    const data = [
      {
        year: "03-28",
        value: 80
      },
      {
        year: "03-29",
        value: 92
      },
      {
        year: "03-30",
        value: 88
      },
      {
        year: "03-31",
        value: 91
      },
      {
        year: "04-01",
        value: 86
      },
      {
        year: "04-02",
        value: 92
      },
      {
        year: "04-03",
        value: 95
      },
      {
        year: "04-04",
        value: 96
      }
    ];
    const cols = {
      value: {
        min: 0,
        formatter: (v) => v + '%' 
      },
      year: {
        range: [0, 1]
      }
    };
    return (
      <div>
        <Chart height={250} data={data} scale={cols} forceFit padding={[30,30,30,40]}>
          <Axis name="year" />
          <Axis name="value"/>
          <Tooltip
            crosshairs={{
              type: "y"
            }}
          />
          <Geom type="line" position="year*value" shape={"smooth"} size={2} />
          <Geom
            type="point"
            position="year*value"
            size={4}
            shape={"circle"}
            style={{
              stroke: "#fff",
              lineWidth: 1
            }}
          />
        </Chart>
      </div>
    );
  }
}

export default Basic