import React, { Component } from 'react'
import { Card, DatePicker, Col, Spin, Empty } from 'antd'
import moment from 'moment'
import {
    <PERSON>,
    Geom,
    Axis,
    Tooltip,
    Legend,
} from "bizcharts";

const { RangePicker } = DatePicker

const map = {
    'auto': 'YYYY-MM-DD',
    'days': 'YYYY-MM-DD',
    'weeks': 'YYYY-MM-DD',
    'months': 'YYYY-MM',
    'years': 'YYYY',
}
const modeMap = {
    'auto': ['date', 'date'],
    'minutes': ['time', 'time'],
    'hours': ['time', 'time'],
    'days': ['date', 'date'],
    'weeks': ['date', 'date'],
    'months': ['month', 'month'],
    'years': ['year', 'year'],
}

export default class Home extends Component {
    constructor(props) {
        super(props)
        this.state = {
            step: 'weeks',
            value: [moment().weekday(-28), moment()],
            format: 'YYYY-MM-DD',
            mode: ['date', 'date'],
            startTime: moment().weekday(-28).format('YYYY-MM-DD'),
            endTime: moment().startOf('week').format('YYYY-MM-DD')
        }
    }

    componentDidMount() {
        this.getData()
    }
    changeStep = (step) => {
        let { value = [], startTime, endTime} = this.state
        if (step === 'days') {
            value = [moment().subtract(7, 'days'), moment()]
            endTime = moment().format('YYYY-MM-DD')
            startTime = moment().subtract(7, 'days').format('YYYY-MM-DD')
            console.log("Home -> changeStep -> StartTime", startTime)

        } else if (step === 'weeks') {
            value = [moment().weekday(-28), moment()]
            endTime = moment().startOf('week').format('YYYY-MM-DD')
            startTime = moment().weekday(-28).format('YYYY-MM-DD')
        } else if (step === 'months') {
            value = [moment().subtract(12, 'months'), moment()]
            endTime = moment().startOf('week').format('YYYY-MM-DD')
            startTime = moment().subtract(12, 'months').format('YYYY-MM-DD')
        }
        this.setState({
            step,
            format: map[step],
            mode: modeMap[step],
            startTime,
            endTime,
            value
        }, () => {
            this.getData()
        })
    }

    onPanelChange = (v) => {
        // const { step } = this.state
        let step = 'days'
        this.setState({
            value: v,
            step,
            startTime: v[0].format(map[step]),
            endTime: v[1].format(map[step]),
            isopen: false
        }, () => this.getData())
    }

    getData = () => {
        const { startTime, endTime, step } = this.state
        this.props.getNoICPData({
            StartTime: moment(startTime).unix(),
            EndTime: moment(endTime).unix(),
            Step: step
        })
        this.props.getIllegalICPData({
            StartTime: moment(startTime).unix(),
            EndTime: moment(endTime).unix(),
            Step: step
        })
    }
    render() {
        const cols = {
            Time: {
                type: 'cat',
            },
            Count: {
                min: 0,
            }
        };
        let { step, format, mode, isopen, value } = this.state
        let { noICPData, illegalICPData, noICPLoading, illegalICPLoading } = this.props
        const tooltip = [
            'Time*Count',
            (x, y) => ({
                name: x,
                value: `未备案数量：${y}`,
            }),
        ];
        const tooltip1 = [
            'Time*Count',
            (x, y) => ({
                name: x,
                value: `违规数量：${y}`
            }),
        ];
        const tabcontent = {
            'tab1': noICPData && noICPData.length > 0 ?
                <Chart height={400} data={noICPData} scale={cols} forceFit>
                    <Axis name="Time"
                        title={false}
                    />
                    <Axis name="Count" min={0} />
                    <Tooltip showTitle={false} crosshairs={false} />
                    <Legend name='Time' />
                    <Geom type="interval" color={'#facb37'} position="Time*Count" tooltip={tooltip} size={(noICPData && noICPData.length <= 5) ? 50 : 20} />
                </Chart>
                : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{height: "400px", lineHeight: "400px"}}/>,
            'tab2':
                illegalICPData.length > 0 ?
                    <Chart height={400} data={illegalICPData} scale={cols} forceFit>
                        <Axis name="Time"
                            title={false}
                        />
                        <Axis name="Count" min={0} />
                        <Tooltip showTitle={false} crosshairs={false} />
                        <Legend name='Time' />
                        <Geom type="interval" color={'#f04663'} position="Time*Count" tooltip={tooltip1} size={(noICPData && noICPData.length <= 5) ? 50 : 20} />
                    </Chart>
                    : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{height: "400px", lineHeight: "400px"}}/>
        }
        return (
            <Card
                bordered={false}
                title="数据统计"
                extra={
                    <div>
                        <div className="search">
                            <a
                                    onClick={() => this.changeStep('auto')}
                                    className={step === 'auto' ? 'active' : ''}
                                >
                                    <span>自定义</span>
                                </a>
                            <a
                                onClick={() => this.changeStep('days')}
                                className={step === 'days' ? 'active' : ''}
                            >
                                <span>近一周</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('weeks')}
                                className={step === 'weeks' ? 'active' : ''}
                            >
                                <span>近一月</span>
                            </a>
                            <a
                                onClick={() => this.changeStep('months')}
                                className={step === 'months' ? 'active' : ''}
                            >
                                <span>近一年</span>
                            </a>
                        </div>
                        <RangePicker
                            allowClear={false}
                            format={format}
                            mode={mode}
                            isopen={isopen}
                            value={value}
                            onOpenChange={(status) => {
                                if (status) {
                                    this.setState({ isopen: true })
                                } else {
                                    this.setState({ isopen: false })
                                }
                            }}
                            onPanelChange={this.onPanelChange}
                            onChange={this.onPanelChange}
                        />
                    </div>}
            >
                <Col
                    span={12}
                >
                    <h3>未备案数据</h3>
                    <Spin spinning={noICPLoading}>
                        {tabcontent['tab1']}
                    </Spin>
                </Col>
                <Col span={12}>
                    <h3>违规数据</h3>
                    <Spin spinning={illegalICPLoading}>
                        {tabcontent['tab2']}
                    </Spin>
                </Col>
            </Card>
        );
    }
}
