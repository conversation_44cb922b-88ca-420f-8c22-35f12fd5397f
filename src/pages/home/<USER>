.search {
  display: inline-block;
}

.search a {
  color: rgba(0, 0, 0, .65);
  margin-right: 24px;
}

.search a:hover {
  color: #1890ff;
}

.search a.active {
  color: #1890ff;
}

.g2Tooltip {
  position: absolute;
  visibility: hidden;
  /* border: 1px solid #efefef; */
  background-color: white;
  border-radius: 2px;
  color: #000;
  opacity: 0.8;
  padding: 5px 15px;
  box-shadow: 0px 1px 4px rgba(0,0,0,0.3),
  0px 0px 4px rgba(0,0,0,0.1) inset;
  transition: top 200ms,left 200ms;
}
.g2TooltipList {
    margin: 10px
  }
.ant-card-body, .ant-card-grid{
  padding-right: 6px
}
.realTimeData .ant-card-body{
  padding-bottom: 10px;
}

.nowDataTitle{
  height: 57px;
  padding: 16px 0 16px 20px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 15px;
}
.realTimeData .ant-slider{
  margin: 0 8px;
  width:60px;
  cursor: pointer!important;
  display: inline-block;
}
.realTimeData .ant-slider .ant-slider-rail, 
.realTimeData .ant-slider:hover .ant-slider-rail{
  background-color: #facc14;
}
/* .ant-slider-track, .ant-slider:hover .ant-slider-track,
.ant-slider-disabled .ant-slider-track{
  background-color: #13c2c2!important;
} */
.realTimeData .ant-slider .ant-slider-handle, 
.realTimeData .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open),
.realTimeData .ant-slider-disabled .ant-slider-handle{
  display: none;
  cursor: pointer!important;
  /* width: 7px;
  height: 7px;
  margin-top: -2px;
  border: solid 2px #f04864!important; */
}
.realTimeData .ant-slider .ant-slider-dot-active, 
.realTimeData .ant-slider-disabled .ant-slider-dot{
  border-color: #f04864!important;
  width: 7px;
  height: 7px;
  cursor: pointer!important;
}
.realTimeData .ant-slider .ant-slider-mark{
  display: none;
}
.ant-slider + span {
  margin-right: 12px;
}
.nowDataTitle  .currentTraffic{
  color: rgba(0,0,0,.65);
  font-size: 14px;
  display: inline-block;
  float: right;
  height:100%;
  padding: 3px 15px 0 0
}

.nowDataSubTitle {
  height: 22px;
  color: rgba(0,0,0,.45);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 5px;
}
.nowData {
  color: rgba(0,0,0,.85);
  font-size: 30px;
  line-height: 38px;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}
.nowDataAuto{
  margin-top:15px;
  color: rgba(0,0,0,.65);
  font-size: 14px;
  line-height: 18px;
}
.nowAutoRate {
  margin-top: 10px;
  padding-top: 9px;
  border-top: 1px solid rgb(240, 240, 240);
}
.nowDataAuto span {
  margin-right: 15px;
}
.autoIcon{
  margin-right: 5px;
  color: #1890ff
}
.personalIcon{
  margin-right: 5px;
  color: #f04864
}
.upIcon,.downIcon{
  margin-left:3px;
  margin-right:3px;
}
.upIcon{
  color: #f04864
}
.downIcon{
  color: #2ec264
}
