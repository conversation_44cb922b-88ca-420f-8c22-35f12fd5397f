import React from 'react';
import { Chart, Geom, Tooltip } from 'bizcharts';
import DataSet from '@antv/data-set';

// 示例地图数据，数据来源DATAV.GeoAtlas: https://datav.aliyun.com/tools/atlas，当前数据只做示例用。
const MAP_URL = 'https://gw.alipayobjects.com/os/basement_prod/a502afb5-d979-443c-9c40-92c0bc297dc9.json';

class MapChart extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      chinaGeo: null,
    };
  }

  componentDidMount() {
    fetch(MAP_URL).then(v => v.json()).then((v) => {
      this.setState({ chinaGeo: v });
    });
  }

  processGeoData = (geoData, dataValue) => {
    const { features } = geoData;
    features.forEach((one) => {
      const name = one && one.properties && one.properties.name;
      dataValue.forEach((item) => {
        if (name.includes(item.name)) {
          one.value = item.value;
        }
      });
    });

    const geoDv = new DataSet.View().source(geoData, { type: 'GeoJSON' });
    return geoDv;
  };

  render() {
    const area = [
      { key: '10105', name: '广东', value: 2111 },
      { key: '10124', name: '上海', value: 4343 },
      { key: '10101', name: '北京', value: 3123 },
    ];
    const { chinaGeo } = this.state;
    if (!chinaGeo) {
      return '数据加载中...';
    }
    const data = this.processGeoData(chinaGeo, area);
    const scale = {
      latitude: {
        sync: true,
        nice: false,
      },
      longitude: {
        sync: true,
        nice: false,
      },
      value: {
        formatter: val => ((val || 0)*100).toFixed(2)+'%',
      },
    };
    return [
      <div key="1" style={{ position: 'relative' }}>
        <Chart height={400} width={500} scale={scale} data={data} padding={[0, 0, 0, 80]}>
          <Geom
            type="polygon"
            position="longitude*latitude"
            // style={{ lineWidth: 1, stroke: '#505050' }}
            color={['value', (value)=>{
                //some code
                  if(value > 0)
                    return '#f04663';
                  else
                    return '#73bdfc';
                }]}
            // color={['value', ['#d9f4ff', '#33c5f6']]}
            tooltip={[
              'name*value',
              (name, value) => value ? {
                name,
                value:  ((value || 0)*100).toFixed(2)+'%',
              } : {
                  value: '无'
              },
            ]}
          >
            <Tooltip showTitle={false} />
            {/* <Legend
              position="bottom-left"
              offsetY={-130}
              offsetX={-60}
              slidable={false}
              width={320}
            /> */}
          </Geom>
        </Chart>
      </div>,
    ];
  }
}

export default MapChart
