
import { connect } from "react-redux"
import Analysis from "./analysis"
import { getIllegalICPData, getNoICPData, getAoDunData, getPersonalAuthData, getSealRecordData, getCompanyAuthData, getRealTimeData } from "../../actionCreator/dashboard"

const mapDispatchToProps = (dispatch) => {
    return {
        // setLoading: (data) => dispatch(setLoading("SET_LOADING",data)),
        getNoICPData: (data) => dispatch(getNoICPData(data)),
        getIllegalICPData: (data) => dispatch(getIllegalICPData(data)),
        getAoDunData: (data) => dispatch(getAoDunData(data)), 
        getPersonalAuthData: (data) => dispatch(getPersonalAuthData(data)),
        getSealRecordData: (data) => dispatch(getSealRecordData(data)),
        getCompanyAuthData: (data) => dispatch(getCompanyAuthData(data)),
        getRealTimeData: () => dispatch(getRealTimeData())
    }
}
const mapStateToProps = ({ dashboardReducer }) => {
    // console.log(state)
    return {
        ...dashboardReducer
    }
}
const DashboardContiner = connect(mapStateToProps, mapDispatchToProps)(Analysis)

export default DashboardContiner
