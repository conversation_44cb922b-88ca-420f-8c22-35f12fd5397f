import React, { Component } from 'react';
import { <PERSON> } from 'react-router-dom'
import { Breadcrumb, notification, Input, Row, Col, Card, Form, Button, Popconfirm } from 'antd';
import PoliceRegisterDetailTable from '../../components/PoliceRegisterDetailTable'
import './List.css';
import request from '../../utils/request'
const Search = Input.Search;


const smsEmailStatusDict = {
  0: '待发送',
  1: '发送中',
  2: '发送成功',
  3: '发送失败',
  4: '发送失败',
  5: '发送中',
  6: '禁止发送',
  7: '超时未响应'
}

const batchStatusDict = {
  0: '解析中',
  1: '已解析',
  2: '发送中',
  3: '发送完成',
  4: '已完成',
  5: '重新获取中',
}

class BatchDetail extends Component {
  state = {
    loading: false,
    batchId: parseInt(this.props.match.params.BatchId, 10),
    Type: parseInt(this.props.match.params.Type, 10),
    batchStatus: "",
    batchInfo: {},
    batchInfoLoading: false,
    allSendSuccess: true,
    expandForm: false, // 是否展开
    list: [], // 列表展示值
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    },
  };

  async finishBatch(BatchId) {

    let options = {
      BatchId,
    }

    try {
      let result = await request('EndPoliceRegisterBatch', options)

      if (result.RetCode !== 0) throw Error(result.Message)

      let { batchInfo } = this.state;
      batchInfo.Status = 4
      this.setState({
        batchInfo,
      })

    } catch (err) {
      notification['error']({
        message: '结束失败',
        description: err.message || '内部错误'
      })
    }

  }

  // 查询该批次的状态
  async getBatchInfo() {
    let options = {
      BatchId: this.state.batchId,
    }

    try {
      let infos = await request('GetPoliceRegisterBatchList', options)

      if (infos.RetCode !== 0) throw Error(infos.Message)
      if (infos.List.length !== 1) throw Error(`未获取到该批次的信息: ${options.BatchId}`)

      let info = infos.List[0]
      info.StatusCN = batchStatusDict[info.Status]

      this.setState({
        batchInfo: info,
        batchInfoLoading: true,
      })

      return info
    } catch (err) {
      notification['error']({
        message: '获取当前批次信息失败',
        description: err.message || '内部错误'
      })
    }

  }

  // 查询列表
  async fetch(options = {}) {
    // 初始化options
    options.Page = options.current - 1 || 0
    options.Limit = options.pageSize || 20
    options.BatchId = options.BatchId || this.state.batchId

    try {

      let data = await request('GetPoliceRegisterListByBatchId', options)

      if (data.RetCode !== 0) throw Error('data.Message')

      const list = data.List.map(item => {
        item.SmsStatus = smsEmailStatusDict[item.SmsStatus];
        item.EmailStatus = smsEmailStatusDict[item.EmailStatus];
        return item;
      })

      // 只有当首页时，判断该批次是否有失败状态
      if (options.Page === 0 && options.updateSendStatus) {
        let allSendSuccess = list.every(item => item.SmsStatus === '发送成功' && item.EmailStatus === '发送成功')
        this.setState({
          allSendSuccess,
        })
      }

      this.setState({
        list,
        pagination: {
          current: options.Page + 1,
          pageSize: options.Limit,
          total: data.Count,
        }
      })


    } catch (err) {
      // 报错
      notification['error']({
        message: '获取列表失败',
        description: err.message || '内部错误'
      })

      // 清空列表
      this.setState({
        list: [],
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0
        }
      })

    }
  }

  // 挂载前查询
  componentDidMount() {
    this.fetch({ "BatchId": this.state.batchId, updateSendStatus: true })
    this.getBatchInfo()
  }

  // 导航
  breadcrumb() {
    return (
      <Row>
        <Col xs={24} sm={12} md={12} lg={12}>
          <Breadcrumb>
            <Breadcrumb.Item><Link to="/PoliceRegister/BatchList">公安未备案通知</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              批次详情
            </Breadcrumb.Item>
          </Breadcrumb>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12}>
          <div>
            <Search
              placeholder="请输入域名, 敲击回车完成搜索"
              onSearch={this.searchValue.bind(this)}
            />
          </div>
        </Col>
      </Row>
    );
  }

  statusFilter(filtersArg) {

    let options = {

    }
    let smsEmailStatusReverseDict = Object.keys(smsEmailStatusDict).reduce((prev, k) => {
      let v = smsEmailStatusDict[k]
      if (prev[v]) {
        prev[v].push(Number(k))
      } else {
        prev[v] = [Number(k)]
      }
      return prev
    }, {})

    if (filtersArg.SmsStatus) {
      let SmsStatus = []

      filtersArg.SmsStatus.forEach(item => {
        SmsStatus.push(...smsEmailStatusReverseDict[item])
      })
      options.SmsStatus = SmsStatus
    }

    if (filtersArg.EmailStatus) {
      let EmailStatus = []

      filtersArg.EmailStatus.forEach(item => {
        EmailStatus.push(...smsEmailStatusReverseDict[item])
      })

      options.EmailStatus = EmailStatus
    }
    return options
  }


  handleRealnameTableChange(pagination, filtersArg) {


    let options = {
      ...pagination,
    }

    if (filtersArg.SmsStatus || filtersArg.EmailStatus) {
      options = Object.assign(options, this.statusFilter(filtersArg))
    }


    this.fetch(options)
  }

  async searchValue(v) {
    // 该功能暂时无法使用
    let value = v.trim()

    let options = {
      batchId: this.state.batchId,
      Domain: value,
    }

    this.fetch(options)
  }

  reSendBUEmail({ Id }) {
    const { BUNotificationInfo } = this.state
    request('RetryIllegalNotifyBUById', { Id })
      .then((resp) => {
        if (resp.RetCode === 0) {
          const index = BUNotificationInfo.findIndex(item => item.Id === Id)
          BUNotificationInfo[index].TaskStatus = "发送中"

          this.setState({
            BUNotificationInfo: JSON.parse(JSON.stringify(BUNotificationInfo))
          })

          notification['open']({
            message: '发送中',
            description: '已经发送成功，状态会在刷新之后改变'
          })
          return
        }

        notification['error']({
          message: resp.RetCode,
          description: resp.message || '内部错误'
        })
      })
      .catch(err => {
        notification['error']({
          message: '通知失败',
          description: err.message || '内部错误'
        })
      })
  }

  async NotifyEveryone({
    BatchId,
  }) {

    try {

      let data = await request('NotifyPoliceRegisterBatch', { BatchId })
      if (data.RetCode !== 0) throw Error(data.Message)


      notification['open']({
        message: '发送中',
        description: '正在发送短信和邮件，以及获取状态，该过程会比较漫长，请耐心等待',
      })

      let { list, batchInfo } = this.state;
      list = list.map(item => {
        if(item.SmsStatus === '发送失败'){
          item.SmsStatus = '发送中'
        }

        if(item.EmailStatus === '发送失败'){
          item.EmailStatus = '发送中'
        }
        return item
      })

      // 将该批次的状态手动设置成发送中
      batchInfo.Status = 2
      batchInfo.StatusCN = batchStatusDict[batchInfo.Status]

      this.setState({
        list,
        batchInfo,
      })

    } catch (err) {
      notification['error']({
        message: '通知失败',
        description: err.message || '内部错误'
      })
    }

  }

  //如果批次状态是处理中，只可以批量发送通知、如果不是处理中，则可重发通知。
  //TODO 如果是已经完成状态禁用批量发送与批量重试
  notifyAndRetry() {
    return (
      <Popconfirm title="是否确认批量发送？" loading={this.state.batchInfoLoading} onConfirm={() => this.NotifyEveryone.bind(this)({ BatchId: this.state.batchId })}>
        <Button type="primary" disabled={[0, 2, 4].includes(this.state.batchInfo.Status) || this.state.allSendSuccess} >
          {[0, 1].includes(this.state.batchInfo.Status) ? '批量发送' : '批量重发'}
        </Button>
      </Popconfirm>
    )

  }

  // 按钮行
  bulkSend() {
    return (
      <div>
        {this.notifyAndRetry()}
        &nbsp;
        &nbsp;
        <Popconfirm title="是否确定完成了通知？" loading={this.state.loading} onConfirm={() => this.finishBatch(this.state.batchId)}>
          <Button type="primary" disabled={![3].includes(this.state.batchInfo.Status)}  >
            完成处理
          </Button>
        </Popconfirm>
      </div>
    )
  }

  render() {
    return (
      <Card bordered={false}>
        <div>
          <div>
            <Card bordered={false}>
              {this.breadcrumb()}
            </Card>
            <Card title="批次详情" style={{ marginBottom: 24 }} bordered={false} >
              {this.bulkSend()}
              <PoliceRegisterDetailTable
                onChange={this.handleRealnameTableChange.bind(this)}
                data={this.state.list}
                pagination={this.state.pagination}
                batchId={this.state.batchId}
                Type={this.state.Type}
              />
            </Card>
          </div>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(BatchDetail);
export default ListForm;
