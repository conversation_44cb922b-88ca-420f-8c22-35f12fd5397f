import React, { Component } from 'react';
import { notification, Select, Row, Col, DatePicker, Card, Form, Input, Icon, Button, message, Progress } from 'antd';
import PoliceRegisterTable from '../../components/PoliceRegisterTable'
import './List.css';
import request,{icpApi} from '../../utils/request'
import Upload from 'rc-upload';
import moment from 'moment';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const Option = Select.Option;
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',');

//状态枚举,将英文状态转成中文在前端显示
const statusList = {
  0 : "解析中",
  1 : "已解析",
  2 : "发送中",
  3 : "发送完成",
  4 : "已完成",
  5 : "重新获取中",
}

const typeList = {
  '1': '首次通知',
  '2': '再次通知',
}

class List extends Component {
  constructor(){
    super()
    this.state = {
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      loading: false,
      fileList: [],
      formValues: {},
      updateType: '1',
      ProcessStatus: 'normal',
      percent: 0
    };
  }

  // 查询列表
  fetch(options = {}) {

    // 初始化options中的分页
    options.Page = options.Page - 1 || 0
    options.Limit = options.Limit || 20

    this.setState({ loading: true })

    request('GetPoliceRegisterBatchList', options)
      .then(resp => {
        if (resp.RetCode !== 0) {
          return Promise.reject({
            message: resp.Message
          })
        }

        let batchList = resp.List.map(item => {
          item.TypeCH = typeList[item.Type]
          item.StatusCH = statusList[item.Status]
          return item
        })


        this.setState({
          list: batchList || [],
          pagination: {
            current: options.Page + 1,
            pageSize: options.Limit,
            total: resp.Count || 0
          },
          loading: false
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })

        // 清空列表
        this.setState({
          loading: false,
          list: [],
          pagination: {
            current: 1,
            pageSize: 20,
            total: 0
          }
        })
        return;
      })
  }

  // 挂载前查询
  componentDidMount() {
    this.fetch({})
  }
  // 获取excel第一行的内容
  getList1(wb) {
    var wbData = wb.Sheets[wb.SheetNames[0]]; // 读取的excel单元格内容
    var re = /^[A-Z]1$/; // 匹配excel第一行的内容
    var arr1 = [];
    for (var key in wbData) { // excel第一行内容赋值给数组
        if (Object.prototype.hasOwnProperty.bind(wbData,key)) {
            if (re.test(key)) {
                arr1.push(wbData[key].h);
            }
        }
    }
    return arr1;
  }
  // 增加对应字段空白内容
  AddXlsxData(xlsxData, list1) {
    var addData = null; // 空白字段替换值
    for (let i = 0; i < xlsxData.length; i++) { // 要被JSON的数组
        for (let j = 0; j < list1.length; j++) { // excel第一行内容
            if (!xlsxData[i][list1[j]]) {
                xlsxData[i][list1[j]] = addData;
            }
        }
    }
    return xlsxData;
}
  // 处理分页
  handleMessageTableChange(pagination, filtersArg){
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      obj[key] = getValue(filtersArg[key]);
      return obj;
    }, {});

    const params = {
      Page: pagination.current,
      Limit: pagination.pageSize,
      ...filters,
    };
    this.fetch(params)
  }
  dataURLToBlob(dataUrl) {
    const base64 = dataUrl.split(',')[1];
    const binary = atob(base64);
    const array = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      array[i] = binary.charCodeAt(i);
    }
    return new Blob([array], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }
  getSheetValue(file) {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.onload = (event) => {
        try {
          const workbook = XLSX.read(event.target.result, { type: 'binary' });
          const sheetName = workbook.SheetNames[0]; // Assuming the first sheet
          const worksheet = workbook.Sheets[sheetName];
          resolve(XLSX.utils.sheet_to_json(worksheet));
        } catch (error) {
          reject(error);
        }
      };
      fileReader.onerror = reject;
      fileReader.readAsArrayBuffer(file);
    });
  }
  getColumnValues(file, columnName) {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.onload = (event) => {
        try {
          const workbook = XLSX.read(event.target.result, { type: 'binary' });
          const sheetName = workbook.SheetNames[0]; // Assuming the first sheet
          const worksheet = workbook.Sheets[sheetName];
          const columnValues = XLSX.utils.sheet_to_json(worksheet).map(row => row[columnName]);
          resolve(columnValues);
        } catch (error) {
          reject(error);
        }
      };
      fileReader.onerror = reject;
      fileReader.readAsArrayBuffer(file);
    });
  }
  exportExcel(workbook, fileName) {
    const wopts = { bookType: 'xlsx', bookSST: false, type: 'binary' };
    const wbout = XLSX.write(workbook, wopts);

    function s2ab(s) {
      const buf = new ArrayBuffer(s.length);
      const view = new Uint8Array(buf);
      for (let i = 0; i !== s.length; ++i) {
        view[i] = s.charCodeAt(i) & 0xFF;
      }
      return buf;
    }

    const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' });
    saveAs(blob, fileName);
  }
  handleLogoUpload(batchFile) {
    //文件上传，使用FileReader读文件
    let reader = new FileReader();
    reader.readAsDataURL(batchFile);
    reader.onloadend = async () => {
      //处理loadend事件。该事件在读取操作结束时（要么成功，要么失败）触发
      let fileBase64 = reader.result.split(",")[1]
      //reader.result中存放Base64编码,需要对原数据做处理，导入数据格式"data:text/csv;base64,aXAs5Z+f5ZCNLA0KMS4xLjEuMSxhdmkucWlhbmp1bnllLmNvbSwNCjIuMi4yLjIsYXYucWlhbmp1bnllLmNvbSwNCg=="
      request('CreatePoliceRegisterBatch', {
        File: fileBase64,
        Description: batchFile.name,
        Type: Number(this.state.updateType),
      })
        .then(resp => {
          if (resp.RetCode === 0) {
            notification['open']({
              message: '上传成功',
              description: `您本次上传的文件是: ${typeList[this.state.updateType]}, 请核实`,
            })
          } else {
            notification['error']({
              message: '上传失败',
              description: resp.Message || '内部错误'
            })
          }
          this.fetch({})
        })
        .catch(err => {
          // 报错
          // message.info("上传失败", err)
          notification['error']({
            message: '上传失败',
            description: err.message || '内部错误'
          })
        })
    };
    //Prevent file uploading
    return false;
  }
  reGenerateFile(batchFile) {
    //文件上传，使用FileReader读文件
    let reader = new FileReader();
    reader.readAsDataURL(batchFile);
    reader.onloadend = async () => {


      const blob = this.dataURLToBlob(reader.result);
      const file = new File([blob], batchFile.name, { type: blob.type });
      let originalSheetData = await this.getSheetValue(file)
      let columnValues = await this.getColumnValues(file, '域名')
      let companyIds =  await this.multiRequest(columnValues,6)
      this.setState({
        percent: 100,
        ProcessStatus: 'success'
      })
      const mergedSheetData = originalSheetData.map((row, index) => {
        return ({
          ...row,
          ['公司Id']: companyIds[index],
        })
      });
      const workbook = XLSX.utils.book_new();
      const newSheet = XLSX.utils.json_to_sheet(mergedSheetData);

      XLSX.utils.book_append_sheet(workbook, newSheet, 'Sheet1');

      message.success('文件导出成功', 2.5)
      this.exportExcel(workbook, 'new_excel_file.xlsx');


    };
    //Prevent file uploading
    return false;
  }
   //根据域名查备案信息
   getCompanyIdByDomain = async(domain) => {
    let domainList = domain.split(';');
    let companyId = null
    for(let domain of domainList){
      const options = {
        "Limit": 100,
        "Offset": 0,
        "Domain": domain,
        "Owner": "ALL",
        "Action": "ICPAdmin.GetICPInfoList"
      }
      let domainForFlag = true
      let res = await icpApi('GetICPInfoList', options)
      if(res.RetCode === 0 && res.ICPs?.length > 0){
        for(let j=0;j<res.ICPs.length;j++){
          let doamins = res.ICPs[j].Website.map(web=>web.Domain).flat().map(domain=>domain.Domain)
          let isExtract = doamins.includes(domain) && res.ICPs[j]?.CompanyId!==-1
          if(isExtract){
            companyId = res.ICPs[j]?.CompanyId
            break;
          }
        }
      }
      if(!domainForFlag){
        break
      }
    }
    if(!companyId){
      return Promise.resolve('无')
    }else{
      return Promise.resolve(companyId)
    }


  }
  //根据域名查询订单信息
  GetFuzzyOrderList = async(domain) => {
    let domainList = domain.split(';');
    let companyId = null
    for(let domain of domainList){
      const options = {
        "Limit": 100,
        "Offset": 0,
        "Domain": domain,
        "Owner": "ALL",
        "Action": "ICPAdmin.GetFuzzyOrderList"
      }
      let domainForFlag = true
      let res = await icpApi('GetFuzzyOrderList', options)
      if(res.RetCode === 0 && res.Orders?.length > 0){
        for(let j=0;j<res.Orders.length;j++){
          let doamins = res.Orders[j].Website.map(web=>web.Domain).flat().map(domain=>domain.Domain)
          let isExtract = doamins.includes(domain)
          if(isExtract){
            companyId = res.Orders[j]?.CompanyId
            break;
          }
        }
      }
      if(!domainForFlag){
        break
      }
    }
    if(!companyId){
      let twiceRes = this.getCompanyIdByDomain(domain)
      return Promise.resolve(twiceRes)
    }else{
      return Promise.resolve(companyId)
    }


  }
  // 重置搜索框
  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();
    this.setState({
      formValues: {},
    });

    this.fetch()
  }

  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  }

  // 搜索
  handleSearch = (e) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
      };

      if (typeof (values.Time) === 'object') {
        let BeginTime, EndTime
        // 格式转换
        BeginTime = moment(values.Time[0]._d).startOf('day').format("X")
        EndTime = moment(values.Time[1]._d).endOf('day').format("X")

        values.BeginTime = parseInt(BeginTime, 10)
        values.EndTime = parseInt(EndTime, 10)

      }

      this.setState({
        formValues: values
      })

      delete values.Time
      delete values.Type
      //去搜索条件中的前后空格， 没写
      this.fetch(values)
    });
  }

  // 更改上传文件的类型
  typeSelector(values) {
    this.setState({
      updateType: values
    })
  }

  multiRequest(doamins = [], maxNum) {
    let result = new Array(doamins.length).fill(false)
    let sum = doamins.length; //总数
    let count = 0;             //已完成数
    let that = this
    return new Promise((resolve) => {
      //先请求maxNum个呗
      while (count < maxNum) {
        next()
      }
      function next() {
        let current = count++
        // 边界
        if (current >= sum) {
          !result.includes(false) && resolve(result)
          return result
        }
        let doamin = doamins[current];

        that.GetFuzzyOrderList(doamin).then((res) => {
          that.setState({
            percent: Math.ceil(count*100/sum)
          })
          result[current] = res
          //还有未完成，递归；
          if (current < sum) {
            next()
          }
        }).catch((err) => {
          result[current] = err
          if (current < sum) {
            next()
          }
        })
      }
    })
  }
  //复杂搜索框
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    let { percent,ProcessStatus } = this.state
    return (
      <Form onSubmit={this.handleSearch} layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={6} key={1} /* style={{ display: 'block' }} */>
            <FormItem label="域名">
              {getFieldDecorator('Domain')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={2} style={{ overflow: 'hidden' }} >
            <FormItem style={{ width: '100%' }} label="时间">
              {getFieldDecorator('Time')(
                <RangePicker onChange={this.timeSelector} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={4} >
            <FormItem style={{ width: '100%' }} label="通知类型: ">
              <Select defaultValue="首次通知" style={{ width: "100%" }} onChange={this.typeSelector.bind(this)}>
                <Option value="1">首次通知</Option>
                <Option value="2">再次通知</Option>
              </Select>
            </FormItem>
          </Col>

        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={1} >
            <Progress type="circle" percent={percent} status={ProcessStatus} width={40}/>
          </Col>
          <Col span={6} >
            <FormItem style={{ width: '100%' }} label="">
            <span style={{  marginBottom: 24 }}>
                <Upload
                  action="temp/"
                  listType="picture-card"
                  fileList={this.state.reGenerateFile}
                  beforeUpload={this.reGenerateFile.bind(this)}
                >
                  <Button>
                    <Icon type="upload" /> {(percent===0|| percent===100)?'重新生成xlsx（根据域名，补齐公司Id':'正在生成中'}
                  </Button>
                </Upload>
              </span>
            </FormItem>
          </Col>
          <Col span={2} offset={1}>
            <FormItem style={{ width: '100%' }} label="">
              <span style={{ marginBottom: 24 }}>
                <Upload
                  action="temp/"
                  listType="picture-card"
                  fileList={this.state.fileList}
                  beforeUpload={this.handleLogoUpload.bind(this)}
                >
                  <Button>
                    <Icon type="upload" /> 上传
                  </Button>
                </Upload>
              </span>
            </FormItem>
          </Col>

          <Col span={8} key={3}>
            <FormItem style={{ width: '100%' }} label="">
              <Button style={{ marginRight: "26px" }} htmlType="submit" type='primary'>查询</Button>
              <Button onClick={this.handleFormReset}>重置</Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }

  // 根据是否展开获取对应的表单形式
  renderForm() {
    return this.renderAdvancedForm()
  }

  render() {
    return (
      <Card bordered={false}>
        <div>
          <Card title="搜索" style={{ display: this.state.expandForm ? 'none' : 'block', marginBottom: 24 }} bordered={false} >
            {this.renderForm()}
          </Card>

          <Card title="结果" style={{ marginBottom: 24 }} bordered={false} >
            <PoliceRegisterTable
              loading={this.state.loading}
              data={this.state.list}
              pagination={this.state.pagination}
              onChange={this.handleMessageTableChange.bind(this)}
            />
          </Card>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(List);
export default ListForm;
