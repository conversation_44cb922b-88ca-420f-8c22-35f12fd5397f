import React, { Component, Fragment } from "react";
import {
  Card,
  Form,
  Table,
  Tooltip,
  Select,
  Tabs,
  Input,
  notification,
} from "antd";
import "./index.css";
import { SensitiveApi } from "../../utils/request";
import { Breadcrumb } from "antd";
const { TabPane } = Tabs;
const { Search } = Input;

//状态枚举,将英文状态转成中文在前端显示
class BatchDetailsPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], // 列表初始值,
      loading: false,
      batchID: this.props.location.search.split("Id=")[1],
      Status: null,
      URL: null,
      Site: null,
      Type: 0,
      pagination: {
        showQuickJumper: true,
        showSizeChanger: true,
        current: 1,
        pageSize: 20,
        total: 0,
      },
    };
  }
  // 挂载前查询
  componentDidMount() {
    console.log(this.props.location.search.split("Id=")[1], "id");
    this.DescribeSensitiveWordBatch();
  }
  onSearch = (value) => {
    this.setState(
      {
        URL: value,
        pagination: {
          showQuickJumper: true,
          showSizeChanger: true,
          current: 1,
          pageSize: 20,
          total: 0,
        },
      },
      () => {
        this.DescribeSensitiveWordBatch();
      }
    );
  };
  onSearchSite = (value) => {
    this.setState(
      {
        Site: value,
        pagination: {
          showQuickJumper: true,
          showSizeChanger: true,
          current: 1,
          pageSize: 20,
          total: 0,
        },
      },
      () => {
        this.DescribeSensitiveWordBatch();
      }
    );
  };
  changeSearch = (value) => {
    console.log(value);
    // this.setState({
    //     URL:value
    // })
  };
  DescribeSensitiveWordBatch = () => {
    let self = this,
      action = "DescribeSensitiveWordBatch",
      options = {
        Action: action,
        Id: parseInt(this.state.batchID),
        Limit: this.state.pagination.pageSize,
        Offset:
          this.state.pagination.pageSize * (this.state.pagination.current - 1),
      };
    if (this.state.Status !== null) {
      options["Status"] = this.state.Status;
    }
    if (this.state.URL) {
      options["URL"] = this.state.URL;
    }
    if (this.state.Site) {
      options["Site"] = this.state.Site;
    }
    self.setState({ loading: true });
    SensitiveApi(action, options)
      .then((resp) => {
        let message = "查询成功";
        self.setState({
          loading: false,
        });
        if (resp.RetCode === 0) {
          let { current, pageSize } = this.state.pagination;
          self.setState({
            list: resp.Rows,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: current,
              pageSize: pageSize,
              total: resp.Total,
            },
          });
          return;
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };

  handleTableChange = (pagination, filters, sorter) => {
    console.log(pagination, filters, sorter);
    if (filters.Status && filters.Status.length > 0) {
      this.setState({
        Status: filters.Status[0],
      });
    } else {
      this.setState({
        Status: null,
      });
    }
    this.setState(
      {
        pagination: {
          showQuickJumper: true,
          showSizeChanger: true,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: this.state.pagination.total,
        },
      },
      () => {
        this.DescribeSensitiveWordBatch();
      }
    );
  };
  render() {
    let { loading, list, pagination } = this.state;
    const columns1 = [
      {
        title: "敏感词",
        dataIndex: "KeywordList",
      },
      {
        title: "URL",
        dataIndex: "Url",
        render(val) {
          return (
            <Fragment>
              <Tooltip title={val}>
                <span>
                  {val.length > 50 ? val.substring(0, 50) + "..." : val}
                </span>
              </Tooltip>
            </Fragment>
          );
        },
      },
      {
        title: "渠道",
        dataIndex: "Site",
      },
      {
        title: "处理状态",
        dataIndex: "Status",
        filterMultiple: false,
        filters: [
          {
            text: "待处理",
            value: 0,
          },
          {
            text: "已完成",
            value: 1,
          },
        ],
        render: (val) => {
          return val === 1 ? "已完成" : "待处理";
        },
      },
      {
        title: "操作人",
        dataIndex: "Operator",
      },
      {
        title: "备注",
        dataIndex: "Remark",
      },
      {
        title: "操作",
        render: (val, row) => {
          let urlAddress = `/SensitiveWords/SensitiveBatchDetails/BatchApply?params=${encodeURI(
            JSON.stringify({ Id: row.Id })
          )}`;
          return (
            <a href={urlAddress} rel="noreferrer" target="_blank">
              处理
            </a>
          );
        },
      },
    ];
    const mailStatusDict = {
      0: "URL",
      1: "渠道",
    };
    return (
      <Card bordered={true}>
        {this.state.Type ? (
          <Search
            placeholder="输入渠道"
            onSearch={this.onSearchSite}
            onChange={this.changeSearch}
            style={{ width: 400, display: "float", float: "right" }}
          />
        ) : (
          <Search
            placeholder="输入URL"
            onSearch={this.onSearch}
            onChange={this.changeSearch}
            style={{ width: 400, display: "float", float: "right" }}
          />
        )}
        <Select
          placeholder="请选择类型"
          style={{ width: 100, display: "float", float: "right" }}
          defaultValue={0}
          onChange={(v) => {
            if (v === 0) {
              this.setState({
                Site: null,
              });
            }
            if (v === 1) {
              this.setState({
                URL: null,
              });
            }
            this.setState({
              Type: v,
            });
          }}
        >
          {Object.keys(mailStatusDict).map((status) => (
            <Select.Option key={status} value={parseInt(status, 10)}>
              {mailStatusDict[status]}
            </Select.Option>
          ))}
        </Select>
        <Breadcrumb style={{ marginBottom: 30 }}>
          <Breadcrumb.Item>
            <a href="/SensitiveWords">广告敏感词审核</a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>批次详情</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <Tabs defaultActiveKey="TaskList">
            <TabPane tab="批次详情" key="GetCopyTaskList">
              <Table
                loading={loading}
                rowKey={(record) => record.Id}
                dataSource={list}
                columns={columns1}
                pagination={pagination}
                onChange={this.handleTableChange}
              />
            </TabPane>
          </Tabs>
        </div>
      </Card>
    );
  }
}

const SensitiveBatchDetails = Form.create()(BatchDetailsPage);
export default SensitiveBatchDetails;
