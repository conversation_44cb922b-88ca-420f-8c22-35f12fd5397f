import React, { Component } from 'react';
import { Card, Row, Col, Input, Table, Button, Tooltip, notification, Popconfirm, Modal } from 'antd';
import { SensitiveApi } from '../../utils/request';
import WhiteListFormWrapper from '../../components/SensitiveWords/WhiteListForm';
import moment from 'moment';
const { Search } = Input;

class WhiteListForm extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            visible: false,
            Word: "",
            pagination: {
                current: 1,
                pageSize: 20,
                total: 20
            },
        };
    }
    handleMessageTableChange() {
        this.GetGlobalWhiteList()
    }
    // 挂载前查询
    componentDidMount() {
        this.GetGlobalWhiteList();
    }
    onSearch = () => {
        this.setState({
            pagination: {
                current: 1,
                pageSize: 20,
                total: 20
            },
        }, () => {
            this.GetGlobalWhiteList()
        })
    }
    GetGlobalWhiteList = (v) => {
        const { Word, pagination } = this.state
        let options = {
            Action: 'GetGlobalWhiteList',
            Limit: pagination.pageSize,
            Offset: pagination.pageSize * (pagination.current - 1)
        }
        if (Word) {
            options['Word'] = Word
        }
        if(v){
            options['Offset'] = 0
        }
        this.setState({ loading: true })
        SensitiveApi('GetGlobalWhiteList', options)
            .then(resp => {
                let message = '查询成功'
                this.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                    const { current, pageSize } = this.state.pagination
                    this.setState({
                        list: resp.Rows,
                        pagination: {
                            current: current,
                            pageSize: pageSize,
                            total: resp.TotalCount,
                        }
                    })
                    return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }

    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.handleMessageTableChange()
        })
    }
    AddGlobalWhiteWord = (values) => {
        let options = {
            Action: 'AddGlobalWhiteWord',
            Remark: values.Remark,
            Words: values.Words
        }
        this.setState({ loading: true })
        SensitiveApi('AddGlobalWhiteWord', options)
            .then(resp => {
                let message = '添加成功'
                this.setState({
                    loading: false,
                    visible: false
                })
                if (resp.RetCode === 0) {
                    this.GetGlobalWhiteList();
                    return
                } else {
                    message = resp.Message || resp.RetCode + "添加失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            });

    }
    deleteData = (val) => {
        let options = {
            Action: 'ModifyGlobalWhiteWord',
            Id: val,
            Enable: 0
        }
        this.setState({ loading: true })
        SensitiveApi('ModifyGlobalWhiteWord', options)
            .then(resp => {
                let message = '删除成功'
                this.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                    this.GetGlobalWhiteList();
                    return
                } else {
                    message = resp.Message || resp.RetCode + "删除失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    render() {
        let { loading, list, pagination, visible } = this.state;
        pagination = {
            ...pagination,
            showQuickJumper: true,
            showSizeChanger: true,
        }
        const columns1 = [
            {
                title: '加白词',
                dataIndex: 'Word',
                render: (val) => <Tooltip title={val}>{val && val.length > 30 ? val.substring(0, 30) + '...' : val}</Tooltip>
            },
            {
                title: '备注',
                dataIndex: 'Remark',
            },
            {
                title: '操作人',
                dataIndex: 'Operator',
            },
            {
                title: '操作时间',
                dataIndex: 'UpdateTime',
                render: val => <span>{val?moment(val * 1000).format('YYYY-MM-DD HH:mm:ss'):val}</span>,
            },
            {
                title: '操作',
                dataIndex: 'Id',
                render: val => <Popconfirm placement="top" title={'确定删除吗？'} onConfirm={() => this.deleteData(val)} okText="Yes" cancelText="No">
                    <a >删除</a></Popconfirm>,
            }
        ];
        console.log(list, 'list')
        return (
            <Card bordered={false}>
                <div>
                    <Row>
                        <Col span={18}>
                            <Button onClick={() => this.setState({ visible: true })} type="primary">全局加白</Button>
                        </Col>
                        <Col span={6}>
                            <Search placeholder='请输入想搜索的加白词' style={{ width: 300, marginRight:200 }} onSearch={v => {
                                this.setState({ Word: v }, () => this.GetGlobalWhiteList(v))
                            }}></Search>
                        </Col>
                    </Row>
                    <Table
                        loading={loading}
                        dataSource={list}
                        columns={columns1}
                        pagination={pagination}
                        onChange={this.handleTableChange}
                    />
                    <Modal visible={visible} title="全局加白表单" footer={null} onCancel={() => this.setState({ visible: false })}>
                        <WhiteListFormWrapper AddGlobalWhiteWord={this.AddGlobalWhiteWord} cancel={() => this.setState({ visible: false })} />
                    </Modal>
                </div>
            </Card>
        )
    }
}

export default WhiteListForm;
