import React, { Component ,Fragment} from 'react';
import { Card,Row,Form,Col,Input, Table, Button, Tabs,notification, DatePicker } from 'antd';
import './index.css';
import exportFile from '../../components/expoertFile/index';
import { SensitiveApi } from '../../utils/request';
import moment from 'moment';
import { Link } from 'react-router-dom';
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const FormItem = Form.Item;
//状态枚举,将英文状态转成中文在前端显示
class SensitiveWord extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            batchID: "",
            URL: "",
            CreatedBegin: "",
            CreatedEnd:"",
            pagination: {
                current: 1,
                pageSize: 20,
                total: 20
            },
        };

    }
    handleMessageTableChange() {
        this.GetSensitiveWordBatchList()
    }
    // 挂载前查询
    componentDidMount() {
        this.GetSensitiveWordBatchList();
    }
    //下载excel
  async downloadExcelFile(row = {},Id) {
    try {
      if (row.length === 0) {
        notification['error']({
          message: '无下载内容',
          description: '无下载内容'
        })
        return
      }
      let dataList = row.reduce((prev, item) => {
        prev.push([
          item.KeywordList.replaceAll(',','-') || '',
          item.Site || '',
          item.Url || '',
          moment(item.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss') || '',
          item.Operator.replaceAll(',','-') || '',
          item.Remark.replaceAll(',','-') || ''
        ]);
        return prev;
      }, [["敏感词", "渠道", "URL", "创建时间", "操作人", "备注"]]);
      let culumnWidthArray = [30, 30, 30, 10, 10, 30];
      let fileName = '下载批次-' + Id + ".xlsx";
      exportFile(dataList, culumnWidthArray, fileName);
    } catch (err) {
      notification['error']({
        message: '当前下载任务信息失败',
        description: err.message || '内部错误'
      })
    }
  }
    onSearch = ()=>{
        this.setState({
            pagination: {
                current: 1,
                pageSize: 20,
                total: 20
            },
        },()=>{
            this.GetSensitiveWordBatchList()
        })
    }
    GetSensitiveWordBatchList = ()=> {
        let { batchID,URL,CreatedBegin,CreatedEnd,pagination } = this.state
        let options = {
            Action :'GetSensitiveWordBatchList',
            Id: parseInt(batchID),
            CreatedBegin:CreatedBegin,
            CreatedEnd:CreatedEnd,
            URL:URL,
            Limit: pagination.pageSize,
            Offset: pagination.pageSize * (pagination.current - 1)
        }
        if(!batchID){
            delete options.Id
        }
        if(!URL){
            delete options.URL
        }
        if(!CreatedBegin){
            delete options.CreatedBegin
        }
        if(!CreatedEnd){
            delete options.CreatedEnd
        }
        this.setState({ loading: true })
        SensitiveApi('GetSensitiveWordBatchList', options)
            .then(resp => {
                let message = '查询成功'
                this.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                        let {current,pageSize} = this.state.pagination
                        this.setState({
                            list: resp.Rows,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.Total,
                            }
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }

    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.handleMessageTableChange()
        })
    }

    //上部份，获取信息
    renderAdvancedForm() {
        return (
            <Form layout="inline" className="ant-advanced-search-form">
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144444}>
                        <FormItem label="批次ID">
                            <Input style={{ width: '100%' }} value={this.state.batchID} placeholder="请输入对应的批次ID" onChange={(e) => { this.setState({ batchID: e.target.value }) }} />
                        </FormItem>
                    </Col>
                    <Col span={10} key={1555}>
                        <FormItem label="URL">
                            <Input style={{ width: '100%' }} value={this.state.URL} placeholder="请输入对应的URL" onChange={(e) => { this.setState({ URL: e.target.value }) }} />
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144664}>
                    <FormItem label="时间">
                    <RangePicker
                        style={{width:'100%'}}
                        ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
                        showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
                        format="YYYY-MM-DD"
                        onChange= {(v)=>{
                            this.setState({
                                CreatedBegin: moment(v[0]?.format("YYYY-MM-DD")).unix(),
                                CreatedEnd:moment(v[1]?.format("YYYY-MM-DD")).unix(),
                            },()=>{
                                console.log(moment(v[0]?.format("YYYY-MM-DD")).unix(),moment(v[1]?.format("YYYY-MM-DD")).unix())
                            })
                        }}
                    />
                    </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={6} key={2} >
                        <FormItem style={{ width: '100%', marginLeft: "80px" }} label="">
                            <Button style={{ marginRight: "16px" }} onClick={this.onSearch} htmlType="submit">查询</Button>
                            <Button onClick={() => {
                                window.location.reload();
                            }}>重置</Button>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        );
    }
    download = (Id)=>{
        console.log(Id,'Id')
        let self = this,
            action = 'DescribeSensitiveWordBatch',
            options = {
                    Action: action,
                    Id: parseInt(Id),
                    GetAll: 1,
            }
            SensitiveApi(action, options)
                .then(resp => {
                    let message = '查询成功'
                    self.setState({
                        loading: false,
                    })
                    if (resp.RetCode === 0) {
                        if (resp.Rows) {
                            this.downloadExcelFile(resp.Rows,Id)
                        }
                        return
                    } else {
                        message = resp.Message || resp.RetCode + "查询失败"
                    }
                    notification.open({
                        message: message,
                    });
                }).catch(err => {
                    // 报错
                    notification['error']({
                        message: '请求失败',
                        description: err.message || '内部错误'
                    })
                    return;
                })

    }
    render() {
        let { loading, list, pagination  } = this.state;
        pagination = {
            ...pagination,
            showQuickJumper: true,
            showSizeChanger: true,
        }
        const columns1 = [
            {
                title: '批次ID',
                dataIndex: 'Id',
            },
            {
                title: '批次名称',
                dataIndex: 'Remark',
            },
            {
                title: '创建时间',
                dataIndex: 'CreateTime',
                render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
            },
            {
                title: '批次详情',
                dataIndex: 'AuthType',
                render: (val, row) => {
                    let urlAddress = "/SensitiveWords/SensitiveBatchDetails/?Id=" + row.Id
                    return (
                      <Fragment>
                        {
                          <Link to={urlAddress} target="_blank">查看</Link>
                        }
                      </Fragment>
                    )
                  },
            },
            {
                title: '导出Excel',
                render: (val, row) => {
                    let id = row.Id
                    return (
                        <Fragment>
                            <Button  type="download"  data-key = {id} onClick = {(e) => this.download(e.target.dataset.key)}>下载</Button>
                        </Fragment>
                    )
                },
            }
        ];
        console.log(list,'list')
        return (
            <Card bordered={false}>
                <div>
                    <Card title="搜索" style={{marginBottom: 24 }} bordered={false} >
                        {this.renderAdvancedForm()}
                    </Card>
                    <Tabs defaultActiveKey="TaskList">
                        <TabPane tab="结果" key="GetList">
                            <Table
                                loading={loading}
                                dataSource={list}
                                columns= {columns1}
                                pagination={pagination}
                                onChange={this.handleTableChange}
                            />
                        </TabPane>
                    </Tabs>
                </div>
            </Card>
        )
    }
}

const SensitiveWords = Form.create()(SensitiveWord);
export default SensitiveWords;
