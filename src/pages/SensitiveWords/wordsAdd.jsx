import React, { Component } from 'react';
import PropTypes from 'prop-types';
// import { Textarea,EditableList } from '@ucloud-fe/react-components';
import { Row, Col,Form, Input} from 'antd';
import cloneDeep from "lodash/cloneDeep"
import EditableList from '../../components/EditableList'
const { TextArea } = Input;
const FormItem = Form.Item;

let uid = 0;
let generateData = ({ name, desc, deletable } = {}) => {
    const id = uid++;
    return {
        key: id,
        name:name || "",
        desc: desc || `desc-${id}`,
        deletable
    };
};

class WordEditor extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[]
        };
    }
    componentDidMount(){
        let { dataSource } = this.state;
        this.props.defaultValue.map(item => {
            dataSource.push(generateData({name:item ,deletable:true}))
        });
        this.setState({ dataSource:dataSource },()=>{
            let array = [];
            dataSource.forEach(item=>{
                if(item.name){
                     array.push(item.name)
                }
            })
            this.props.onChange(array)
        });
    }
    handleDelete(record) {
        let { dataSource } = this.state;
        let key = record.key;
        this.setState({ dataSource: dataSource.filter(item => item.key !== key) },()=>{
            let array = []
            this.state.dataSource.forEach(item=>{
                if(item.name){
                    array.push(item.name)
                }
             })
            this.props.onChange(array)
         });
    }
    handleAdd() {
        let { dataSource } = this.state;
        dataSource.push(generateData());
        this.setState({ dataSource:dataSource },()=>{
            let array = []
            this.state.dataSource.forEach(item=>{
                if(item.name){
                    array.push(item.name)
                }
             })
            this.props.onChange(array)
        });
    }
    render() {
        let { dataSource } = this.state;
        let renderItem = item => {
            return <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
            <Col span={10} key={item.key+"88888888888888"} style={{width:"100%"}}>
                <FormItem name ={"加白:"} label={"加白"+`${item.key+1}`+":"}  rules={[{ required: true, message: 'Please input your word!' }]} >
                    <TextArea style = {{ height:"80px",marginLeft:"10px"}} size={'lg'} value = {item.name} onChange ={(e)=>{
                         let newData = cloneDeep(this.state.dataSource)
                         newData.map( data =>{
                             if(data.key === item.key){
                                 data.name = e.target.value
                             }
                             return data
                         })
                         this.setState({
                             dataSource: newData
                         },()=>{
                             let array = []
                             newData.forEach(item=>{
                                if(item.name){
                                    array.push(item.name)
                                }
                             })
                             this.props.onChange(array)
                         })
                    }}/>
                </FormItem>
            </Col>
        </Row>
        };
        return (
            <EditableList
                renderItem={renderItem}
                dataSource={dataSource}
                addition={{ onAdd: () => this.handleAdd() }}
                itemDeletion={{
                    onDelete: record => this.handleDelete(record),
                    getDisabledOfItem: record => record.deletable === false
                }}
            />
        );
    }
}

WordEditor.propTypes = {
    onChange: PropTypes.fun,
};

export default WordEditor;
