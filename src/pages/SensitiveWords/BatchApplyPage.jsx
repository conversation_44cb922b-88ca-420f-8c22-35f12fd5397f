import React, { Component } from 'react';
import { Row, Col, Modal, Card, Form, Tooltip,Input, Button, notification } from 'antd';
import './index.css';
import { SensitiveApi } from '../../utils/request';
import { Breadcrumb } from 'antd';
import WordEditor from './wordsAdd';
const FormItem = Form.Item;
class BatchApplyPage extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            params: JSON.parse(decodeURI(this.props.location.search.split("params=")[1])),
            bodyText: "",
            addRemark: "",
            newRemark:"",
            loading: false,
            Word: [],
            modal: false,
            inputShow: false,
            addWordModal:false,
            defaultValue:[]
        };

    }

    // 挂载前查询
    componentDidMount() {
        this.CheckURLSensitiveWord()
    }
    CheckURLSensitiveWord() {
        let self = this,
            action = 'CheckURLSensitiveWord',
            options = {
                Action: action,
                Id: this.state.params.Id,
            }
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                let message = '查询成功'
                self.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                    if (resp.result) {
                        let arr = []
                        resp.result.forEach(item=>{
                            arr.push(item.SubString)
                        })
                        self.setState({
                            defaultValue:arr,
                            params:resp.recordInfo,
                            list: resp.result,
                            bodyText: resp.bodyText,
                            newRemark:resp.recordInfo.Remark
                        }, () => {
                            self.renderAddWhite( resp.result,resp.bodyText)
                        })
                    }
                    return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
   
    UpdateSensitiveWordRecord = (Id, Remark) => {
        let self = this,
            action = 'UpdateSensitiveWordRecord',
            options = {
                Action: action,
                Id: parseInt(Id),
                Remark: Remark
            };
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                let message = '新增成功'
                self.setState({
                    loading: false,
                })
                if (resp.RetCode !== 0) {
                    message = resp.Message || resp.RetCode + "新增失败"
                    notification.open({
                        message: message,
                    });
                }  
                window.location.reload()
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    AddWhiteWordToURL = () => {
        let { Word, params } = this.state
        console.log(Word)
        let self = this,
            action = 'AddWhiteWordToURL',
            options = {
                Action: action,
                Id: parseInt(params.Id),
                URL: params.Url,
                Word: Word
            }
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                let message = '添加白名单成功'
                if (resp.RetCode !== 0) {
                    message = resp.Message || resp.RetCode + "添加白名单失败"
                    notification.open({
                        message: message,
                    });
                }
                self.setState({
                    loading: false,
                    modal:false,
                    addWordModal:false
                })
               
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    renderAddWhite = (list,bodyText) => {
        list.forEach((item, index) => {
            if(bodyText.indexOf(item.SubString)>0){
                bodyText =  bodyText.replaceAll(item.SubString,"<span style= 'background-color:#f9eda6;cursor: pointer' id = wordId"+index+">"+item.SubString+"</span>")
            }
        })
        document.getElementById('bodyText').innerHTML = list.length > 0 ? bodyText:"无疑似违规内容！" ;
        list.forEach((item, index) => {
            if(document.getElementById('wordId'+index)){
                document.getElementById('wordId'+index).addEventListener('click',(e)=>{
                    this.setState({
                        Word: [e.target.innerText],
                        addWordModal:true
                    })
                })
            }    
        })
          
    }
    //上部份，获取信息
    renderAdvancedForm() {
        let { KeywordList = "", Url = "", Id} = this.state.params
        let {inputShow,newRemark,addRemark} = this.state
        return (
            <Form layout="inline" className="ant-advanced-search-form" style = {{border:"0px solid #ffffff"}}>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={1555}>
                        <FormItem label="URL">
                            <Tooltip title={Url}  >
                            <a href={Url} rel="noreferrer" target={'_blank'}>{Url.length>50?Url.substring(0,50) + '...':Url}</a>
                          </Tooltip>
                        </FormItem>
                    </Col>
                    <Col span={10} key={144444}>
                        <FormItem label="敏感词">
                        <Tooltip title={KeywordList}  >
                            <span>{KeywordList.length>50 ? KeywordList.substring(0,50) + '...' : KeywordList}</span>
                          </Tooltip>
                        </FormItem>
                    </Col>
                    <Col span={10} key={14455444}>
                        <FormItem label="备注">
                        <Tooltip title={newRemark}  >
                            <span>{newRemark.length>50?newRemark.substring(0,50) + '...':newRemark}</span>
                          </Tooltip>     
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={24} >
                        <FormItem style={{ width: '100%', marginLeft: "60px" }} label="">
                            <Button onClick={() => { this.setState({ inputShow: true }) }} htmlType="submit">新增备注</Button>
                        </FormItem>
                    </Col>
                </Row>
                {inputShow ? <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={25} >
                        <FormItem label="备注">
                            <Input placeholder="" onChange={(e) => { this.setState({ addRemark: e.target.value }) }} />
                        </FormItem>
                        <FormItem style={{ width: '100%', marginLeft: "60px" }} label="">
                            <Button style={{ marginRight: "16px" }} type = "primary" onClick={()=>this.UpdateSensitiveWordRecord(Id, addRemark)} className="primary">确定</Button>
                            <Button onClick={() => { this.setState({ inputShow: false }) }}>取消</Button>
                        </FormItem>
                    </Col>
                </Row> : null}
            </Form>
        );
    }
    render() {
        let {BatchId} = this.state.params;
        let {defaultValue} = this.state;
        console.log('defaultValue',defaultValue)
        return (
            <Card bordered={false}>
                <div>
                    <Breadcrumb style = {{marginBottom : 20}}>
                        <Breadcrumb.Item><a href="" onClick={()=>{
                            this.props.history.push("/SensitiveWords")
                        }}>首页</a></Breadcrumb.Item>
                        <Breadcrumb.Item>
                            <a href="" onClick={()=>{
                                this.props.history.push("/SensitiveWords/SensitiveBatchDetails/?Id="+BatchId);
                            }}>批次详情</a>
                        </Breadcrumb.Item>
                        <Breadcrumb.Item>
                             处理
                        </Breadcrumb.Item>
                    </Breadcrumb>
                    <Card title="疑似违规" style={{ marginBottom: 24 }} bordered={true} >
                        {this.renderAdvancedForm()}
                    </Card>
                    <Card title="敏感词" style={{ marginBottom: 24 }} bordered={true} >
                    <Form layout="inline" className="ant-advanced-search-form" style = {{border:"0px solid #ffffff"}}>
                    {this.state.list.map(item => {
                            return <Row key={JSON.stringify(item)} gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                                <Col span={10} key={1555}>
                                    <FormItem label="命中敏感词">{item.Word}</FormItem>
                                </Col>
                                <Col span={10} key={144444}>
                                    <FormItem label="推荐加白词">{item.SubString}</FormItem>
                                </Col>
                            </Row>
                        })}
                    </Form>
                        <Card title="疑似正文" style={{ marginBottom: 24 }} bordered={false} >
                            <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                                <Col span={6} key={2} >
                                    <FormItem style={{ width: '100%', marginLeft: "20px" }} label="">
                                        <Button style={{ marginRight: "16px" }} onClick={() => {
                                            this.setState({
                                                modal: true
                                            })
                                        }} type="primary">加白</Button>
                                        <Button onClick={() => {
                                            window.location.reload()
                                        }} >刷新</Button>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                                <Col span={23} key={144488884}>
                                    <FormItem label="正文内容" style = {{width:"100%",color:"blue"}}><div id = "bodyText" style={{color:"blue"}}></div></FormItem>
                                </Col>
                            </Row>
                        </Card>
                    </Card>
                </div>
                <Modal
                    title="加白表单"
                    visible={this.state.modal}
                    onCancel={() => { this.setState({ modal: false }) }}
                    footer={null}
                >
                     <Form layout="inline" className="ant-advanced-search-form" style = {{border:"0px solid #ffffff"}}>
                         <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                        <Col span={10} key={144488884} style={{width:"100%"}}>
                            <FormItem label="URL">{this.state.params.Url}</FormItem>
                        </Col>
                    </Row>
                    <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                        <Col span={10} key={144488884} style={{width:"100%",marginLeft:"40px"}}>
                           <WordEditor  defaultValue = {defaultValue}   onChange = {(value)=>{
                               console.log('value',value)
                               this.setState({
                                Word: value || []
                               })
                           }}/>
                        </Col>
                    </Row>
                    <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                        <Col span={10} key={1444888899994}>
                            <FormItem style={{ width: '100%', marginLeft: "60px" }} label="">
                                <Button style={{ marginRight: "16px" }} type = "primary" onClick={()=>this.AddWhiteWordToURL()} className="primary">确定</Button>
                                <Button onClick={() => { this.setState({ modal: false }) }}>取消</Button>
                            </FormItem>
                        </Col>
                    </Row></Form>
                </Modal>
                <Modal 
                    title="加白"
                    visible={this.state.addWordModal}
                    onCancel={() => { this.setState({ addWordModal: false }) }}
                    footer={null}>
                    是否确认加白[{this.state.Word}]?
                   <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                        <Col span={10} key={1444888899994}>
                            <FormItem style={{ width: '100%', margin: "30px 30px 30px 250px" }} label="">
                                <Button style={{ marginRight: "36px" }} type = "primary" onClick={()=>this.AddWhiteWordToURL()} className="primary">确定</Button>
                                <Button onClick={() => { this.setState({ addWordModal: false }) }}>取消</Button>
                            </FormItem>
                        </Col>
                    </Row>
                </Modal>
            </Card>
        )
    }
}

const BatchApply = Form.create()(BatchApplyPage);
export default BatchApply;
