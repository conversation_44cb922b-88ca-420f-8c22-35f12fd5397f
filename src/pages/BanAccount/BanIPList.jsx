/**
 * 封号IP
*/
import React, { Component } from "react"
import { Card, Tabs, Row, Col, Button, Input, Table, Modal, message, Tooltip } from 'antd';
import BanIPForm from './BanIPForm';
import request from '../../utils/request';
import './index.css';
import moment from 'moment'
const { Search } = Input;
const { TabPane } = Tabs;
const IPStatus = {
  1:"已释放",
  0:"未释放"
}
class BanIPList extends Component {
    constructor(props) {
        super(props)
        this.state = {
            activeKey: "1",
            visible: false,
            unSealVisible: false,
            IP: null,
            BanIP: null,
            UnSealRemark: '',
            HistoryIP: null,
            formLoading: false,
            selectedRowKeys: [], // Check here to configure the default column
            loading: false,
        }
    }
    start = () => {
           let downloadData = this.state.selectedRowKeys.map((item)=>{
               return this.props.list[item].Id+""
           })
           console.log('downloadData',downloadData)
           if(downloadData.length < 1){
               message.error('请选择导出的选项！')
               return
           }
           //发送批次请求信息
           request('GetBlockIPList', { Id: downloadData}).then(res => {
               if(res.RetCode === 0){
                let content = [];
                let data = res.DataSet
                content.push("Id,封禁IP,封禁原因,IP状态,创建人,创建时间,备注\n")
                data.forEach(item=>{
                  content.push(item.Id+','+(item.IP || '')+','+item.Reason+','+(IPStatus[item.IPStatus]||'')+','+item.Operator+','+moment(item.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss')+','+item.Remark+'\n')
                })
                this.dataToCsv(content.join(''));
               }else{
                message.error( '请求出错')
               }
           })
      };
      dataToCsv(dataList) {
        var blob = new Blob(["\ufeff", dataList], { type: "text/csv,charset=UTF-8" })
        var csvUrl = URL.createObjectURL(blob)
        // return csvUrl
        let link = document.createElement('a');
        link.download = "封禁IP导出.csv"; //文件名字
        link.href = csvUrl;
        // 触发下载
        link.click();
      }
      onSelectChange = selectedRowKeys => {
        console.log('selectedRowKeys changed: ', selectedRowKeys);
        this.setState({ selectedRowKeys });
      };
    /**
     * 切换tab
     */
    changeTab = (key) => {
        this.setState({
            activeKey: key
        })
    }
    componentDidMount() {
        this.searchList()
        this.searchHistoryList()
    }
    /**
     * 查询历史记录
     */
    searchHistoryList = (historyPage) => {
        let historyPagination = historyPage ? historyPage : this.props.historyPagination
        const { pageSize, current } = historyPagination
        const { HistoryIP } = this.state
        let params = {
          Limit: pageSize,
          Offset: (current - 1) * pageSize,
        }
        if(HistoryIP){
          params['IP'] = HistoryIP
        }
        this.props.getBlockIPLog(params)
    }
    /**
     * 查询批次记录
     */
    searchList = (page) => {
        const { IP } = this.state
        let pagination = page ? page : this.props.pagination
        const { pageSize, current } = pagination
        this.props.setLoading(true)
        let params = {
          Limit: pageSize || 10,
          Offset: (current - 1) * pageSize || 0,
        }
        if(IP){
          params['IP'] = IP
        }
        this.props.getBlockIPList(params)
        this.props.setLoading(false)
    }
    /**
     * 封禁IP
     */
    validateAccount = async (values) => {
        try {
            this.setState({ formLoading: true })
            //建议前期测试时，封禁IP写死“**************”
            let res = await request('BlockIP', {...values})
            if (res.RetCode === 0) {
                this.setState({
                    visible: false,
                    formLoading: false
                }, this.searchList)
                message.success('完成封禁！')
            }else{
                message.error('封禁失败！'+res.Message)
                this.setState({ formLoading: false })
            }
        }catch(e) {
            message.error(e.message || '封禁异常')
        }
    }
    /**
     * 切换批次列表页面
     */
    handleChange = (pagination, filters) => {
        const { current, pageSize,total } = pagination
        this.props.setPagination({
            Offset: (current - 1) * pageSize,
            Limit: pageSize,
            ...filters,
            ...total
        })
        this.searchList({ current, pageSize })
    }
    /**
     * 切换历史记录页面
     */
    handleHistoryChange = (pagination, filters) => {
        const { current, pageSize } = pagination
        this.props.setHistoryPagination({
            Offset: (current - 1) * pageSize,
            Limit: pageSize,
            ...filters,
        })
        this.props.getBlockIPLog({
          Offset: (current - 1) * pageSize,
          Limit: pageSize,
          ...filters,
        })
    }
    /**
     * 解封IP
     */
    unSeal = () => {
        const { Id, UnSealRemark } = this.state
        request('UnBlockIP', {Id: Id, Remark: UnSealRemark})
        .then(res => {
            if (res.RetCode === 0) {
                this.setState({UnSealRemark: '',unSealVisible:false})
                return message.success(`解封成功`)
            }
            message.error(`解封失败：`+ res.Message)
            this.setState({
                visible: false,
                formLoading: false
            })
        })
        .catch(e => {
            message.error(e.message || `解封失败`)
        })
    }
    render() {
        const columns = [
            {
                title: '封禁IP',
                dataIndex: 'IP'
            },
            {
                title: '封禁原因',
                dataIndex: 'Reason',
                render:val=><Tooltip title={val}>{val ? val.substring(0,20):'暂无'}</Tooltip>
            },
            {
                title: 'IP状态',
                dataIndex: 'IPStatus',
                render:val=>IPStatus[val]
            },
            {
                title: '备注',
                dataIndex: 'Remark',
                render:val=><Tooltip title={val}>{val?val.substring(0,20):'暂无'}</Tooltip>
              },
            {
                title: '创建时间',
                dataIndex: 'CreateTime',
                render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
            },
            {
                title: '创建人',
                dataIndex: 'Operator'
            },
            {
                title: '操作',
                dataIndex: 'Id',
                render: (value, row) => {
                    return <a onClick={()=>this.setState({Id:row.Id,unSealVisible:true})}>解封</a>
                }
            }
        ]
        const IPlogColumns = [{
            title: 'IP',
            dataIndex: 'IP'
        }, {
            title: '操作',
            dataIndex: 'Action'
        }, {
            title: '封禁原因',
            dataIndex: 'Reason',
            render:val=><Tooltip title={val}>{val?val.substring(0,20):'暂无'}</Tooltip>
        }, {
            title: '备注',
            dataIndex: 'Remark',
            render:val=><Tooltip title={val}>{val?val.substring(0,20):'暂无'}</Tooltip>
        }, {
            title: '操作人',
            dataIndex: 'Operator'
        }, {
            title: '操作时间',
            dataIndex: 'CreateTime',
            render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        }]
        const { activeKey, visible, IP, error, formLoading, unSealVisible,loading, selectedRowKeys } = this.state
        const { pagination, list, historyList, historyPagination} = this.props;
        const rowSelection = {
            selectedRowKeys,
            onChange: this.onSelectChange,
        };
        const hasSelected = selectedRowKeys.length > 0;
        return (
            <div>
                <Card style={{ marginTop: 20 }}>
                    <Tabs defaultActiveKey="1" onChange={this.changeTab} activeKey={activeKey}>
                        {/* tab1 账号操作 */}
                        <TabPane tab="IP操作" key="1">
                            <Row style={{ marginBottom: 60, marginTop: 20 }}>
                                <Col span={2} style={{ textAlign: 'right', lineHeight: '32px' }}>
                                    <span>IP：</span>
                                </Col>
                                <Col span={4}>
                                    <Input
                                        placeholder='请输入IP'
                                        value={IP}
                                        onChange={(e) => this.setState({IP: e.target.value})}
                                    />
                                </Col>
                                <Button style={{ marginRight: 10, marginLeft: 10 }} onClick={this.searchList}>查询</Button>
                                <Button type="danger" onClick={() => this.setState({ visible: true })}>封IP</Button>
                            </Row>
                            <Button type="primary" onClick={this.start} disabled={!hasSelected} loading={loading}>
                              导出
                             </Button>
                            <Table
                                columns={columns}
                                dataSource={list}
                                rowSelection={rowSelection}
                                pagination={{...pagination, pageSizeOptions:[10,20,50,100],showQuickJumper: true,showSizeChanger: true}}
                                onChange={this.handleChange}
                            />
                        </TabPane>
                        {/* tab2解封操作 */}
                        <TabPane tab="日志" key="2">
                            <Row style={{marginTop: 20 }}>
                              <Col span={18}></Col>
                              <Col span={4}>
                                <Search
                                  placeholder='请输入IP'
                                  onSearch={(e) => {
                                    this.setState({HistoryIP: e},this.searchHistoryList)
                                  }}
                                />
                              </Col>
                            </Row>
                            <Table
                                columns={IPlogColumns}
                                dataSource={historyList}
                                pagination={{...historyPagination, pageSizeOptions:[10,20,50,100], showQuickJumper: true, showSizeChanger: true}}
                                onChange={this.handleHistoryChange}
                            />
                        </TabPane>
                    </Tabs>
                    {/* 封号表单 */}
                    <Modal
                        visible={visible}
                        title='新增封禁IP'
                        footer={null}
                        width="600px"
                        onCancel={() => {
                          this.props.setLoading(false)
                          this.setState({ visible: false, formLoading: false })
                        }}
                    >
                        <BanIPForm
                            validateAccount={this.validateAccount}
                            cancel={() => {
                              this.props.setLoading(false)
                              this.setState({ visible: false, formLoading: false })
                            }}
                            error={error}
                            loading={formLoading}
                        />
                    </Modal>
                    {/* 解封备注 */}
                    <Modal
                        visible={unSealVisible}
                        title='你确定要解封这个IP吗？'
                        width="300px"
                        onCancel={() => this.setState({ unSealVisible: false })}
                        footer={<Button type='primary' onClick={this.unSeal}>确定</Button>}
                    >
                        <Input.TextArea row={3} placeholder='非必填，请输入解封原因' onChange={e => this.setState({UnSealRemark: e.target.value})}/>
                    </Modal>
                </Card>
            </div>
        )
    }
}

export default BanIPList
