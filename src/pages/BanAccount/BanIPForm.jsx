/*
 * @Author: li<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-04-19 15:19:40
 * @LastEditors: liyuewen <EMAIL>
 * @LastEditTime: 2023-08-03 13:49:12
 * @FilePath: /hegui-frontend/src/pages/BanAccount/BanIPForm.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useEffect } from 'react'
import { Form, Input,Radio, AutoComplete,Button, Popconfirm } from 'antd'
const { Option } = AutoComplete;
const IPDict = ['香港','上海','北京'];
const BanIPForm = (props) => { 
    const [error, setError] = useState({})
    const [loading, setLoading] = useState(false)
    //只有props中的error变化，才会触发此钩子
    useEffect(() => {
        setError(props.error)
        setLoading(props.loading)
    }, [props.error, props.loading]);

    const handleSubmit = () => {
        props.form.validateFields(async (err, values) => {
            if(err) {
                return;
            } 
            setLoading(true)
            props.validateAccount(values)
        })
    }
    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: 6 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
        },
    };
    const { getFieldDecorator } = props.form;
    const dataSource = ['未备案','内容违规','运营商下发','未实名','其他原因']
    return (
        <Form>
            <Form.Item label="IP" {...formItemLayout}>
                {getFieldDecorator('IP',  {
                    rules: [{required: true, message: '请输入想要封禁的IP'}]
                })(
                    <Input/>
                )}
                <span style={{color: 'red'}}>
                    {error ? Object.keys(error).map(key => `${key}账号状态: ${error[key]};`) : ''}
                </span>
            </Form.Item>
            <Form.Item label="IP归属地" {...formItemLayout}>
                {getFieldDecorator('Region',  {
                    initialValue: IPDict[0],
                    rules: [{required: true, message: '请选择IP归属地'}]
                })(
                    <Radio.Group>
                        {
                            IPDict.map(item => 
                                <Radio.Button key={item} value={item}>{item==='北京' ? '北京（含内蒙）':item}</Radio.Button>
                            )
                        }
                    </Radio.Group>
                )}
            </Form.Item>
            <Form.Item label="封禁原因" {...formItemLayout}>
                {getFieldDecorator('Reason', {
                    rules: [{required: true, message: '请选择封禁原因'}]
                })(
                    <AutoComplete
                        style={{ width: "100%" }}
                        dropdownMatchSelectWidth={false}
                        dataSource={dataSource.map(item=><Option key={item} value={item} title={item}>{item}</Option>)}
                        placeholder="请填写理由"
                        filterOption={(inputValue, option) =>
                        option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                        }
                    />
                )}
            </Form.Item>
            <Form.Item label="备注" {...formItemLayout}>
                    {getFieldDecorator('Remark',  {
                        rules: [{required: true, message: '请输入备注'}]
                    })(
                        <Input.TextArea/>
                    )}
            </Form.Item>
            <Form.Item style={{ textAlign: 'center' }}>
                <Popconfirm placement="top" title={'确定封禁IP吗？'} onConfirm={handleSubmit} okText="Yes" cancelText="No">
                    <Button type="primary" loading={loading}>
                        确定
                    </Button>
                </Popconfirm>
                <Button style={{ marginLeft: 10 }} onClick={props.cancel}>
                    取消
                </Button>
            </Form.Item>
        </Form>
    )
}

const BanIPFormWrapper = Form.create()(BanIPForm)
export default BanIPFormWrapper