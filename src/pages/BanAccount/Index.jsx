import { connect } from "react-redux"
import List from "./List"
import { setPagination, getList, setLoading, getHistoryList, setHistoryPagination } from "../../actionCreator/sealBatch"

const mapDispatchToProps = (dispatch) => {
    return {
        setPagination: (data) =>  dispatch(setPagination("SET_PAGINATION",data)),
        getList: (data) => dispatch(getList(data)),
        setLoading: (data) => dispatch(setLoading("SET_LOADING",data)),
        getHistoryList: (data) => dispatch(getHistoryList(data)),
        setHistoryPagination: (data) => dispatch(setHistoryPagination("SET_HISTORY_PAGINATION"), data)
    }
  }
const mapStateToProps = ({sealBatchReducer}) => {
    return {
        ...sealBatchReducer
    }
  }
  const SealBatchContiner = connect(mapStateToProps, mapDispatchToProps)(List)
  
  export default SealBatchContiner
  