/**
 * 封号记录列表页
 * <AUTHOR>
 */
import React, { Component } from "react"
import { Card, Breadcrumb, Row, Button, Input, Table, notification } from 'antd'
import { getDetailList, setDetailLoading, setDetailPagination, setDetailList } from "../../actionCreator/sealBatch"
import { connect } from "react-redux"

import { Link } from 'react-router-dom';
import { sealTypeDict } from '../../utils/config';
import checkNoticeStatus from '../../utils/checkNoticeStatus';
import request from '../../utils/request'
import cloneDeep from 'lodash/cloneDeep'
import './index.css'
import _ from 'lodash'

class BanAccountDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
            CompanyId: null
        }
    }

    componentDidMount() {
        const { params } = this.props.match
        this.setState({
            BatchId: parseInt(params.BatchId, 10) || null
        }, () => this.searchList())
    }
    /**
     * 查询列表
     */
    searchList = () => {
        const { CompanyId, BatchId } = this.state
        const { pageSize, current } = this.props.pagination

        this.props.setLoading(true)
        this.props.getDetailList({
            Limit: pageSize,
            Offset: (current - 1) * pageSize,
            BatchId: BatchId,
            CompanyId: CompanyId ? parseInt(CompanyId, 10) : undefined
        })

        this.props.setLoading(false)
    }
    /**
     * 批量发送通知
     */
    sendNotice = () => {
        const { BatchId } = this.state
        const { list } = this.props
        request("NotifySeal", { BatchId, Type: list && list.length > 0 ? list[0].Type : null })
		.then(() => {
            this.searchList()
			notification.success({
				message: "发送封号通知",
				description: "通知成功"
			})
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "发送封号失败",
				description: err.message || "内部错误"
			})
		})
    }
    retrySeal = () => {
        const { BatchId } = this.state
        const { list } = this.props
        this.props.setLoading(true)
        request("RetrySealAccount", { BatchId, Type: list && list.length > 0 ? list[0].Type : null })
		.then(() => {
            this.searchList()
			notification.success({
				message: "失败重封完成",
				description: "失败重封完成"
			})
            this.props.setLoading(false)
		})
		.catch(err => {
			// 报错
			notification.error({
				message: "啊哦！重封失败",
				description: err.message || "内部错误"
			})
            this.props.setLoading(false)
		})
    }
    handleChange = (pagination, filters) => {
        const { originList } = this.props
        const { current, pageSize } = pagination
        if(_.isEmpty(filters)){
            const { CompanyId, BatchId } = this.state
            this.props.getDetailList({
                Limit: pageSize,
                Offset: (current - 1) * pageSize,
                BatchId: BatchId,
                CompanyId: CompanyId ? parseInt(CompanyId, 10) : undefined
            })
            return
        }
        //过滤
        let newList = cloneDeep(originList)
        if (filters.RecycleStatus && filters.RecycleStatus.length > 0) {
            newList = newList.filter(item => filters.RecycleStatus.includes(item.RecycleStatus))
        }
        if (filters.SealStatus && filters.SealStatus.length > 0) {
            newList = newList.filter(item => filters.SealStatus.includes(item.SealStatus))
        }
        if (filters.HasNotifyInfo && filters.HasNotifyInfo.length > 0) {
            newList = newList.filter(item => filters.HasNotifyInfo.includes(checkNoticeStatus(item.NotifyInfo)))
        }
        this.props.setDetailList(newList)
    }
    searchValue = (value) => {
        const { originList } = this.props
        if(value !== null && value.trim() !== '') {
            let newList = originList.filter(item => item.CompanyId === parseInt(value, 10))
            this.props.setDetailList(newList)
            return;
        }
        this.props.setDetailList(cloneDeep(originList))
    }
    render() {
        const columns = [
            {
                title: '公司ID',
                dataIndex: 'CompanyId'
            },
            {
                title: '封号原因',
                dataIndex: 'Type',
                render: (value) => sealTypeDict[value] || '未知'
            },
            {
                title: '是否下发',
                dataIndex: 'Issue'
            },
            {
                title: '账号状态',
                dataIndex: 'SealStatus',
                filters: [
                    { text: '正常', value: '正常' },
                    { text: '已拒绝', value: '已拒绝' },
                    { text: '操作失败', value: '操作失败' }
                ],
            },
            {
                title: '回收策略',
                dataIndex: 'RecycleStatus',
                filters: [
                    { text: '设置不回收成功', value: '设置不回收成功' },
                    { text: '设置不回收失败', value: '设置不回收失败' },
                    { text: '未调整', value: '未调整' }
                ],
            },
            {
                title: '封号时间',
                dataIndex: 'CreateTime'
            },
            {
                title: '通知状态',
                dataIndex: 'HasNotifyInfo',
                filters: [{
                    text: '发送中', value: '发送中'
                }, {
                    text: '发送失败', value: '发送失败'
                }, {
                    text: '发送成功', value: '发送成功'
                }, {
                    text: '待发送', value: '待发送'
                }, {
                    text: '不通知', value: '不通知'
                },{
                    text: '超时未响应', value: '超时未响应'
                }],
                render: (value, row) => {
                    return row.Notice === '通知' ?
                    (row.HasNotifyInfo === '发送中' ? checkNoticeStatus(row.NotifyInfo) : row.HasNotifyInfo)
                    : '不通知'
                }
            },
            {
                title: '通知详情',
                dataIndex: 'Operate',
                render: (value, row) => {
                    const { BatchId } = this.state
                    return row.Notice === '通知' && row.HasNotifyInfo === '发送中' ?
                        <Link to={`/banAccount/message/${BatchId}/${row.CompanyId}`}>查看</Link>
                        : '无'
                }
            }
        ]

        const { pagination, list = [], canRetrySeal, canRetrySendNotice } = this.props
        console.log("TCL: BanAccountDetail -> render -> pagination", pagination)
        return (
            <div>
                <Card bordered={false}>
                    <Breadcrumb>
                        <Breadcrumb.Item><Link to="/banAccount">自动封号</Link></Breadcrumb.Item>
                        <Breadcrumb.Item>批次详情</Breadcrumb.Item>
                    </Breadcrumb>
                </Card>
                <Card style={{ marginTop: 20 }}>
                    <Row style={{marginBottom: 30}}>
                        <Input.Search
                            placeholder="公司ID"
                            enterButton
                            onSearch={this.searchValue}
                            style={{float: 'right', width: '30%'}}
                        />
                        <Button type="primary" onClick={this.retrySeal} disabled={!canRetrySeal}>失败重封</Button>
                        {
                            //如果该批次支持发送通知，则展示批量通知按钮
                            list.length > 0 && list[0].Notice === '通知' ?
                            <Button type="primary" style={{marginLeft: 20}} disabled={!canRetrySendNotice}
                              onClick={this.sendNotice}
                            >批量通知</Button>
                            : null
                        }
                    </Row>
                    <Table
                        columns={columns}
                        dataSource={list}
                        rowKey={record => record.Id}
                        pagination={{...pagination, showQuickJumper: true}}
                        onChange={this.handleChange}
                    />
                </Card>
            </div>
        )
    }
}

const mapDispatchToProps = (dispatch) => {
    return {
        getDetailList: (data) => dispatch(getDetailList(data)),
        setPagination: (data) => dispatch(setDetailPagination("SET_DETAIL_PANIGATION", data)),
        setLoading: (data) => dispatch(setDetailLoading("SET_DETAIL_LOADING", data)),
        setDetailList: (data) => dispatch(setDetailList("SET_DETAIL_LIST",data))
    }
}
const mapStateToProps = ({ sealBatchDetailReducer }) => {
    return {
        ...sealBatchDetailReducer
    }
}
const BanAccountDetailContiner = connect(mapStateToProps, mapDispatchToProps)(BanAccountDetail)

export default BanAccountDetailContiner
