import React, { useState, useEffect } from 'react'
import { Form, Input, Radio, Select, Button, Popconfirm } from 'antd'
import { sealTypeDict } from '../../utils/config'

const SealBatchForm = (props) => {
    
    const [showRemark, setShowRemark] = useState(false)
    const [error, setError] = useState({})
    const [loading, setLoading] = useState(false)
    const [callBack,setCallBack] = useState(false)

    //只有props中的error变化，才会触发此钩子
    useEffect(() => {
        setError(props.error)
        setLoading(props.loading)
    }, [props.error, props.loading]);

    const handleSubmit = () => {
        props.form.validateFields(async (err, values) => {
            if(err) {
                return;
            } 
            setLoading(true)
            props.validateAccount(values)
        })
    }
    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: 6 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
        },
    };
    const { getFieldDecorator } = props.form;
    console.log(showRemark)
    return (
        <Form>
            <Form.Item label="封号原因" {...formItemLayout}>
                {getFieldDecorator('Type', {
                    rules: [{required: true, message: '请选择封号原因'}]
                })(
                    <Select onChange={(e) => { setCallBack(e === '2')}}>
                        {
                            Object.keys(sealTypeDict).map(item => 
                                <Select.Option key={item}>{sealTypeDict[item]}</Select.Option>
                            )
                        }
                    </Select>
                )}
            </Form.Item>
            <Form.Item label="公司ID" {...formItemLayout}>
                {getFieldDecorator('CompanyIds',  {
                    rules: [{required: true, message: '请录入封号的账号，上限50个'}]
                })(
                    <Select placeholder='请输入' mode="tags" tokenSeparators={[',', ' ']} maxTagCount={50} />
                )}
                <span style={{color: 'red'}}>
                    {error ? Object.keys(error).map(key => `${key}账号状态: ${error[key]};`) : ''}
                </span>
            </Form.Item>
            {callBack ?
                <Form.Item label="资源是否设置回收" {...formItemLayout}>
                {getFieldDecorator('SetNoRecycle',  {
                    initialValue:false,
                    rules: [{required: true, message: '请选择资源是否设置回收'}]
                })(
                    <Radio.Group name="radiogroup">
                        <Radio value={true}>设置为不回收</Radio>
                        <Radio value={false}>不设置(不变动账号当前回收策略)</Radio>
                    </Radio.Group>
                )}
               </Form.Item>:''
            }
            <Form.Item label="是否管局下发" {...formItemLayout}>
                {getFieldDecorator('Issue',  {
                    initialValue: 0,
                    rules: [{required: true, message: '请选择是否管局下发'}]
                })(
                    <Radio.Group name="radiogroup" onChange={(e) => setShowRemark(e.target.value)}>
                        <Radio value={1}>是</Radio>
                        <Radio value={0}>否</Radio>
                    </Radio.Group>
                )}
            </Form.Item>
            {/* {
                showRemark ? */}
                <Form.Item label="备注" {...formItemLayout}>
                    {getFieldDecorator('Remark',  {
                        rules: [{required: true, message: '请输入备注'}]
                    })(
                        <Input.TextArea/>
                    )}
                </Form.Item>
                {/* : ''
            } */}
            <Form.Item label="是否通知" {...formItemLayout}>
                {getFieldDecorator('Notice', {
                    initialValue: 1,
                    rules: [{required: true, message: '请选择是否通知客户'}]
                })(
                    <Radio.Group name="radiogroup">
                        <Radio value={1}>通知</Radio>
                        <Radio value={0}>不通知</Radio>
                    </Radio.Group>
                )}
            </Form.Item>
            <Form.Item style={{ textAlign: 'center' }}>
                <Popconfirm placement="top" title={'确定封号吗？'} onConfirm={handleSubmit} okText="Yes" cancelText="No">
                    <Button type="primary" loading={loading}>
                        确定封号
                    </Button>
                </Popconfirm>
                <Button style={{ marginLeft: 10 }} onClick={props.cancel}>
                    取消
                </Button>
            </Form.Item>
        </Form>
    )
}

const SealBatchFormWrapper = Form.create()(SealBatchForm)
export default SealBatchFormWrapper