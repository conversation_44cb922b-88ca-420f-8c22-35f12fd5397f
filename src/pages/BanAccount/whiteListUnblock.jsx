import React, { useState, useEffect } from "react";
import moment from "moment";
import "./whiteList.css";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Popconfirm,
  Icon,
  Modal,
  Form,
  Select,
  notification,
  message,
} from "antd";
import request from "../../utils/request";
const { Option } = Select;
const { Search } = Input;
const { TextArea } = Input;

const WhiteListUnblock = (props) => {
  const { getFieldDecorator, getFieldValue, resetFields } = props.form;
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editingPort, setEditingPort] = useState(false);
  const [editingProtocol, setEditingProtocol] = useState(false);
  const [editingDirection, setEditingDirection] = useState(false);
  const [oldDatasource,setOldDataSource] = useState([]);
  const [dataLoading, setDataLoading] = useState(false);
  useEffect(() => {
    GetWhitelistOrganizations();
  }, [loading]);

  const openNotification = (rejectId,isOtherSuccess) => {
    const args = {
      message: `${rejectId.join(",")}已存在白名单中！`,
      duration: 0,
      style: {
        width: 400,
        marginLeft: -20
      }
    };
    if(isOtherSuccess){
      args.message = args.message + "其他账户添加成功。"
      args.style.width = 415
    }
    notification.warning(args);
  };
  const GetWhitelistOrganizations = async (CompanyId) => {
    setDataLoading(true)
    let res = await request(
      "GetCompanyWhiteList",
      CompanyId ? { CompanyId } : null
    );
    setDataLoading(false)
    if (res.RetCode === 0) {
      let dataSource = res.Data.map((data) => {
        data.Protocol = data.Udp ? "Udp" : "Tcp";
        data.port = data[data.Protocol];
        return data;
      });
      setDataSource(dataSource);
      setOldDataSource(dataSource)
    }
  };
  const onSearch = (value) => {
    if(value===''){
      setDataSource(oldDatasource)
      return
    }
    let CompanyId = parseInt(value);
    if (isNaN(CompanyId)) {
      message.error("请输入正确的公司ID");
      return;
    }
    let findObj = dataSource.filter(item=>item.CompanyId===CompanyId)
    setDataSource(findObj)
  };
  const handleSearchChange = (e)=>{
    if(!e.target.value){
      setDataSource(oldDatasource)
    }
  }
  const columns = [
    {
      title: "公司ID",
      dataIndex: "CompanyId",
      key: "CompanyId",
    },
    {
      title: "公司名称",
      dataIndex: "CompanyName",
      key: "CompanyName",
    },
    {
      title: "协议端口",
      dataIndex: "port",
      key: "port",
    },
    {
      title: "协议类型",
      dataIndex: "Protocol",
      key: "Protocol",
    },
    {
      title: "出入向",
      dataIndex: "Direction",
      key: "Direction",
      render: val=>{
        return val===0?'出向':'入向'
      }
    },
    {
      title: "备注",
      dataIndex: "Description",
      key: "Description",
    },
    {
      title: "创建时间",
      dataIndex: "CreateTime",
      key: "CreateTime",
      defaultSortOrder: 'descend',
      sorter: (a, b) => a.CreateTime - b.CreateTime,
      render: (val) => (
        <span>
          {val ? moment(val * 1000).format("YYYY-MM-DD HH:mm:ss") : ""}
        </span>
      ),
    },
    {
      title: "创建人",
      dataIndex: "Operator",
      key: "Operator",
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <Popconfirm
            title="确定从白名单移除吗?"
            onConfirm={() => {
              deleteRow(row);
            }}
            okText="确定"
            cancelText="取消"
          >
            <Button type="danger" ghost>
              删除
            </Button>
          </Popconfirm>
        );
      },
    },
  ];
  const deleteRow = async (row) => {
    setLoading(true);
    let res = await request("DelCompanyWhiteList", {
      Id: row.Id,
    });
    if (res.RetCode === 0) {
      message.success("删除成功");
    }
    setLoading(false);
  };

  const handleOk = () => {
    handleSubmit();
  };

  const handleSubmit = () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        let promises = [],
          repeatedID = [],
          success = [],
          rejectId = [];
        //批量添加白名单
        promises = values.CompanyId.map(async (CompanyId) => {
          let options
          if(values.port==='53'){
            options = {
              CompanyId: parseInt(CompanyId),
              Description: values.Description,
              Direction: values.Direction,
              'UDP': values.port,
              'TCP': ''
            };
          }else{
            options = {
              CompanyId: parseInt(CompanyId),
              Description: values.Description,
              'TCP': '',
              'UDP': '',
              [values.Protocol]: values.port,
            };
          }

          return request("AddCompanyWhiteList", options);
        });
        Promise.allSettled(promises).then((results) => {
          results.forEach((result, i) => {
            if (result.status === "rejected") {
              if(result.reason.message.indexOf('35931') !== -1){
                repeatedID.push(values.CompanyId[i])
              }
              rejectId.push(values.CompanyId[i]);
            }else{
              success.push(values.CompanyId[i])
            }
          });
          success.length && message.success("添加成功");
          repeatedID.length && openNotification(repeatedID, success.length > 0);
          setVisible(false);
          setLoading(false);
          resetFields();
        });
      }
    });
  };

  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 12 }}
        onSubmit={handleSubmit}
      >
        <Form.Item label="公司ID">
          {getFieldDecorator("CompanyId", {
            rules: [
              {
                required: true,
                message: "请输入公司ID",
              },
            ],
          })(<Select mode="tags" tokenSeparators={[","]} />)}
        </Form.Item>
        <Form.Item label="协议端口">
          {getFieldDecorator("port", {
            rules: [
              {
                required: true,
                message: "请选择协议端口",
              },
            ],
            initialValue: "25",
          })(
            editingPort ? (
              <Select  style={{ width: 120 }}>
                <Option value="25">25</Option>
                <Option value="26">26</Option>
                <Option value="53">53</Option>
              </Select>
            ) : (
              <>
                <Icon
                  type="edit"
                  theme="twoTone"
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    setEditingPort(true);
                  }}
                />
                {getFieldValue("port")}
              </>
            )
          )}
        </Form.Item>
        {
          getFieldValue("port")==='53'? <Form.Item label="出入向">
          {getFieldDecorator("Direction", {
            rules: [
              {
                required: true,
                message: "请选择出入向",
              },
            ],
            initialValue: 1,
          })(
            editingDirection ? (
              <Select  style={{ width: 120 }}>
                <Option value={0}>出向</Option>
                <Option value={1}>入向</Option>
              </Select>
            ) : (
              <>
                <Icon
                  type="edit"
                  theme="twoTone"
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    setEditingDirection(true);
                  }}
                />
                {['出向','入向'][getFieldValue("Direction")]}
              </>
            )
          )}
        </Form.Item>:<Form.Item label="协议类型">
          {getFieldDecorator("Protocol", {
            rules: [
              {
                required: true,
                message: "请选择协议类型",
              },
            ],
            initialValue: "TCP",
          })(
            editingProtocol ? (
              <Select  style={{ width: 120 }}>
                <Option value="TCP">TCP</Option>
                <Option value="UDP">UDP</Option>
              </Select>
            ) : (
              <>
                <Icon
                  type="edit"
                  theme="twoTone"
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    setEditingProtocol(true);
                  }}
                />
                {getFieldValue("Protocol")}
              </>
            )
          )}
        </Form.Item>
        }

        <Form.Item label="备注">
          {getFieldDecorator("Description", {
            rules: [
              {
                required: true,
                message: "请填写备注",
              },
            ],
          })(<TextArea rows={4} />)}
        </Form.Item>
      </Form>
    );
  };
  return (
    <Card style={{ minHeight: "80vh" }}>
      <Row type="flex" justify="space-between" style={{ marginBottom: "30px" }}>
        <Col span={2}>
          <Button
            type="primary"
            onClick={() => {
              setVisible(true);
            }}
          >
            添加白名单
          </Button>
        </Col>
        <Col span={4}>
          <Search
            className="searchComp"
            placeholder="请输入公司ID"
            allowClear
            onSearch={onSearch}
            onChange={handleSearchChange}
            enterButton={<Icon type="search" />}
          />
        </Col>
      </Row>
      <Row>
        <Table dataSource={dataSource} columns={columns} rowKey='Id' loading={dataLoading}/>
      </Row>
      <Modal
        title="添加白名单"
        visible={visible}
        onOk={handleOk}
        onCancel={() => {
          setVisible(false);
          resetFields();
        }}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleOk}
          >
            提交
          </Button>,
          <Button
            key="back"
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </Button>,
        ]}
        // width={500}
      >
        {addForm()}
      </Modal>
    </Card>
  );
};

export default Form.create()(WhiteListUnblock);
