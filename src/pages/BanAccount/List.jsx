/**
 * 封号批次列表页
 * <AUTHOR>
 */
import React, { Component,Fragment } from "react"
import { Card, Tabs, Row, Col, Button, Input, Table, Modal, message, Tooltip } from 'antd'
import SealBatchForm from './SealBatchForm'

import { Link } from 'react-router-dom';
import request from '../../utils/request'
import './index.css'
import isEmpty from 'lodash/isEmpty'
const { TabPane } = Tabs;

class BanAccountList extends Component {
    constructor(props) {
        super(props)
        this.state = {
            activeKey: "1",
            visible: false,
            unSealVisible: false,
            CompanyId: null,
            UnSealCompanyId: null,
            UnSealRemark: '',
            UnSealList: [],
            HistoryCompanyId: null,
            formLoading: false,
            selectedRowKeys: [], // Check here to configure the default column
            loading: false,
            pagination:{
              current: 1,
              pageSize: 10,
            }

        }
    }
    start = () => {
          this.setState({ loading: true });
          this.setState({
            loading: false,
          },()=>{
           let downloadData = this.state.selectedRowKeys.map((item)=>{
               return this.props.list[item].Id+""
           })
           console.log('downloadData',downloadData)
           if(downloadData.length<1){
               return
           }
           //发送批次请求信息
           request('GetSealRecordDownload', { BatchId: downloadData}).then(res => {
               if(res.RetCode === 0){
                let content = [];
                let data = res.RecordList
                content.push("批次Id,公司ID,封号原因,账号状态,是否下发,创建时间,备注\n")
                data.map(item=>{
                  content.push(item.Id+','+(item.CompanyId || '')+','+item.Type+','+(item.SealStatus||'')+','+item.Issue+','+item.CreateTime+','+item.Remark+'\n')
                })
                this.dataToCsv(content)
               }else{
                console.log("res")
                message.error( '请求出错')
               }
           })
          });
      };
      dataToCsv(dataList) {
        var blob = new Blob(dataList, { type: "text/csv,charset=UTF-8" })
        var csvUrl = URL.createObjectURL(blob)
        // return csvUrl
        let link = document.createElement('a');
        link.download = "批量下载.csv"; //文件名字
        link.href = csvUrl;
        // 触发下载
        link.click();
    }
      onSelectChange = selectedRowKeys => {
        console.log('selectedRowKeys changed: ', selectedRowKeys);
        this.setState({ selectedRowKeys });
      };
    /**
     * 切换tab
     */
    changeTab = (key) => {
        this.setState({
            activeKey: key
        })
    }
    componentDidMount() {
        this.searchList()
    }
    /**
     * 查询历史记录
     */
    searchHistoryList = (historyPagination) => {
        historyPagination = historyPagination ? historyPagination : this.props.historyPagination
        const { pageSize, current } = historyPagination
        const { HistoryCompanyId } = this.state
        this.props.getHistoryList({
            Limit: pageSize,
            Offset: (current - 1) * pageSize,
            CompanyId: HistoryCompanyId ? parseInt(HistoryCompanyId, 10) : undefined
        })
    }
    /**
     * 查询批次记录
     */
    searchList = (pagination) => {
        const { CompanyId } = this.state
        pagination = pagination ? pagination : this.props.pagination
        const { pageSize, current } = pagination
        this.props.setLoading(true)
        this.props.getList({
            Limit: pageSize,
            Offset: (current - 1) * pageSize,
            CompanyId: CompanyId ? parseInt(CompanyId, 10) : undefined
        })
        this.props.setLoading(false)
    }
    /**
     * 根据账号查询可解封的记录
     */
    searchUnSealList = () => {
        const { UnSealCompanyId } = this.state
        if (UnSealCompanyId === '') {
            message.error('请先输入查询的公司账号')
        }
        request('GetUnSealAccount', {CompanyId: parseInt(UnSealCompanyId, 10)})
        .then(res => {
            if (res.RetCode === 0) {
                this.setState({
                    UnSealList: res.Result ? [res.Result] : []
                })
            }
        })
    }
    /**
     * 创建封号，并校验账号状态
     */
    validateAccount = async (values) => {
        try {
            let abnormalData = await request('CheckAccountStatus', {CompanyIds: values.CompanyIds})
            if (!isEmpty(abnormalData.Result)) {
                this.setState({
                    error: abnormalData.Result,
                    formLoading: false
                })
                return;
            }
            this.setState({ formLoading: true })
            let res = await request('CreateSealBatch', {...values, Type: parseInt(values.Type, 10)})
            if (res.RetCode === 0) {
                this.setState({
                    visible: false,
                    formLoading: false
                }, () => this.searchList())
                message.success('完成封号！')
            }
        }catch(e) {
            message.error(e.message || '封号异常')
        }
    }
    /**
     * 切换批次列表页面
     */
    handleChange = (pagination, filters) => {
        console.log("pagination, filters",pagination, filters)
        const { current, pageSize,total } = pagination
        this.props.setPagination({
            Offset: (current - 1) * pageSize,
            Limit: pageSize,
            ...filters,
            ...total
        })
        this.searchList({ current, pageSize })
    }
    /**
     * 切换历史记录页面
     */
    handleHistoryChange = (pagination, filters) => {
        const { current, pageSize } = pagination
        this.props.setHistoryPagination({
            Offset: (current - 1) * pageSize,
            Limit: pageSize,
            ...filters,
        })
        this.searchHistoryList(pagination)
    }
    /**
     * 解封账号
     */
    unSeal = () => {

        const { UnSealCompanyId, UnSealRemark } = this.state
        request('UnSealAccount', {CompanyId: parseInt(UnSealCompanyId, 10), Remark: UnSealRemark, Audited: 1})
        .then(res => {
            if (res.RetCode === 0 && !res.Message) {
                this.setState({UnSealList: [], unSealVisible: false, UnSealRemark: ''})
                return message.success(`${UnSealCompanyId} 解封成功`)
            }
            return message.error(res.Message)
        })
        .catch(e => {
            return message.error(e.message || `${UnSealCompanyId} 解封失败`)
        })
    }
    render() {
        const columns = [
            {
                title: '批次ID',
                dataIndex: 'Id'
            },
            {
                title: '封号原因',
                dataIndex: 'Type'
            },
            {
                title: '是否下发',
                dataIndex: 'Issue',
            render: (value, row) =>
                value === '是' ? <Tooltip placement="top" title={row.Remark}>{value}</Tooltip> : value
            },
            {
                title: '是否通知',
                dataIndex: 'Notice'
            },
            {
                title: '通知状态',
                dataIndex: 'Status'
            },
            {
                title: '创建时间',
                dataIndex: 'CreateTime'
            },
            {
                title: '操作人',
                dataIndex: 'Operator'
            },
            {
                title: '备注',
                dataIndex: 'Remark',
                render: (value) => {
                    if (value && value.length > 16) {
                      return (
                        <Fragment>
                          <Tooltip title={value} >
                            <span>{value.substring(0,16) + '..'}</span>
                          </Tooltip>
                        </Fragment>
                      )
                    } else {
                      return (
                        <Fragment>
                          <span>{value || ''}</span>
                        </Fragment>
                      )
                    }
                  }
            },
            {
                title: '批次详情',
                dataIndex: 'Operate',
                render: (value, row) => {
                    return <Link to={'/banAccount/detail/'+row.Id} target="_blank">查看</Link>
                }
            }
        ]
        const historyColumns = [{
            title: '公司ID',
            dataIndex: 'CompanyId'
        }, {
            title: '操作场景',
            dataIndex: 'Type',
            render: (value, record) => record.Remark ?
                    <Tooltip placement="top" title={record.Remark}><div>{value}</div></Tooltip>
                    : <span>{value}</span>
        }, {
            title: '是否下发',
            dataIndex: 'Issue'
        }, {
            title: '账号状态',
            dataIndex: 'SealStatus'
        }, {
            title: '回收策略',
            dataIndex: 'RecycleStatus'
        }, {
            title: '操作人',
            dataIndex: 'Operator'
        },{
            title: '备注',
            dataIndex: 'Remark',
            render: (value) => {
                if (value && value.length > 16) {
                  return (
                    <Fragment>
                      <Tooltip title={value} >
                        <span>{value.substring(0,16) + '..'}</span>
                      </Tooltip>
                    </Fragment>
                  )
                } else {
                  return (
                    <Fragment>
                      <span>{value || ''}</span>
                    </Fragment>
                  )
                }
              }
        },{
            title: '操作时间',
            dataIndex: 'CreateTime'
        }]

        const unSealColumns = [{
            title: '公司ID',
            dataIndex: 'CompanyId'
        }, {
            title: '操作场景',
            dataIndex: 'Type'
        }, {
            title: '是否下发',
            dataIndex: 'Issue'
        }, {
            title: '账号状态',
            dataIndex: 'SealStatus'
        }, {
            title: '回收策略',
            dataIndex: 'RecycleStatus'
        }, {
            title: '操作人',
            dataIndex: 'Operator'
        }, {
            title: '操作时间',
            dataIndex: 'CreateTime'
        },{
            title: '备注',
            dataIndex: 'Remark',
            render: (value) => {
                if (value && value.length > 16) {
                  return (
                    <Fragment>
                      <Tooltip title={value} >
                        <span>{value.substring(0,16) + '..'}</span>
                      </Tooltip>
                    </Fragment>
                  )
                } else {
                  return (
                    <Fragment>
                      <span>{value || ''}</span>
                    </Fragment>
                  )
                }
              }
        },{
            title: '操作',
            dataIndex: 'Operate',
            render: (value, record) => {
                return record.AccountStatus !== '正常' ?
                        <Button type='primary' onClick={() => this.setState({unSealVisible: true})}>解封</Button>
                        : '无'
            }
        }]
        const { activeKey, visible, CompanyId, HistoryCompanyId, error, formLoading, UnSealCompanyId, UnSealList, unSealVisible,loading, selectedRowKeys } = this.state

        const { pagination, list, historyList, historyPagination } = this.props;
        const rowSelection = {
            selectedRowKeys,
            onChange: this.onSelectChange,
        };
        const hasSelected = selectedRowKeys.length > 0;
        return (
            <div>
                <Card style={{ marginTop: 20 }}>
                    <Tabs defaultActiveKey="1" onChange={this.changeTab} activeKey={activeKey}>

                        {/* tab1 账号操作 */}
                        <TabPane tab="账号操作" key="1">
                            <Row style={{ marginBottom: 35, marginTop: 20 }}>
                                <Col span={2} style={{ textAlign: 'right', lineHeight: '32px' }}>
                                    <span>公司ID：</span>
                                </Col>
                                <Col span={4}>
                                    <Input
                                        placeholder='请输入'
                                        value={CompanyId}
                                        onChange={(e) => this.setState({CompanyId: e.target.value})}
                                    />
                                </Col>
                                <Button style={{ marginRight: 10, marginLeft: 10 }} onClick={() => this.searchList()}>查询</Button>
                                <Button type="danger" onClick={() => this.setState({ visible: true })}>封号</Button>
                            </Row>
                            <div className="title-wrap">
                              <h3>封号批次列表</h3>
                              <Button type="primary" onClick={this.start} disabled={!hasSelected} loading={loading} className="downLoadBtn">
                                批量下载
                              </Button>
                            </div>

                            <Table
                                columns={columns}
                                dataSource={list}
                                rowSelection={rowSelection}
                                pagination={{...pagination, pageSizeOptions:[10,20,50,100],showQuickJumper: true,showSizeChanger: true}}
                                onChange={this.handleChange}
                            />
                        </TabPane>

                        {/* tab2解封操作 */}
                        <TabPane tab="解封" key="2">
                            <Row style={{ marginBottom: 60, marginTop: 20 }}>
                                <Col span={2} style={{ textAlign: 'right', lineHeight: '32px' }}>
                                    <span>公司ID：</span>
                                </Col>
                                <Col span={4}>
                                    <Input
                                        placeholder='请输入'
                                        value={UnSealCompanyId}
                                        onChange={(e) => this.setState({UnSealCompanyId: e.target.value})}
                                    />
                                </Col>
                                <Button style={{ marginRight: 10, marginLeft: 10 }} onClick={this.searchUnSealList}>查询</Button>
                                <Button type="danger" style={{ marginRight: 10, marginLeft: 10 }} onClick={() => this.setState({unSealVisible: true})}>老账号解封</Button>
                            </Row>
                            <Table
                                columns={unSealColumns}
                                dataSource={UnSealList}
                                // pagination={null}
                                // onChange={this.handleHistoryChange}
                            />
                        </TabPane>
                        {/* tab3历史记录 */}
                        <TabPane tab="历史记录" key="3">
                            <Row style={{ marginBottom: 60, marginTop: 20 }}>
                                <Col span={2} style={{ textAlign: 'right', lineHeight: '32px' }}>
                                    <span>公司ID：</span>
                                </Col>
                                <Col span={4}>
                                    <Input
                                        placeholder='请输入'
                                        value={HistoryCompanyId}
                                        onChange={(e) => this.setState({HistoryCompanyId: e.target.value})}
                                    />
                                </Col>
                                <Button style={{ marginRight: 10, marginLeft: 10 }} onClick={() => this.searchHistoryList()}>查询</Button>
                            </Row>
                            <Table
                                columns={historyColumns}
                                dataSource={historyList}
                                pagination={{...historyPagination, showQuickJumper: true}}
                                onChange={this.handleHistoryChange}
                            />
                        </TabPane>
                    </Tabs>

                    {/* 封号表单 */}
                    <Modal
                        visible={visible}
                        title='封号表单'
                        footer={null}
                        width="600px"
                        onCancel={() => this.setState({ visible: false })}
                    >
                        <SealBatchForm
                            validateAccount={this.validateAccount}
                            cancel={() => this.setState({ visible: false })}
                            error={error}
                            loading={formLoading}
                        />
                    </Modal>
                    {/* 解封备注 */}
                    <Modal
                        visible={unSealVisible}
                        title='你确定要解封这个账号吗？'
                        width="300px"
                        onCancel={() => this.setState({ unSealVisible: false })}
                        footer={<Button type='primary' onClick={this.unSeal}>确定</Button>}
                    >
                        <Input.TextArea row={3} placeholder='非必填，请输入解封原因' onChange={e => this.setState({UnSealRemark: e.target.value})}/>
                    </Modal>
                </Card>
            </div>
        )
    }
}

export default BanAccountList
