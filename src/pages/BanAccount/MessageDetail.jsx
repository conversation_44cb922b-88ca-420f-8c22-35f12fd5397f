/**
 * 封号通知详情页
 * <AUTHOR>
 */
import React, { Component } from "react"
import { Card, Breadcrumb, Table, Row, Col, Button, Modal } from 'antd'
import { Link } from 'react-router-dom';
import request from '../../utils/request'
import { statusList } from '../../utils/config'

class MessageDetail extends Component {
    constructor(props) {
        super(props)
        this.state = {
            BatchId: null,
            CompanyId:null,
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            }
        }
    }
    componentDidMount() {
        const { params } = this.props.match
        this.setState({
            BatchId: parseInt(params.BatchId,10),
            CompanyId: parseInt(params.CompanyId,10)
        }, () => {
            this.getMessage()
        })
    }
    getMessage() {
        let {pagination,BatchId, CompanyId} = this.state;
        let options = {
            BatchId:BatchId,
            CompanyId:CompanyId,
            Limit: pagination.pageSize,
            Offset: (pagination.current - 1) * pagination.pageSize
        }
        request('GetSealNoticeDetail', options)
        .then(res=> {
            this.setState({
                message: res.RecordList,
                smsContent: res.smsContent,
                emailContent: res.emailContent,
                pagination: {
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: res.TotalCount
                }
            })
        })
    }
    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.getMessage()
        })
    }
    showEmailPreview = () => {
        Modal.info({
            title: '邮件预览',
            width: '800px',
            content: <div dangerouslySetInnerHTML={{__html: this.state.emailContent}}></div>
        })
    }
    showSmsPreview = () => {
        Modal.info({
            title: '短信预览',
            width: '500px',
            content: <div dangerouslySetInnerHTML={{__html: this.state.smsContent}}></div>
        })
    }
    render() {
        const { BatchId, message = [], pagination } = this.state
        const columns = [{
            title: '手机号',
            dataIndex: 'Mobile'
        },
        {
            title: '邮箱',
            dataIndex: 'Email'
        },
        {
            title: '短信结果',
            dataIndex: 'SmsStatus',
            render: (value) => statusList[value]
        },
        {
            title: '邮件结果',
            dataIndex: 'EmailStatus',
            render: (value) => statusList[value]
        },
        {
            title: '短信更新的时间',
            dataIndex: 'SmsUpdateTime'
        },
        {
            title: '邮件更新时间',
            dataIndex: 'EmailUpdateTime'
        }]
        let paginationProps = {
            showSizeChanger: true,
            showQuickJumper: true,
            ...pagination,
        };
        return (
            <div>
                <Card bordered={false}>
                    <Breadcrumb>
                        <Breadcrumb.Item><Link to="/banAccount">首页</Link></Breadcrumb.Item>
                        <Breadcrumb.Item><Link to={`/banAccount/detail/${BatchId}`}>批次详情</Link></Breadcrumb.Item>
                        <Breadcrumb.Item>通知详情</Breadcrumb.Item>
                    </Breadcrumb>
                </Card>
                <Card title="用户信息">
                    <Row style={{ marginBottom: 50 }}>
                        <Col span={12}>公司ID：{message && message[0] ? message[0].CompanyId : null}</Col>
                        <Col span={12}>通知模版：{message && message[0] ? message[0].Type : null}</Col>
                    </Row>
                    <Button onClick={this.showEmailPreview}>邮件模版</Button>
                    <Button style={{marginLeft: 20}} onClick={this.showSmsPreview}>短信模版</Button>
                </Card>
                <Card title="通知列表" style={{ marginTop: 20 }}>
                    <Table columns={columns} dataSource={message} pagination={paginationProps} onChange={this.handleTableChange}/>
                </Card>
            </div>
        )
    }
}
export default MessageDetail