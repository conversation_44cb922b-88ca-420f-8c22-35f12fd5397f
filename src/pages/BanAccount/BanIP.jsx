
import { connect } from "react-redux";
import BanIPList from "./BanIPList";
import { setPagination, getBlockIPList, setLoading, getBlockIPLog, setHistoryPagination } from "../../actionCreator/banIP";

  const mapDispatchToProps = (dispatch) => { 
    console.log('dispatch',dispatch)
    return {
        setPagination: (data) =>  dispatch(setPagination("SET_PAGINATION",data)),
        getBlockIPList: (data) => dispatch(getBlockIPList(data)),
        setLoading: (data) => dispatch(setLoading("SET_LOADING",data)),
        getBlockIPLog: (data) => dispatch(getBlockIPLog(data)),
        setHistoryPagination: (data) => dispatch(setHistoryPagination("SET_HISTORY_PAGINATION"), data)
    }
  }
  const mapStateToProps = ({BanIPReducer}) => {
    console.log('BanIPReducer',BanIPReducer)
    return {
        ...BanIPReducer
    }
  }
  const banIPContiner = connect(mapStateToProps, mapDispatchToProps)(BanIPList)
  
  export default banIPContiner
  