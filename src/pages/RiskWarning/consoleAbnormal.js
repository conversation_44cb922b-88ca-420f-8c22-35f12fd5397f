import React, { Component ,Fragment} from 'react';
import { Card,Row,Form,Col,Input, Table, Button, Tabs,notification, Select, DatePicker, Modal } from 'antd';
import './index.css';
import exportFile from '../../components/expoertFile/index';
import { SensitiveApi } from '../../utils/request';
import moment from 'moment';
import { Link } from 'react-router-dom';
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const FormItem = Form.Item;
const Option = Select.Option;
class ConsoleAbnormal extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            BatchId: "",
            CompanyId: "",
            BeginTime: null,
            EndTime: null,
            modalVisile:false,
            BatchType:1,//批次类型（风险地区传入1，风险账号传入2）默认为1
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
            RiskCuntry:[],
            RiskCity:[],
            RiskRegion:[]
        };

    }
    // 挂载前查询
    componentDidMount() {
        this.GetRiskBatchList();
        this.GetRiskArea();
    }
    reSetField() {
      this.setState({
        BatchId: "",
        CompanyId: "",
        BeginTime: null,
        EndTime: null,
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
      });
    }
    //下载excel
    async downloadExcelFile(row = {},Id) {
    try {
      if (row.length === 0) {
        notification['error']({
          message: '无下载内容',
          description: '无下载内容'
        })
        return
      }
      let dataList = row.reduce((prev, item) => {
        prev.push([
          item.RiskId || '',
          item.BatchId || '',
          moment(item.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss') || '',
          item.BatchType || '1',
          item.CompanyId || '',
          item.CompanyName || '',
          item.AccessIp || '',
          item.AccessCountryName || '',
          item.AccessRegionName || '',
          item.AccessCityName || '',
          moment(item.AccessTime * 1000).format('YYYY-MM-DD HH:mm:ss') || '',
        ]);
        return prev;
      }, [["风险记录Id", "批次Id", "记录创建时间", "批次类型", "风险公司Id", "风险公司名","登录IP","登录国家","登录区域/省份","登录城市","登录时间"]]);
      let culumnWidthArray = [10, 10, 10, 10, 10, 10,10,10,10,10,10];
      let fileName = '下载批次-' + Id + ".xlsx";
      exportFile(dataList, culumnWidthArray, fileName);
    } catch (err) {
      notification['error']({
        message: '当前下载任务信息失败',
        description: err.message || '内部错误'
      })
    }
    }
    onSearch = ()=>{
        this.setState({
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
        },()=>{
            this.GetRiskBatchList()
        })
    }
    GetRiskBatchList = ()=> {
        let { BatchId,CompanyId,BeginTime,EndTime,BatchType } = this.state
        let options = {
            Action :'GetRiskBatchList',
            BatchType:BatchType,//批次类型（风险地区传入1，风险账号传入2）默认为1
            BatchId: parseInt(BatchId),
            BeginTime:BeginTime&&BeginTime.unix(),
            EndTime:EndTime&&EndTime.unix(),
            CompanyId:parseInt(CompanyId),
            Limit: this.state.pagination.pageSize,
            Offset: this.state.pagination.pageSize * (this.state.pagination.current - 1)
        }
        if(!BatchId){
            delete options.BatchId
        }
        if(!CompanyId){
            delete options.CompanyId
        }
        if(!BeginTime){
            delete options.BeginTime
        }
        if(!EndTime){
            delete options.EndTime
        }
        this.setState({ loading: true })
        SensitiveApi('GetRiskBatchList', options)
            .then(resp => {
                let message = '查询成功'
                this.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                        let {current,pageSize} = this.state.pagination
                        this.setState({
                            list: resp.BatchList,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.Count,
                            }
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    GetRiskArea = ()=> {
        let action = 'GetRiskArea';
        SensitiveApi(action, {
            Action:action
        }).then(resp => {
                let message = '查询成功'
                if (resp.RetCode === 0) {
                        this.setState({
                            RiskCuntry: resp.RiskCuntry,
                            RiskCity: resp.RiskCity,
                            RiskRegion: resp.RiskRegion
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    UpdateRiskArea = ()=> {
        let action = 'UpdateRiskArea';
        let {RiskCity, RiskCuntry, RiskRegion} = this.state
        let options = {
            Action: action
        }
        if(RiskCity){
            options['RiskCity'] = RiskCity
        }
        if(RiskCuntry){
            options['RiskCuntry'] = RiskCuntry
        }
        if(RiskRegion){
            options['RiskRegion'] = RiskRegion
        }
        SensitiveApi(action, options).then(resp => {
                this.setState({modalVisile:false})
                let message = '更新成功'
                if (resp.RetCode !== 0) {
                    message = resp.Message || resp.RetCode + "更新失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.GetRiskBatchList()
        })
    }
    //上部份，获取信息
    renderAdvancedForm() {
        return (
            <Form layout="inline" className="ant-advanced-search-form">
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144444}>
                        <FormItem label="批次ID">
                            <Input style={{ width: '100%' }} value={this.state.BatchId} placeholder="请输入对应的批次ID" onChange={(e) => { this.setState({ BatchId: e.target.value }) }} allowClear/>
                        </FormItem>
                    </Col>
                    <Col span={10} key={1555}>
                        <FormItem label="公司ID">
                            <Input style={{ width: '100%' }} value={this.state.CompanyId} placeholder="请输入对应的公司Id" onChange={(e) => { this.setState({ CompanyId: e.target.value }) }} allowClear/>
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144664}>
                    <FormItem label="时间">
                    <RangePicker
                        style={{width:'100%'}}
                        ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
                        showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
                        format="YYYY-MM-DD"
                        value={[this.state.BeginTime, this.state.EndTime]}
                        onChange= {(v)=>{
                          //选择同一天时，默认设置为0点-23点59
                          if(v[0].unix()===v[1].unix()){
                            v[0] = v[0].startOf('day')
                            v[1] = v[1].endOf('day')
                          }
                          this.setState({
                            BeginTime: v[0],
                            EndTime:v[1],
                          })
                        }}

                    />
                    </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={6} key={2} >
                        <FormItem style={{ width: '100%', marginLeft: "80px" }} label="">
                            <Button style={{ marginRight: "16px" }} onClick={this.onSearch} htmlType="submit">查询</Button>
                            <Button onClick={() => {
                                this.reSetField()
                            }}>重置</Button>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        );
    }
    download = (row)=>{
        let self = this,
            action = 'GetRiskRecordList',
            options = {
                    Action: action,
                    BatchType: row.BatchType,
                    BatchId: parseInt(row.BatchId),
            }
            //先获取批次长度
            SensitiveApi(action, options)
            .then(resp=>{
              if (resp.RetCode === 0) {
                options.Limit = resp.Count
                options.Offset = 0
              }
              SensitiveApi(action, options)
              .then(resp => {
                  self.setState({
                      loading: false,
                  })
                  let message = '查询成功'
                  if (resp.RetCode === 0) {
                      if (resp.RecordList) {
                          this.downloadExcelFile(resp.RecordList,row.BatchId)
                      }
                      return
                  } else {
                      message = resp.Message || resp.RetCode + "查询失败"
                  }
                  notification.open({
                      message:message
                  })

              }).catch(err => {
                  // 报错
                  notification['error']({
                      message: '请求失败',
                      description: err.message || '内部错误'
                  })
                  return;
              })
            })


    }
    handleCountryChange = (value)=> {
        this.setState({
            RiskCuntry: value
        })
    }
    handleCityChange = (value)=> {
        this.setState({
            RiskCity: value
        })
    }
    render() {
        let { loading, list, modalVisile, pagination, RiskCuntry,RiskCity} = this.state;
        const columns1 = [
            {
                title: '批次ID',
                dataIndex: 'BatchId',
            },
            {
                title: '批次名称',
                dataIndex: 'BatchName',
            },
            {
                title: '创建时间',
                dataIndex: 'CreateTime',
                render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
            },
            {
                title: '批次详情',
                dataIndex: 'AuthType',
                render: (val, row) => {
                    let urlAddress = `/RiskWarning/RiskWarningDetails?params=${encodeURI(JSON.stringify({BatchId:row.BatchId,BatchType:row.BatchType}))}`
                    return (
                      <Fragment>
                        {
                          <Link to={urlAddress} target='_blank' >查看</Link>
                        }
                      </Fragment>
                    )
                  },
            },
            {
                title: '导出Excel',
                render: (val, row) => {
                    return (
                        <Fragment>
                            <Button  type="download"  data-key = {row.BatchId} onClick = {()=>{
                                this.download(row)
                            }}>下载</Button>
                        </Fragment>
                    )
                },
            }
        ]
        pagination = {
             ...pagination,
             showQuickJumper: true,
             showSizeChanger: true,
        }
        return (
            <Card bordered={false} bodyStyle={{padding:0}}>
                <div>
                    <Card title="搜索" style={{marginBottom: 24 }} bordered={false} >
                        {this.renderAdvancedForm()}
                    </Card>
                    <Button style={{ margin: "16px" }} onClick={()=> {
                        this.setState({
                          modalVisile: true
                        })
                    }} htmlType="submit">监控区域</Button>
                    <Tabs defaultActiveKey="TaskList">
                        <TabPane tab="结果" key="GetList">
                            <Table
                                loading={loading}
                                dataSource={list}
                                columns= {columns1}
                                pagination={pagination}
                                onChange={this.handleTableChange}
                                rowKey='BatchId'
                            />
                        </TabPane>
                    </Tabs>
                    <Modal
                        title = "监控区域表单"
                        visible = {modalVisile}
                        onCancel={()=>{this.setState({modalVisile:false},()=>{
                            this.GetRiskArea();
                        })}}
                        onOk={()=>{this.UpdateRiskArea()}}
                    >
                            <FormItem label="国家与地区">
                            <Select mode="tags" style={{ width: '100%' }} defaultValue={RiskCuntry} value={RiskCuntry} onChange={this.handleCountryChange} tokenSeparators={[',', ' ', '\t', '\n']}>
                                {
                                    RiskCuntry.map(item=>{
                                        return <Option key={item} value={item}>{item}</Option>
                                    })
                                }
                            </Select>
                            </FormItem>
                            <FormItem label="城市">
                            <Select mode="tags" style={{ width: '100%' }} defaultValue={RiskCity} value={RiskCity} onChange={this.handleCityChange} tokenSeparators={[',', ' ', '\t', '\n']}>
                            {  RiskCity.map(item=>{
                                    return <Option key={item} value={item}>{item}</Option>
                                })
                                }
                            </Select>
                            </FormItem>
                    </Modal>
                </div>
            </Card>
        )
    }
}

const ConsoleAbnormalPage = Form.create()(ConsoleAbnormal);
export default ConsoleAbnormalPage;
