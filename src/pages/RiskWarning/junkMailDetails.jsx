import React, { Component } from 'react';
import { Card,Form, Table,Tabs,notification} from 'antd';
import './index.css';
import { SensitiveApi } from '../../utils/request';
import { Breadcrumb } from 'antd';
import moment from 'moment';

const { TabPane } = Tabs;

//状态枚举,将英文状态转成中文在前端显示
class JunkEmailDetails extends Component {
    constructor(props) {
        super(props)
        let params = decodeURI(this.props.location.search.split("params=")[1])
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            BatchId: JSON.parse(params).BatchId,
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
        };

    }
    // 挂载前查询
    componentDidMount() {
        this.GetRiskRecordListSpams()
    }
    GetRiskRecordListSpams = ()=> {
        let {BatchId,pagination} = this.state
        let self = this,
            action = 'GetRiskRecordListSpams',
            options = {
                Action: action,
                BatchId: BatchId,
                Limit: pagination.pageSize,
                Offset: pagination.pageSize * (pagination.current - 1)
            }
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                self.setState({
                    loading: false,
                })
                let message = '查询成功'
                if (resp.RetCode === 0) {
                        let { current, pageSize } = this.state.pagination
                        self.setState({
                            list: resp.RecordList,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.Count,
                            }
                        }, () => {
                            console.log(this.state.Count)
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
        })
    }

    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.GetRiskRecordListSpams()
        })
    }
    render() {
        let { loading, list, pagination } = this.state
        pagination = {
            ...pagination,
            showQuickJumper: true,
            showSizeChanger: true,
        }
        const columns1 = [
            {
                title: '源IP',
                dataIndex: 'SrcIp',
            },
            {
                title: '目标端口',
                render: ()=>25
            },
            {
                title: '请求次数',
                dataIndex: 'Count',
            },
            {
                title: '第一次请求时间',
                dataIndex: 'TimeStamp',
                render: val => <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span>,
            },

        ];
        return (
            <Card bordered={true}>
                <Breadcrumb style={{marginBottom:30}}>
                        <Breadcrumb.Item><a href="" onClick={()=>{
                            this.props.history.push("/RiskWarning")
                        }}>首页</a></Breadcrumb.Item>
                        <Breadcrumb.Item>批次详情</Breadcrumb.Item>
                </Breadcrumb>
                <div >
                    <Tabs defaultActiveKey="TaskList">
                        <TabPane tab="批次详情" key="TaskList">
                            <Table
                                loading={loading}
                                rowKey='SrcIp'
                                dataSource={list}
                                columns={columns1}
                                pagination={pagination}
                                onChange={this.handleTableChange}
                            />
                        </TabPane>
                    </Tabs>
                </div>
            </Card>
        )
    }
}

const RiskWarningDetailsPage = Form.create()(JunkEmailDetails);
export default RiskWarningDetailsPage;
