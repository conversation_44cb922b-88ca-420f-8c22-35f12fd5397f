import React, { Component } from "react";
import { Card, Form, Table, Tabs, notification } from "antd";
import "./index.css";
import { SensitiveApi } from "../../utils/request";
import { Breadcrumb } from "antd";
import moment from "moment";

const { TabPane } = Tabs;

//状态枚举,将英文状态转成中文在前端显示
class AbnormalModifyDetails extends Component {
  constructor(props) {
    super(props);
    let params = decodeURI(this.props.location.search.split("params=")[1]);
    this.state = {
      list: [], // 列表初始值,
      loading: false,
      BatchId: JSON.parse(params).BatchId,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
    };
  }
  // 挂载前查询
  componentDidMount() {
    this.GetRiskChangeMsgRecordInfo();
  }
  GetRiskChangeMsgRecordInfo = () => {
    let { BatchId, pagination } = this.state;
    let self = this,
      action = "GetRiskChangeMsgRecordInfo",
      options = {
        Action: action,
        BatchId: BatchId,
        Limit: pagination.pageSize,
        Offset: pagination.pageSize * (pagination.current - 1),
      };
    self.setState({ loading: true });
    SensitiveApi(action, options)
      .then((resp) => {
        self.setState({
          loading: false,
        });
        let message = "查询成功";
        if (resp.RetCode === 0) {
          let { current, pageSize } = this.state.pagination;
          self.setState(
            {
              list: resp.RecordList,
              pagination: {
                current: current,
                pageSize: pageSize,
                total: resp.TotalCount,
              },
            },
            () => {
              console.log(this.state.Count);
            }
          );
          return;
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };

  handleTableChange = (pagination, filters, sorter) => {
    console.log(pagination, filters, sorter);
    this.setState(
      {
        pagination: {
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: this.state.pagination.total,
        },
      },
      () => {
        this.GetRiskChangeMsgRecordInfo();
      }
    );
  };
  render() {
    let { loading, list, pagination } = this.state;
    pagination = {
      ...pagination,
      showQuickJumper: true,
      showSizeChanger: true,
    };
    const columns1 = [
      {
        title: "公司ID",
        dataIndex: "CompanyId",
      },
      {
        title: "公司名称",
        dataIndex: "CompanyName",
      },
      {
        title: "注册手机号",
        dataIndex: "RegisterPhone",
      },
      {
        title: "修改后手机号",
        dataIndex: "ChangePhone",
      },
      {
        title: "注册邮箱",
        dataIndex: "RegisterEmail",
      },
      {
        title: "修改后邮箱",
        dataIndex: "ChangeEmail",
      },
      {
        title: "修改时间",
        dataIndex: "ChangeTime",
        render: (val) => (
          <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
      {
        title: "修改时登录IP",
        dataIndex: "IP",
      },
      {
        title: "登录国家",
        dataIndex: "Country",
      },
      {
        title: "登录区域/省份",
        dataIndex: "Province",
      },
      {
        title: "登录城市",
        dataIndex: "City",
      },
    ];
    return (
      <Card bordered={true}>
        <Breadcrumb style={{ marginBottom: 30 }}>
          <Breadcrumb.Item>
            <a
              href=""
              onClick={() => {
                this.props.history.push("/RiskWarning");
                sessionStorage.setItem("RiskWarningTabKey", 4);
              }}
            >
              异常修改手机号/邮箱
            </a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>批次详情</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <Tabs defaultActiveKey="TaskList">
            <TabPane tab="批次详情" key="TaskList">
              <Table
                loading={loading}
                rowKey="_id"
                dataSource={list}
                columns={columns1}
                pagination={pagination}
                onChange={this.handleTableChange}
              />
            </TabPane>
          </Tabs>
        </div>
      </Card>
    );
  }
}

const RiskWarningDetailsPage = Form.create()(AbnormalModifyDetails);
export default RiskWarningDetailsPage;
