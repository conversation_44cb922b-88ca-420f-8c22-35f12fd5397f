import React, { Component, Fragment } from "react";
import {
  Card,
  Form,
  Table,
  Popover,
  Tabs,
  notification,
  Tag,
  Button,
  Input,
} from "antd";
import "./index.css";

import { SensitiveApi } from "../../utils/request";
import { Breadcrumb } from "antd";

const { TabPane } = Tabs;
const { Search } = Input;

//状态枚举,将英文状态转成中文在前端显示
class RiskWarningDetails extends Component {
  constructor(props) {
    super(props);
    let params = decodeURI(this.props.location.search.split("params=")[1]);
    this.state = {
      list: [], // 列表初始值,
      loading: false,
      BatchId: JSON.parse(params).BatchId,
      IsPersonalPayment: null,
      OverseasLoginBehavior: null,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  }
  // 挂载前查询
  componentDidMount() {
    this.GetRiskRecordForInpour();
  }
  GetRiskRecordForInpour = (CompanyId) => {
    let { BatchId, OverseasLoginBehavior, IsPersonalPayment, pagination } = this.state;
    let self = this,
      action = "GetRiskRecordForInpour",
      options = {
        Action: action,
        BatchId: BatchId,
        ...(CompanyId && { CompanyId: parseInt(CompanyId) }),
        ...(OverseasLoginBehavior!==null && { OverseasLoginBehavior}),
        ...(IsPersonalPayment!==null && { IsPersonalPayment}),
        Limit: pagination.pageSize,
        Offset: pagination.pageSize * (pagination.current - 1),
      };
    self.setState({ loading: true });
    SensitiveApi(action, options)
      .then((resp) => {
        self.setState({
          loading: false,
        });
        let message = "查询成功";
        if (resp.RetCode === 0) {
          let { current, pageSize } = this.state.pagination;
          self.setState(
            {
              list: resp.List,
              pagination: {
                current: current,
                pageSize: pageSize,
                total: resp.Count,
              },
            },
            () => {
              console.log(this.state.Count);
            }
          );
          return;
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };

  handleTableChange = (pagination, filters) => {
    let IsPersonalPayment = null, OverseasLoginBehavior = null
    if(filters.IsPersonalPayment?.length===1){
      IsPersonalPayment = filters.IsPersonalPayment[0]
    }
    if(filters.OverseasLoginBehavior?.length===1){
      OverseasLoginBehavior = filters.OverseasLoginBehavior[0]
    }
    this.setState(
      {
        IsPersonalPayment,
        OverseasLoginBehavior,
        pagination: {
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: this.state.pagination.total,
        },
      },
      () => {
        this.GetRiskRecordForInpour();
      }
    );
  };

  onSearch = (val) => {
    this.setState(
      {
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
        },
      },
      () => {
        this.GetRiskRecordForInpour(val);
      }
    );
  };
  render() {
    let { loading, list, pagination } = this.state;
    pagination = {
      ...pagination,
      showQuickJumper: true,
      showSizeChanger: true,
    };
    const columns1 = [
      {
        title: "公司Id",
        dataIndex: "CompanyId",
        fixed: 'left',
      },
      {
        title: "公司名称",
        dataIndex: "CompanyName",
      },
      {
        title: "认证类型",
        dataIndex: "AuthType",
      },
      {
        title: "客户等级",
        dataIndex: "VIPLevel",
      },
      {
        title: "所属BU",
        dataIndex: "BU",
      },

      {
        title: "是否涉案",
        dataIndex: "IsInvolved",
      },
      {
        title: "是否存在个人打款",
        dataIndex: "IsPersonalPayment",
        filters: [
          {
            text: '是',
            value: true,
          },
          {
            text: '否',
            value: false,
          }
        ],
      },
      {
        title: "登录地",
        dataIndex: "OverseasLoginBehavior",
        filters: [
          {
            text: '有海外登录行为',
            value: true,
          },
          {
            text: '无海外登录行为',
            value: false,
          }
        ],
      },
      {
        title: "充值记录数",
        dataIndex: "TotalInpourRecords",
      },
      {
        title: "涉及账户数",
        dataIndex: "TotalPayAccounts",
      },
      {
        title: "支付宝充值数",
        dataIndex: "AlipayRecords",
      },
      {
        title: "支付宝账户数",
        dataIndex: "AlipayAccounts",
        render(val) {
          return (
            <Fragment>
              <Popover
                placement="left"
                content={
                  <div>
                    {val.map((item) => (
                      <div key={item} style={{ marginBottom: "8px" }}>
                        <Tag>{item}</Tag>
                      </div>
                    ))}
                  </div>
                }
                trigger="click"
              >
                {val.length ? (
                  <Button style={{ cursor: "pointer" }}>查看</Button>
                ) : (
                  "无"
                )}
              </Popover>
            </Fragment>
          );
        },
      },
      {
        title: "银行充值数",
        dataIndex: "BankRecords",
      },
      {
        title: "银行账户数",
        dataIndex: "BankAccounts",
        render(val) {
          return (
            <Fragment>
              <Popover
                placement="left"
                content={
                  <div className="scrollWrap">
                    {val.map((item) => (
                      <div key={item} style={{ marginBottom: "8px" }}>
                        <Tag>{item}</Tag>
                      </div>
                    ))}
                  </div>
                }
                trigger="click"
              >
                {val.length ? (
                  <Button style={{ cursor: "pointer" }}>查看</Button>
                ) : (
                  "无"
                )}
              </Popover>
            </Fragment>
          );
        },
      },

    ];
    return (
      <Card bordered={true}>
        <Breadcrumb style={{ marginBottom: 30 }}>
          <Breadcrumb.Item>
            <a
              href=""
              onClick={() => {
                this.props.history.push("/RiskWarning");
                sessionStorage.setItem("RiskWarningTabKey",2)
              }}
            >
              打款异常
            </a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>批次详情</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <Tabs defaultActiveKey="TaskList">
            <TabPane tab="批次详情" key="TaskList">
              <Search
                placeholder="请输入公司ID"
                onSearch={this.onSearch}
                style={{
                  width: 200,
                  position: "absolute",
                  right: 0,
                  top: 0,
                }}
              />
              <Table
                loading={loading}
                rowKey={(record) => record.CompanyId}
                dataSource={list}
                columns={columns1}
                scroll={{
                  x: 1300,
                }}
                pagination={pagination}
                onChange={this.handleTableChange}
              />
            </TabPane>
          </Tabs>
        </div>
      </Card>
    );
  }
}

const RiskWarningDetailsPage = Form.create()(RiskWarningDetails);
export default RiskWarningDetailsPage;
