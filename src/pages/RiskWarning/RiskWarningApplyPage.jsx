import React, { Component } from "react";
import { Row, Col, Modal, Card, Form, Table, notification } from "antd";
import "./index.css";
import { SensitiveApi } from "../../utils/request";
import { Breadcrumb } from "antd";
import moment from "moment";

const FormItem = Form.Item;
class RiskWarningApply extends Component {
  constructor(props) {
    super(props);
    this.state = {
      DomainInfo: [],
      RelevancyRiskList: [],
      params: JSON.parse(
        decodeURI(this.props.location.search.split("params=")[1])
      ),
      loading: false,
      modal: false,
    };
  }

  // 挂载前查询
  componentDidMount() {
    this.GetRiskRecordInfo();
  }
  GetRiskRecordInfo = () => {
    let { params } = this.state,
      action = "GetRiskRecordInfo",
      options = {
        Action: action,
        ...params,
      };
    this.setState({ loading: true });
    SensitiveApi(action, options)
      .then((resp) => {
        this.setState({
          loading: false,
        });
        let message = "查询成功";
        if (resp.RetCode === 0) {
          this.setState({
            RiskRecordInfo: resp.RiskRecordInfo,
            RelevancyRiskList: resp.RelevancyRiskList,
            DomainInfo: resp.DomainInfo,
          });
          return;
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  //上部份，获取信息
  renderAdvancedForm() {
    let { RiskRecordInfo } = this.state;
    if (!RiskRecordInfo) {
      return null;
    }
    return (
      <Form
        layout="inline"
        className="ant-advanced-search-form"
        style={{ border: "none" }}
      >
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={1555}>
            <FormItem label="公司ID">{RiskRecordInfo.CompanyId}</FormItem>
          </Col>
          <Col span={10} key={144444}>
            <FormItem label="公司名称">{RiskRecordInfo.CompanyName}</FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={14455444}>
            <FormItem label="登陆IP">{RiskRecordInfo.AccessIp}</FormItem>
          </Col>
          <Col span={10} key={14455444}>
            <FormItem label="国家">{RiskRecordInfo.AccessCountryName}</FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={14455444}>
            <FormItem label="区域/省份">
              {RiskRecordInfo.AccessRegionName}
            </FormItem>
          </Col>
          <Col span={10} key={14455444}>
            <FormItem label="城市">{RiskRecordInfo.AccessCityName}</FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={14455444}>
            <FormItem label="登陆时间">
              {moment(RiskRecordInfo.AccessTime * 1000).format(
                "YYYY-MM-DD HH:mm:ss"
              )}
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  }
  render() {
    let { loading, modal, DomainInfo, RelevancyRiskList } = this.state;
    const columns1 = [
      {
        title: "IP",
        dataIndex: "IP",
      },
      {
        title: "归属地",
        dataIndex: "Region",
      },
      {
        title: "域名",
        dataIndex: "Domain",
        render: (val) => {
          if (val) {
            return (
              <span
                style={{ color: "blue", cursor: "pointer" }}
                onClick={() => {
                  window.open("http://" + val, "_blank");
                }}
              >
                {val}
              </span>
            );
          }
        },
      },
      {
        title: "资源创建时间",
        dataIndex: "CreateTime",
        render: (val) => (
          <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
    ];
    const columns2 = [
      {
        title: "关联违规公司ID",
        dataIndex: "CompanyId",
      },
      {
        title: "关联IP",
        dataIndex: "IP",
      },
      {
        title: "关联违规原因",
        dataIndex: "Cause",
      },
    ];
    return (
      <Card bordered={false}>
        <div>
          <Breadcrumb style={{ marginBottom: 20 }}>
            <Breadcrumb.Item>
              <a
                href=""
                onClick={() => {
                  this.props.history.push("/RiskWarning");
                }}
              >
                首页
              </a>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <a
                onClick={() => {
                  window.history.go(-1);
                }}
              >
                批次详情
              </a>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <span
                onClick={() => {
                  this.setState({ modal: true });
                }}
              >
                处理
              </span>
            </Breadcrumb.Item>
          </Breadcrumb>
          <Card
            title="风险账户信息"
            style={{ marginBottom: 24 }}
            bordered={true}
          >
            {this.renderAdvancedForm()}
          </Card>
          <Card
            title="关联违规信息"
            style={{ marginBottom: 24 }}
            bordered={true}
          >
            <Form
              layout="inline"
              className="ant-advanced-search-form"
              style={{ border: "0px solid #ffffff" }}
            >
              {RelevancyRiskList && RelevancyRiskList.length > 0
                ? RelevancyRiskList.map((item) => {
                    return (
                      <Row
                        key={JSON.stringify(item)}
                        gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
                      >
                        <Col span={10} key={item.Cause}>
                          <FormItem label="违规内容">{item.Cause}</FormItem>
                        </Col>
                        <Col span={10} key={item.Remark}>
                          <FormItem label="备注">{item.Remark}</FormItem>
                        </Col>
                      </Row>
                    );
                  })
                : "暂无数据"}
            </Form>
          </Card>
          <Card title="资源信息" style={{ marginBottom: 24 }} bordered={true}>
            <Table
              loading={loading}
              dataSource={DomainInfo}
              columns={columns1}
            />
          </Card>
        </div>
        <Modal
          title="关联账户"
          visible={modal}
          onCancel={() => {
            this.setState({ modal: false });
          }}
          footer={null}
        >
          <Table dataSource={RelevancyRiskList} columns={columns2} />
        </Modal>
      </Card>
    );
  }
}

const RiskWarningApplyPage = Form.create()(RiskWarningApply);
export default RiskWarningApplyPage;
