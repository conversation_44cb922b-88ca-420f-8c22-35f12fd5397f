import React,{useState, useEffect, Fragment} from 'react';
import { Card,Row,Form,Col,Input, Button,DatePicker,Table, notification } from 'antd';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { SensitiveApi } from '../../utils/request';
import exportFile from '../../components/expoertFile/index';
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const PaymentAbnormal = ()=>{
  //上部份，获取信息
  const [BatchId, setBatchId] = useState('')
  const [SrcIP, setSrcIP] = useState('')
  const [BeginTime, setBeginTime] = useState(null)
  const [EndTime, setEndTime] = useState(null)
  const [BatchType] = useState(3)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })
  const [loading, setLoading] = useState(false)
  const [abnormalList, setAbnormalList] = useState([])

  const  onSearch = ()=>{
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0
    })
  }
  const GetRiskBatchList = ()=> {
    let options = {
        Action :'GetRiskBatchList',
        BatchType:BatchType,
        BatchId: parseInt(BatchId),
        BeginTime:BeginTime&&BeginTime.unix(),
        EndTime:EndTime&&EndTime.unix(),
        SrcIP:SrcIP,
        Limit: pagination.pageSize,
        Offset: pagination.pageSize * (pagination.current - 1)
    }
    if(!BatchId){
        delete options.BatchId
    }
    if(!SrcIP){
        delete options.SrcIP
    }
    if(!BeginTime){
        delete options.BeginTime
    }
    if(!EndTime){
        delete options.EndTime
    }
    setLoading(true)
    SensitiveApi('GetRiskBatchList', options)
        .then(resp => {
            let message = '查询成功'
            setLoading(false)
            if (resp.RetCode === 0) {
                setAbnormalList(resp.BatchList.sort((a,b)=>b.BatchId-a.BatchId))
                pagination.total = resp.Count
                setPagination(pagination)
                return
            } else {
                message = resp.Message || resp.RetCode + "查询失败"
            }
            notification.open({
                message: message,
            });
        }).catch(err => {
            // 报错
            notification['error']({
                message: '请求失败',
                description: err.message || '内部错误'
            })
            return;
        })
  }
  const  handleTableChange = (pagination) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total
    })
  }
  const renderAdvancedForm = ()=>{
    return (
        <Form layout="inline" className="ant-advanced-search-form">
            <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                <Col span={10} key={144444}>
                    <FormItem label="批次ID">
                        <Input style={{ width: '100%' }} value={BatchId} placeholder="请输入对应的批次ID" onChange={(e) => { setBatchId(e.target.value) }} allowClear/>
                    </FormItem>
                </Col>
                <Col span={10} key={1555}>
                    <FormItem label="源IP">
                        <Input style={{ width: '100%' }} value={SrcIP} placeholder="请输入对应的源IP" onChange={(e) => {
                           console.log('e.target.value', e.target.value)
                        setSrcIP(e.target.value)}} allowClear/>
                    </FormItem>
                </Col>
            </Row>
            <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                <Col span={10} key={144664}>
                <FormItem label="时间">
                <RangePicker
                    style={{width:'100%'}}
                    ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
                    showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
                    format="YYYY-MM-DD"
                    value={[BeginTime, EndTime]}
                    onChange= {(v)=>{
                      if(v.length===0){
                        setBeginTime(null)
                        setEndTime(null)
                        return
                      }
                      //选择同一天时，默认设置为0点-23点59
                      if(v[0].unix()===v[1].unix()){
                        v[0] = v[0].startOf('day')
                        v[1] = v[1].endOf('day')
                      }
                      setBeginTime(v[0])
                      setEndTime(v[1])
                    }}
                />
                </FormItem>
                </Col>
            </Row>
            <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                <Col span={6} key={2} >
                    <FormItem style={{ width: '100%', marginLeft: "80px" }} label="">
                        <Button style={{ marginRight: "16px" }} onClick={onSearch} htmlType="submit">查询</Button>
                        <Button onClick={() => {
                            reSetField();
                        }}>重置</Button>
                    </FormItem>
                </Col>
            </Row>
        </Form>
    );
  }
  const downloadExcelFile = async (row = {},Id) =>{
  try {
    if (row.length === 0) {
      notification['error']({
        message: '无下载内容',
        description: '无下载内容'
      })
      return
    }
    let dataList = row.reduce((prev, item) => {
      prev.push([
        item.BatchId || '',
        item.SrcIp || '',
        25,
        item.Count || '',
        moment(item.TimeStamp).format('YYYY-MM-DD HH:mm:ss') || '',
      ]);
      return prev;
    }, [["批次Id", "源IP", "目标端口", "请求次数", "第一次请求时间"]]);
    let culumnWidthArray = [10, 10, 10, 10, 10];
    let fileName = '下载批次-' + Id + ".xlsx";
    exportFile(dataList, culumnWidthArray, fileName);
  } catch (err) {
    notification['error']({
      message: '当前下载任务信息失败',
      description: err.message || '内部错误'
    })
  }
  }
  const download = (row)=>{
    let action = 'GetRiskRecordListSpams',
        options = {
                Action: action,
                BatchId: parseInt(row.BatchId),
        }
    SensitiveApi(action, options).then((resp) => {
      if (resp.RetCode === 0) {
        options.Limit = resp.Count;
        options.Offset = 0
      }
      SensitiveApi(action, options)
        .then((resp) => {
          setLoading(false);
          let message = "查询成功";
          if (resp.RetCode === 0) {
            if (resp.RecordList) {
              downloadExcelFile(resp.RecordList, row.BatchId);
            }
            return;
          } else {
            message = resp.Message || resp.RetCode + "查询失败";
          }
          notification.open({
            message: message,
          });
        })
        .catch((err) => {
          // 报错
          notification["error"]({
            message: "请求失败",
            description: err.message || "内部错误",
          });
          return;
        });
    });
  }
  const reSetField = ()=> {
    setBatchId('')
    setSrcIP('')
    setBeginTime(null)
    setEndTime(null)
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0
    })
  }
  useEffect(() => {
    GetRiskBatchList()
  },[pagination]);
  const columns = [
    {   key: '1',
        title: '批次ID',
        dataIndex: 'BatchId',
    },
    {   key: '2',
        title: '批次名称',
        dataIndex: 'BatchName',
    },
    {   key: '3',
        title: '创建时间',
        dataIndex: 'CreateTime',
        render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {   key: '4',
        title: '批次详情',
        dataIndex: 'AuthType',
        render: (val, row) => {
            let urlAddress = `/RiskWarning/JunkEmailDetails?params=${encodeURI(JSON.stringify({BatchId:row.BatchId}))}`
            return (
              <Fragment>
                {
                  <Link to={urlAddress} target='_blank'>查看</Link>
                }
              </Fragment>
            )
          },
    },
    {   key: '5',
        title: '导出Excel',
        render: (val, row) => {
            return (
                <Fragment>
                    <Button  type="download"  data-key = {row.BatchId} onClick = {()=>{
                        download(row)
                    }}>下载</Button>
                </Fragment>
            )
        },
    }
]
  return (
    <>
      <Card title="搜索" style={{marginBottom: 24 }} bordered={false} >
          {renderAdvancedForm()}
      </Card>
      <Card title="结果" style={{marginBottom: 24 }} bordered={false} >
        <Table
          loading={loading}
          dataSource={abnormalList}
          columns= {columns}
          pagination={pagination}
          onChange={handleTableChange}
          rowKey='BatchId'
        />
      </Card>
    </>

  )
}

export default PaymentAbnormal
