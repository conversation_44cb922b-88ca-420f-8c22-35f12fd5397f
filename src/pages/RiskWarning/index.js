import React, { Component } from 'react';
import {Tabs,Card} from 'antd';
import './index.css';
import ConsoleAbnormalPage from './consoleAbnormal'
import PaymentAbnormal from './paymentAbnormal'
import JunkMail from './junkMail'
import AbnormalModify from './AbnormalModify'
import DNSBatchList from './DNSBatchList'
const { TabPane } = Tabs;
class RiskWarningPage extends Component {
    render() {
      const defaultActiveKey = sessionStorage.getItem('RiskWarningTabKey') || 1;
        return (
          <Card>
            <Tabs defaultActiveKey={defaultActiveKey} >
              <TabPane tab="控制台异常登录" key="1">
                <ConsoleAbnormalPage />
              </TabPane>
              <TabPane tab="打款异常" key="2">
                <PaymentAbnormal/>
              </TabPane>
              <TabPane tab="垃圾邮件发送" key="3">
                <JunkMail/>
              </TabPane>
              <TabPane tab="异常修改手机号/邮箱" key="4">
                <AbnormalModify/>
              </TabPane>
              <TabPane tab="53风险数据" key="5">
                <DNSBatchList/>
              </TabPane>
            </Tabs>
          </Card>
        )
    }
}

export default RiskWarningPage;
