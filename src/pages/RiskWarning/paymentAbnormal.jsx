import React, { useState, useEffect, Fragment } from "react";
import {
  Card,
  Row,
  Form,
  Col,
  Input,
  Button,
  DatePicker,
  Table,
  notification,
} from "antd";
import moment from "moment";
import { Link } from "react-router-dom";
import { SensitiveApi } from "../../utils/request";
import exportFile from "../../components/expoertFile/index";
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const PaymentAbnormal = () => {
  //上部份，获取信息
  const [BatchId, setBatchId] = useState("");
  const [CompanyId, setCompanyId] = useState("");
  const [BeginTime, setBeginTime] = useState("");
  const [EndTime, setEndTime] = useState("");
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [loading, setLoading] = useState(false);
  const [abnormalList, setAbnormalList] = useState([]);

  const onSearch = () => {
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0,
    });
  };
  //下载excel
  const downloadExcelFile = async (row = [], Id) => {
    try {
      if (row.length === 0) {
        notification["error"]({
          message: "无下载内容",
          description: "无下载内容",
        });
        return;
      }
      let dataList = row.reduce(
        (prev, item) => {
          prev.push([
            item.CompanyId || "",
            item.CompanyName || "",
            item.BU || "",
            item.IsInvolved || "",
            item.IsPersonalPayment || "",
            item.OverseasLoginBehavior || "",
            item.TotalInpourRecords || "",
            item.TotalPayAccounts || "",
            item.AlipayRecords || "",
            item.AlipayAccounts.join(",") || "",
            item.BankRecords || 0,
            item.BankAccounts.join(",") || "无"
          ]);
          return prev;
        },
        [
          [
            "公司Id",
            "公司名称",
            "所属BU",
            "是否涉案",
            "是否存在个人打款",
            "登录地",
            "充值记录数",
            "涉及账户数",
            "支付宝充值数",
            "支付宝账户数",
            "银行充值数",
            "银行账户数",
          ],
        ]
      );
      let culumnWidthArray = [10, 20, 8, 10, 14, 12, 12, 14, 18, 18, 10, 30];
      let fileName = "下载批次-" + Id + ".xlsx";
      exportFile(dataList, culumnWidthArray, fileName);
    } catch (err) {
      notification["error"]({
        message: "当前下载任务信息失败",
        description: err.message || "内部错误",
      });
    }
  };
  const download = (row) => {
    let action = "GetRiskRecordForInpour",
      options = {
        Action: action,
        BatchId: row.BatchId,
      };
    //先获取批次长度
    SensitiveApi(action, options).then((resp) => {
      if (resp.RetCode === 0) {
        // 如果Count小于等于默认长度，不用第二次请求
        if (resp.Count > 20) {
          options.Limit = resp.Count;
          options.Offset = 0;
          SensitiveApi(action, options)
            .then((resp) => {
              let message;
              if (resp.RetCode === 0) {
                if (resp.List) {
                  downloadExcelFile(resp.List, row.BatchId);
                }
                return;
              } else {
                message = resp.Message || resp.RetCode + "下载失败";
              }
              notification.open({
                message: message,
              });
            })
            .catch((err) => {
              // 报错
              notification["error"]({
                message: "请求失败",
                description: err.message || "内部错误",
              });
              return;
            });
        } else {
          downloadExcelFile(resp.List, row.BatchId);
        }
      } else {
        let message = resp.Message || resp.RetCode + "下载失败";
        notification.open({
          message: message,
        });
      }
    });
  };
  const GetRiskBatchList = () => {
    let options = {
      Action: "GetRiskBatchForInpour",
      BatchId: BatchId,
      BeginTime: BeginTime && BeginTime.unix(),
      EndTime: EndTime && EndTime.unix(),
      CompanyId: parseInt(CompanyId),
      Limit: pagination.pageSize,
      Offset: pagination.pageSize * (pagination.current - 1),
    };
    if (!BatchId) {
      delete options.BatchId;
    }
    if (!CompanyId) {
      delete options.CompanyId;
    }
    if (!BeginTime) {
      delete options.BeginTime;
    }
    if (!EndTime) {
      delete options.EndTime;
    }
    setLoading(true);
    SensitiveApi("GetRiskBatchForInpour", options)
      .then((resp) => {
        let message = "查询成功";
        setLoading(false);
        if (resp.RetCode === 0) {
          setAbnormalList(resp.BatchList);
          return;
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        setLoading(false);
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        setAbnormalList([]);
        return;
      });
  };
  const handleTableChange = (pagination) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total,
    });
  };
  const renderAdvancedForm = () => {
    return (
      <Form layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144444}>
            <FormItem label="批次ID">
              <Input
                // style={{ width: "100%" }}
                value={BatchId}
                placeholder="请输入对应的批次ID"
                onChange={(e) => {
                  setBatchId(e.target.value);
                }}
              />
            </FormItem>
          </Col>
          <Col span={12} key={1555}>
            <FormItem label="公司ID">
              <Input
                // style={{ width: "100%" }}
                value={CompanyId}
                placeholder="请输入对应的公司Id"
                onChange={(e) => {
                  setCompanyId(e.target.value);
                }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144664}>
            <FormItem label="时间">
              <RangePicker
                style={{ width: "100%" }}
                ranges={{
                  Today: [moment().startOf("day"), moment().endOf("day")],
                  "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                  ],
                }}
                showTime={{
                  defaultValue: [
                    moment("00:00:00", "HH:mm:ss"),
                    moment("23:59:59", "HH:mm:ss"),
                  ],
                }}
                format="YYYY-MM-DD"
                value={[BeginTime, EndTime]}
                onChange={(v) => {
                  if (v.length === 0) {
                    setBeginTime(null);
                    setEndTime(null);
                    return;
                  }
                  //选择同一天时，默认设置为0点-23点59
                  if (v[0].unix() === v[1].unix()) {
                    v[0] = v[0].startOf("day");
                    v[1] = v[1].endOf("day");
                  }
                  setBeginTime(v[0]);
                  setEndTime(v[1]);
                }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={2}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                style={{ marginRight: "16px" }}
                onClick={onSearch}
                htmlType="submit"
              >
                查询
              </Button>
              <Button onClick={reSetField}>重置</Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  };
  const reSetField = () => {
    setBatchId("");
    setCompanyId("");
    setBeginTime("");
    setEndTime("");
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0,
    });
  };
  useEffect(() => {
    GetRiskBatchList();
  }, [pagination]);
  const columns = [
    { key: "1", title: "批次ID", dataIndex: "BatchId" },
    { key: "2", title: "批次名称", dataIndex: "BatchName" },
    {
      key: "3",
      title: "创建时间",
      dataIndex: "CreatedTime",
      render: (val) => (
        <span>{moment(val * 1000).format("YYYY-MM-DD HH:mm:ss")}</span>
      ),
    },
    {
      key: "4",
      title: "批次详情",
      dataIndex: "AuthType",
      render: (val, row) => {
        let urlAddress = `/RiskWarning/PayAbnormalDetails?params=${encodeURI(
          JSON.stringify({ BatchId: row.BatchId })
        )}`;
        return (
          <Fragment>
            {
              <Link to={urlAddress} target="_blank">
                查看
              </Link>
            }
          </Fragment>
        );
      },
    },
    {
      key: "5",
      title: "导出Excel",
      render: (val, row) => {
        return (
          <Fragment>
            <Button
              type="download"
              data-key={row.BatchId}
              onClick={() => {
                download(row);
              }}
            >
              下载
            </Button>
          </Fragment>
        );
      },
    },
  ];
  return (
    <>
      <Card title="搜索" style={{ marginBottom: 24 }} bordered={false}>
        {renderAdvancedForm()}
      </Card>
      <Card title="结果" style={{ marginBottom: 24 }} bordered={false}>
        <Table
          loading={loading}
          dataSource={abnormalList}
          columns={columns}
          pagination={pagination}
          onChange={handleTableChange}
          rowKey={(record, index) => index}
        />
      </Card>
    </>
  );
};

export default PaymentAbnormal;
