import React, { Component, Fragment } from 'react';
import { Card,Form, Table, Tooltip,Tabs,notification} from 'antd';
import './index.css';
import { Link } from 'react-router-dom';
import { SensitiveApi } from '../../utils/request';
import { Breadcrumb } from 'antd';
import moment from 'moment';

const { TabPane } = Tabs;

//状态枚举,将英文状态转成中文在前端显示
class RiskWarningDetails extends Component {
    constructor(props) {
        super(props)
        let params = decodeURI(this.props.location.search.split("params=")[1])
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            BatchId: JSON.parse(params).BatchId,
            BatchType:JSON.parse(params).BatchType,
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
        };

    }
    // 挂载前查询
    componentDidMount() {
        this.GetRiskRecordList()
    }
    GetRiskRecordList = ()=> {
        let {BatchId,BatchType,pagination} = this.state
        let self = this,
            action = 'GetRiskRecordList',
            options = {
                Action: action,
                BatchId: BatchId,
                BatchType:BatchType,
                Limit: pagination.pageSize,
                Offset: pagination.pageSize * (pagination.current - 1)
            }
        self.setState({ loading: true })
        SensitiveApi(action, options)
            .then(resp => {
                self.setState({
                    loading: false,
                })
                let message = '查询成功'
                if (resp.RetCode === 0) {
                        let { current, pageSize } = this.state.pagination
                        self.setState({
                            list: resp.RecordList,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.Count,
                            }
                        }, () => {
                            console.log(this.state.Count)
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
        })
    }

    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.GetRiskRecordList()
        })
    }
    render() {
        let { loading, list, pagination } = this.state
        pagination = {
            ...pagination,
            showQuickJumper: true,
            showSizeChanger: true,
        }
        const columns1 = [
            {
                title: '公司Id',
                dataIndex: 'CompanyId',
            },
            {
                title: '公司名/用户名',
                dataIndex: 'CompanyName',
                render(val){
                    return (
                        <Fragment>
                          <Tooltip title={val} >
                            <span>{val.length>50?val.substring(0,50) + '...':val}</span>
                          </Tooltip>
                        </Fragment>
                      )
                }
            },
            {
                title: '登陆IP',
                dataIndex: 'AccessIp',
            },
            {
                title: '登陆国家',
                dataIndex: 'AccessCountryName',
            },
            {
                title: '登陆区域/省份',
                dataIndex: 'AccessRegionName',
            },
            {
                title: '登陆城市',
                dataIndex: 'AccessCityName',
            },
            {
                title: '登陆时间',
                dataIndex: 'AccessTime',
                render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
            },
            {
                title: '公司详情',
                render: (val, row) => {
                    let urlAddress = `/RiskWarning/RiskWarningDetails/RiskWarningApplyPage?params=${encodeURI(JSON.stringify({CompanyId:row.CompanyId,RiskId:row.RiskId}))}`
                    return (
                      <Fragment>
                        {
                         row.Message ? <Tooltip title={row.Message}><span style={{color:"red"}}>查看</span></Tooltip> : <Link to={urlAddress}>查看</Link>
                        }
                      </Fragment>
                    )
                },
            }
        ];
        return (
            <Card bordered={true}>
              <Breadcrumb style={{ marginBottom: 30 }}>
                <Breadcrumb.Item>
                  <a
                    href=""
                    onClick={() => {
                      this.props.history.push("/RiskWarning");
                      sessionStorage.setItem("RiskWarningTabKey", 1);
                    }}
                  >
                    控制台异常登录
                  </a>
                </Breadcrumb.Item>
                <Breadcrumb.Item>批次详情</Breadcrumb.Item>
              </Breadcrumb>
              <div >
                  <Tabs defaultActiveKey="TaskList">
                      <TabPane tab="批次详情" key="TaskList">
                          <Table
                              loading={loading}
                              rowKey={record => record.CompanyId}
                              dataSource={list}
                              columns={columns1}
                              pagination={pagination}
                              onChange={this.handleTableChange}
                          />
                      </TabPane>
                  </Tabs>
              </div>
            </Card>
        )
    }
}

const RiskWarningDetailsPage = Form.create()(RiskWarningDetails);
export default RiskWarningDetailsPage;
