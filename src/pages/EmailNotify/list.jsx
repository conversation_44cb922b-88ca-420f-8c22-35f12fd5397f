import React, { Component } from 'react'
import { <PERSON><PERSON>, Card, Table, Icon, message, Popconfirm, Dropdown, Menu } from 'antd'
import { Link } from 'react-router-dom'
import WrappedSearchForm from './form'
import { mailStatusDict, mailStatusIcon } from '../../utils/config'
import request from '../../utils/request'
import moment from 'moment'
import "./index.css"


class EmailNotify extends Component {
  constructor(props) {
    super(props)
    this.state = {
      list: [], // 列表初始值
      selectedIds: [],
      formValues: {},
    }
  }
  // 挂载前查询
  componentDidMount() {
    this.props.getEmailNotifyType()
    let { pagination } = this.props
    this.props.getEmailNotifyList({
      Limit: pagination.pageSize  || 10,
      Offset: (pagination.current - 1) * pagination.pageSize || 0,
    })
  }
  //查询列表
  search = (params) => {
    let { pagination } = this.props
    let { pageSize } = pagination
    this.setState({
      formValues: params
    }, () => {
      this.props.getEmailNotifyList({
        Limit: pageSize,
        Offset: 0,
        //Offset: (current - 1) * pageSize,
        ...params
      })
    })
  }
  //切换分页
  handleTableChange = (pagination, filtersArg, sorter) => {
    console.log('pagination, filtersArg, sorter', pagination, filtersArg, sorter)
    let { formValues } = this.state
    let newPage = {
      ...this.props.pagination,
      ...pagination
    }
    let OrderBy = ""
    if (sorter.field) {
      OrderBy = `${sorter.field} ${sorter.order ? sorter.order.replace("end", "") : 'asc'}`
    }
    if (filtersArg.Type && filtersArg.Type.length > 0) {
      formValues = {
        ...formValues,
        Type: filtersArg.Type[0]
      }
    }
    this.props.setPagination(newPage)
    this.props.getEmailNotifyList({
      Limit: newPage.pageSize,
      Offset: (newPage.current - 1) * newPage.pageSize,
      ...formValues,
      OrderBy
    })
  }
  //批量回复
  batchSend = () => {
    if (Array.isArray(this.state.selectedIds) && this.state.selectedIds.length <= 0) {
      return message.error('你还没选中要处理的邮件哦')
    }
    request('SendMailBatch', { Id: this.state.selectedIds })
      .then(() => {
        message.success('成功！已发起批量发送')
        let { formValues } = this.state
        let { pagination } = this.props
        this.props.getEmailNotifyList({
          Limit: pagination.pageSize,
          Offset: (pagination.current - 1) * pagination.pageSize,
          ...formValues
        })
        this.setState({
          selectedIds: []
        })
      }).catch(e => {
        message.error(e.message || '发起批量发送失败...')
      })
  }
  //批量完成
  batchFinish = () => {
    if (Array.isArray(this.state.selectedIds) && this.state.selectedIds.length <= 0) {
      return message.error('你还没选中要处理的邮件哦')
    }
    request('UpdateMailBatch', { Id: this.state.selectedIds, Status: 6 })
      .then(() => {
        message.success('成功啦')
        let { formValues } = this.state
        let { pagination } = this.props
        this.props.getEmailNotifyList({
          Limit: pagination.pageSize,
          Offset: (pagination.current - 1) * pagination.pageSize,
          ...formValues
        })
        this.setState({
          selectedIds: []
        })
      }).catch(e => {
        message.error(e.message || '批量完成失败...')
      })
  }
  //批量删除
  batchDel = () => {
    if (Array.isArray(this.state.selectedIds) && this.state.selectedIds.length <= 0) {
      return message.error('你还没选中要处理的邮件哦')
    }
    //todo 提示可能会有流程中的记录，系统会自动忽略
    request('UpdateMailBatch', { Id: this.state.selectedIds, Del: 1 })
      .then(() => {
        message.success('成功啦')
        let { formValues } = this.state
        let { pagination } = this.props
        this.props.getEmailNotifyList({
          Limit: pagination.pageSize,
          Offset: (pagination.current - 1) * pagination.pageSize,
          ...formValues
        })
        this.setState({
          selectedIds: []
        })
      }).catch(e => {
        message.error(e.message || '批量删除失败...')
      })
  }
  updateStatus = (id,type)=>{
    request('UpdateMailBatch', { Id:[id], Type:parseInt(type) })
    .then(res => {
      if(res.RetCode === 0){
        message.success('成功啦')
        setTimeout(()=>{
          window.location.reload()
        },5)
      }
    }).catch(e => {
      message.error(e.message || '失败...')
    })
  }
  render() {
    let { types, list, loading, pagination } = this.props
    console.log('types', types)
    const columns = [{
      title: '发件人',
      dataIndex: 'From',
      key: 'From',
      width: '15%',
      sorter: true,
    }, {
      title: '标题',
      dataIndex: 'Subject',
      key: 'Subject',
      width: '40%',
      sorter: true,
      render: (value, row) => {
        if (!row.IsRead) {
          return <span style={{ fontWeight: "bolder" }}>{value && value.length > 70 ? value.substr(0, 70) + '...' : value}</span>
        }
        return <span>{value && value.length > 70 ? value.substr(0, 70) + '...' : value}</span>
      }
    }, {
      title: '日期',
      dataIndex: 'CreateTime',
      key: 'CreateTime',
      width: '15%',
      render: (val) => val ? moment(val * 1000).format('YYYY-MM-DD HH:mm:ss') : val
    }, {
        title: '类别',
        dataIndex: 'Type',
        key: 'Type',
        width: 100,
        render: (val,row) => {
          const menu = types.map(item => {
            return <Menu.Item key={`${item.key}`} value={item.value} onClick={(e) => {
              this.setState({
                  Type: e.key,
              },()=>{
                  console.log(e)
                  this.updateStatus(row.Id,e.key)
              })
          }} >
                {item.value}
              </Menu.Item>
          })
          const list = <Menu>{menu}</Menu>
          return <Dropdown overlay={list} trigger={['click']}>
            <a className="ant-dropdown-link" >
                 {val}
            </a>
          </Dropdown>
        }
      }, {
      title: '状态',
      dataIndex: 'Status',
      key: 'Status',
      width: 150,
      render: (val) => mailStatusDict[val]
    }, {
      title: '渠道',
      dataIndex: 'Channel',
      key: 'Channel',
      width: 100
    }, {
      title: '邮件详情',
      dataIndex: 'detail',
      width: 100,
      key: 'detail',
      render: (v, row) => <Link to={{
        pathname: "/emailNotify/detail?Id=" + row.Id,
        state: row,
      }} target={"_blank"}>查看</Link>
    }, {
      title: 'BU',
      width: 80,
      dataIndex: 'BU',
      key: 'BU',
      render: (val) => {
        if (val && Array.isArray(val) && val.length > 0) {
          let obj = mailStatusIcon[val[0].EmailStatus]
          return <Icon type={obj.type} theme="outlined" twoToneColor={obj.twoToneColor} />
        }
        return '无'
      }
    }, {
      title: '客户',
      width: 80,
      dataIndex: 'CUSTOMER',
      key: 'CUSTOMER',
      render: (val) => {
        if (val && Array.isArray(val) && val.length > 0) {
          let obj = mailStatusIcon[val[0].EmailStatus]
          return <Icon type={obj.type} theme="outlined" twoToneColor={obj.twoToneColor} />
        }
        return '无'
      }
    }, {
      title: '运营商',
      width: 80,
      dataIndex: 'ISP',
      key: 'ISP',
      render: (val) => {
        if (val && Array.isArray(val) && val.length > 0) {
          let obj = mailStatusIcon[val[0].EmailStatus]
          return <Icon type={obj.type} theme="outlined" twoToneColor={obj.twoToneColor} />
        }
        return '无'
      }
    }]
    const rowSelection = {
      selectedRowKeys: this.state.selectedIds,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({
          selectedRows,
          selectedIds: selectedRows.map(row => row.Id)
        })
      },
      getCheckboxProps: record => ({
        disabled: record.name === 'Disabled User', // Column configuration not to be checked
        name: record.name,
      }),
    }
    pagination = {
      ...pagination,
      showSizeChanger: true,
      showQuickJumper: true
    }
    return (
      <div>
        <Card bordered={false} style={{ paddingRight: 15 }}>
          <WrappedSearchForm search={this.search} types={types} />
        </Card>
        <Card bordered={false} style={{ marginTop: 15, paddingRight: 15 }}>
          <div className="table-edit">
            <Button type="primary" onClick={this.batchSend}>批量回复</Button>
            <Popconfirm placement="top" title={'确定要批量完成吗？'} onConfirm={this.batchFinish} okText="Yes" cancelText="No">
              <Button type="primary">批量完成</Button>
            </Popconfirm>
            <Popconfirm placement="top" title={'已选中的记录中，如果存在发送中的记录，会自动忽略。'} onConfirm={this.batchDel} okText="Yes" cancelText="No">
              <Button type="danger">批量删除</Button>
            </Popconfirm>
            <Button onClick={this.batchSend}>重新发送</Button>
          </div>
          <Table
            // 传入 fetchInfo供子组件在发送消息后刷新用
            rowKey={(row) => row.Id}
            rowSelection={rowSelection}
            loading={loading}
            scroll={{ x: 1200 }}
            columns={columns}
            dataSource={list}
            onChange={this.handleTableChange}
            pagination={pagination}
          />
        </Card>
      </div>
    )
  }
}


export default EmailNotify
