import { Upload, Button, Icon } from 'antd';
import React from 'react'
import request from '../../utils/request'

export default function AttachFile ({value = [], changeAttachFile}) {
console.log("AttachFile -> fileList", value)

    const onRemove = (file) => {
        console.log("onRemove -> file", file)
        changeAttachFile('remove', file)
    }
    const upload = (file) => {
        // console.log("upload -> file", fileList)
        //FileReader可直接将上传文件转化为二进制流
        let reader = new FileReader();
        reader.readAsDataURL(file);
        console.log("upload -> file", file.name)
        reader.onloadend = function(){//完成后this.result为二进制流
            // console.log()
            let fileBase64 = reader.result
            const param = { File: fileBase64 }
            request('UploadFile', param)
            .then(result => {
                //todo 返回文件名
                if (result.FileInfo) {
                    let fileInfo = result.FileInfo[0]
                    // fileList.push()
                    changeAttachFile('add', {
                        ...fileInfo,
                        FileName: file.name,
                        Index: value.length
                    })
                }
            }).catch(()=> {
                // fileList.push({
                //     uid: fileList.length,
                //     name: file.name,
                //     status: 'error',
                //     // url: ''
                // })
            })
        }
        return false
    }
    const props2 = {
        // action: url + 'Action = UploadFile',
        fileList: value.map((file, index) => ({
            uid: file.FileId,
            name: file.FileName,
            status: 'done',
            index,
            url: 'http://172.18.183.71:9600/image/'+file.FileId
        })),
        className: 'upload-list-inline',
        onRemove: onRemove
    };
    return (
        <div>
            <Upload {...props2} beforeUpload={upload}>
                <Button>
                    <Icon type="upload" /> 添加附件
                </Button>
            </Upload>
        </div>
    )
}