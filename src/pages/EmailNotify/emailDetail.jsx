import React, { Component } from 'react'
import BraftEditor from 'braft-editor'
import  TableEdit  from 'braft-extensions/dist/table';
import 'braft-extensions/dist/table.css';
// import { ContentUtils } from 'braft-utils'
import { Link } from 'react-router-dom';
import moment from 'moment'
import { Tabs, Card, Form, Input, Button, Select, Breadcrumb,notification, Drawer, InputNumber, message } from 'antd'
import UploadAttachFile from './uploadAttachFile'
import gethtml from '../../utils/gethtml'
import 'braft-editor/dist/index.css'
import request from '../../utils/request'
import api from '../../utils/request'
import "./index.css"
const { Option } = Select;
const imgPre = "http://172.18.183.71:9600/image/"

const { TabPane } = Tabs;
const FormItem = Form.Item
BraftEditor.use(TableEdit({defaultColumns:3,defaultRows:3,withDropdown:true,columnResizable:true}))
class EmailDetail extends Component {
  constructor(props){
    super(props)
    this.state = {
      content: BraftEditor.createEditorState('<p>你好，<b>世界!</b><p>'), // 设置编辑器初始内容
      visible: false,
      key: 'BU',
      attachMent: [],
      template:[],//模版选择列表
      //outputHTML: '<p></p>'
    }
  }
  

  componentDidMount() {
    let Id = parseInt(this.props.location.search.split('Id=')[1]);
    this.isLivinig = true;
    request("GetEmailNotifyList", {Id:Id})
		.then(res=>{
      const data = res.RecordList[0]
      this.setState({
        detail: data
      }, () => {
        // 3秒后更改编辑器内容
        setTimeout(() => this.setEditorContentAsync('BU'), 1000)
      })
      //调用接口，将该条记录标记为“已读”
      if (!data.IsRead) {
        request('UpdateMailBatch', { Id: [Id] })
          .catch(e => {
            console.log(e.message || '已读标记失败')
          })
      }
    })
		.catch(err => {
			// 报错
			notification.error({
				message: "获取待处理邮件记录失败",
				description: err.message || "内部错误"
			})
		})
    this.getTemplate()
  }
  
  componentWillUnmount() {
    this.isLivinig = false
  }

  handleChange = (editorState) => {
    this.setState({
      content: editorState,
      //outputHTML: editorState.toHTML()
    },()=>{
      console.log(this.state.content)
    })
  }

  // 切换邮件模版
  setEditorContentAsync = (key) => {
    const { detail = {} } = this.state
    let formValues = {}
    if (detail[key]) {
      detail[key].forEach(item => {
          formValues = {
            Id: item.Id,
            Text: BraftEditor.createEditorState(item.Template||'<p>尊敬的UClouder</p>'),
            Email: item.Email || (key==='ISP'?detail.From:""),
            ChannelId: detail.ChannelId,
            Template:"",
            CC: item.CC || (key==='ISP'?detail.CC:""),
            Subject: 'Re: ' + detail.Subject,
            Attachment: Array.isArray(item.Attachment) ? item.Attachment.map((attach,index)=>({...attach,Index:index})) : []
          }
          this.setState({
            attachMent: formValues.Attachment
          })
          //附件
      })
    } else {
      formValues = {
        Text: BraftEditor.createEditorState('<p>尊敬的UClouder</p>'),
        Email: (key==='ISP'?detail.From:undefined),
        CC: (key==='ISP'?detail.CC:undefined),
        Template:"",
        Subject: 'Re: ' + detail.Subject,
        Attachment: []
      }
      this.setState({
        attachMent: []
      })
    }
    this.isLivinig && this.props.form.setFieldsValue(formValues)
  }
  //切换tab
  callback = (key) => {
    this.setState({
      key
    })
  }
  //发送邮件
  handleSubmit = (event) => {
    event.preventDefault()
    this.sendMail("SendMail")

  }

  sendMail = (action) => {
    this.props.form.validateFields((error, values) => {
      if (!error) {
        const { detail = {}, key } = this.state
        const submitData = {
          NotifyType: key,
          Email: Array.isArray(values.Email) ? values.Email.join(";") : values.Email,
          CC: Array.isArray(values.CC) ? values.CC.join(";") : values.CC,
          Text: gethtml(values.Text.toHTML(), detail),
          Template: values.Text.toHTML(),
          Id: values.Id,
          Subject: values.Subject,
          Channel: values.ChannelId,
          //todo 附件
          Attachment: this.state.attachMent
        }
        //如果没有Id，则传入BatchId
        if (!submitData.Id) {
          submitData.BatchId = detail.Id
        }
        //todo 发送邮件
        request(action, submitData)
          .then(() => {
            message.success(action === 'SendMail' ? '发送成功' : '保存成功')
            //清空
            this.isLivinig && this.props.form.setFieldsValue({
              Text: BraftEditor.createEditorState('<p>尊敬的UClouder</p>'),
              Template:"",
              Email: undefined,
              CC: undefined,
              Attachment: []
            })
            this.onClose()
            this.getNewDetail()
          }).catch(e => {
            message.error(e.message || action === 'SendMail' ? '发送失败' : '保存失败')
          })
      }
    })
  }
  //更新附件
  changeAttachFile = (type, file) => {
    let { attachMent } = this.state
    if (type === 'add') {
      attachMent.push(file)
    } else {
      attachMent.splice(file.index, 1)
    }
    this.setState({
      attachMent
    }, () => {
      this.props.form.setFieldsValue({
        Attachment: this.state.attachMent
      })
    })
  }
  //上传图片
  // uploadHandler = (param) => {
  //   if (!param.file) {
  //     return false
  //   }
  //   let form = this.props.form
  //   let content = form.getFieldValue('Text')
  //   var reader = new FileReader();  
  //     reader.readAsDataURL(param.file);//转化二进制流，异步方法
  //     reader.onloadend = () => {//完成后this.result为二进制流
  //         // console.log()
  //         request('UploadFile', {File: reader.result})
  //         .then(res => {
  //             //todo 返回文件名
  //             if (res.FileInfo) {
  //               let fileInfo = res.FileInfo[0]
  //                 //上传
  //                 this.isLivinig && form.setFieldsValue({
  //                   Text: ContentUtils.insertMedias(content, [{
  //                     type: 'IMAGE',
  //                     url: imgPre + fileInfo.FileId
  //                   }])
  //                 })
  //             }
  //         }).catch(e => {
  //           message.error(e.message || '插入图片失败')
  //         })
  //     }
  // }
  //更换邮件模版
  changeTemplate = () => {
    console.log(this.state.key,this.state.key === 'EMAIL')
    if (this.state.key === 'EMAIL') {
      return ;
    }
    this.setState({
      visible: true,
    }, () => {
      this.setEditorContentAsync(this.state.key)
    });
  }
  //新建邮件
  newMail = () => {
    this.setState({
      visible: true,
    }, () => {
      this.isLivinig && this.props.form.setFieldsValue({
        Text: BraftEditor.createEditorState('<p>尊敬的UClouder</p>'),
        Email: undefined,
        CC: undefined,
        Template:"",
        Subject: 'Re: ' + this.state.detail.Subject,
        Attachment: []
      })
    })
  }
  getNewDetail() {
    const { detail } = this.state
    request('GetEmailNotifyDetail', { BatchId: this.state.detail.Id })
      .then(res => {
        if (res.RecordList)
          this.setState({
            detail: {
              ...detail,
              ...res.RecordList
            }
          })
      })
  }
  //忽略邮件
  ignoreMail = () => {
    const { detail = {}, key } = this.state
    request('IgnoreMailSend', { BatchId: detail.Id, NotifyType: key })
      .then(() => {
        message.success('已忽略')
        this.getNewDetail()
      }).catch(e => {
        message.error(e.message || '操作失败')
      })
  }
  onClose = () => {
    this.setState({
      visible: false,
    });
  };
  getTemplate = ()=>{
    api('GetMailTempList').then(
      res => {
          if (res.RetCode === 0) {
              this.setState({
                  template: res.Rows,
              })
          } else {
              notification['error']({
                  message: 'GetMailTempList接口出错',
                  description: res.message || res.Message
              })
          }
      }
  )
      .catch(e => {
          console.log(e.message || 'GetMailTempList失败')
      })
  }
  handleTemplateChange = (value)=> {
    this.props.form.setFieldsValue({
      Text: BraftEditor.createEditorState(value)
    })
  }
  render() {
    //todo:附件上传，图片上传，邮件状态区分是已发送还是未发送。
    let { detail = {},template } = this.state
    const { getFieldDecorator } = this.props.form
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 3 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
    };
    // const extendControls = [
    //   {
    //     key: 'antd-uploader',
    //     type: 'component',
    //     component: (
    //       <Upload
    //         accept="image/*"
    //         showUploadList={false}
    //         customRequest={this.uploadHandler}
    //       >
    //         {/* 这里的按钮最好加上type="button"，以避免在表单容器中触发表单提交，用Antd的Button组件则无需如此 */}
    //         <button type="button" className="control-item button upload-button" data-title="插入图片">
    //           <Icon type="picture" theme="filled" />
    //         </button>
    //       </Upload>
    //     )
    //   }
    // ]
    return (
      <div>
        <Breadcrumb style={{ margin: '16px 0' }}>
          <Breadcrumb.Item><Link to='/emailNotify/'>邮件列表</Link></Breadcrumb.Item>
          <Breadcrumb.Item>邮件详情</Breadcrumb.Item>
        </Breadcrumb>
        <Card style={{ minHeight: 400 }}>
          <Tabs onChange={this.callback} type="card">
            <TabPane tab="邮件正文" key="EMAIL">
              <Card style={{ marginBottom: 15, maxHeight: '700px', overflowY: 'scroll' }}>
                <p><span style={{ fontSize: 16 }}><strong>发件人：</strong></span>{detail.From}</p>
                <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{detail.To}</p>
                <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{detail.CC}</p>
                <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{detail.Subject}</p>
                <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                <p dangerouslySetInnerHTML={{ __html: detail.Text }}></p>
                <p>附件</p>
                {
                  Array.isArray(detail.Attachment) ?
                    detail.Attachment.map((attach, index) => (<a rel="noreferrer" style={{display: 'block'}} key={index} target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                    : null
                }
              </Card>
            </TabPane>
            <TabPane tab="回复BU" key="BU">
              {
                Array.isArray(detail.BU) && detail.BU[0].EmailStatus === 6 ?
                null
                :
                <div>
                  <Button type="primary" style={{ marginLeft: 20 }} onClick={this.changeTemplate}>回复</Button>
                  <Button type="primary" style={{ marginLeft: 20 }} onClick={this.newMail}>新建</Button>
                  <Button type="default" style={{ marginLeft: 20 }} onClick={this.ignoreMail}>忽略</Button>
                </div>
              }
              {
                detail.BU ? detail.BU.map((reply, index) => reply.EmailStatus > 0 ? (
                  <section key={index} style={{ marginTop: 20 }}>
                    <h3>回复{index + 1}</h3>
                    <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{reply.Subject}</p>
                    <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{reply.Email}</p>
                    <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{reply.CC}</p>
                    <p><span style={{ fontSize: 16 }}><strong>时间：</strong></span>{reply ? moment(reply.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss') : "无"}</p>
                    <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                    <p dangerouslySetInnerHTML={{ __html: reply.Text }}></p>
                    <p>附件</p>
                    {
                      Array.isArray(reply.Attachment) ?
                        reply.Attachment.map((attach, index) => (<a style={{display: 'block'}} rel="noreferrer" key={index} target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                        : null
                    }
                  </section>
                ) : null)
                  : null
              }
            </TabPane>
            <TabPane tab="回复客户" key="CUSTOMER">
              {
                Array.isArray(detail.CUSTOMER) && detail.CUSTOMER[0].EmailStatus === 6 ?
                  null
                  :
                  <div>
                    <Button type="primary" style={{ marginLeft: 20 }} onClick={this.changeTemplate}>回复</Button>
                    <Button type="primary" style={{ marginLeft: 20 }} onClick={this.newMail}>新建</Button>
                    <Button type="default" style={{ marginLeft: 20 }} onClick={this.ignoreMail}>忽略</Button>
                  </div>
              }
              {
                detail.CUSTOMER ? detail.CUSTOMER.map((reply, index) => reply.EmailStatus > 0 ? (
                  <section key={index} style={{ marginTop: 20 }}>
                    <h3>回复{index + 1}</h3>
                    <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{reply.Subject}</p>
                    <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{reply.Email}</p>
                    <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{reply.CC}</p>
                    <p><span style={{ fontSize: 16 }}><strong>时间：</strong></span>{reply ? moment(reply.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss') : "无"}</p>
                    <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>

                    <p dangerouslySetInnerHTML={{ __html: reply.Text }}></p>
                    <p>附件</p>
                    {
                      Array.isArray(reply.Attachment) ?
                        reply.Attachment.map((attach, index) => (<a style={{display: 'block'}} key={index} rel="noreferrer" target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                        : null
                    }
                  </section>
                ) : null)
                  : null
              }
            </TabPane>
            <TabPane tab="回复投诉方" key="ISP">
            {
                Array.isArray(detail.ISP) && detail.ISP[0].EmailStatus === 6 ?
                  null
                  :
                  <div>
                    <Button type="primary" style={{ marginLeft: 20 }} onClick={this.changeTemplate}>回复</Button>
                    <Button type="primary" style={{ marginLeft: 20 }} onClick={this.newMail}>新建</Button>
                    <Button type="default" style={{ marginLeft: 20 }} onClick={this.ignoreMail}>忽略</Button>
                  </div>
            }
              {
                detail.ISP ? detail.ISP.map((reply, index) => reply.EmailStatus > 0 ? (
                  <section key={index} style={{ marginTop: 20 }}>
                    <h3>回复{index + 1}</h3>
                    <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{reply.Subject}</p>
                    <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{reply.Email}</p>
                    <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{reply.CC}</p>
                    <p><span style={{ fontSize: 16 }}><strong>时间：</strong></span>{reply ? moment(reply.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss') : "无"}</p>
                    <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                    <p dangerouslySetInnerHTML={{ __html: reply.Text }}></p>
                    <p>附件</p>
                    {
                      Array.isArray(reply.Attachment) ?
                        reply.Attachment.map((attach, index) => (<a style={{display: 'block'}} key={index} rel="noreferrer" target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                        : null
                    }
                  </section>
                ) : null)
                  : null
              }
            </TabPane>
          </Tabs>
        </Card>
        <Drawer
          title="回复邮件"
          width={1000}
          onClose={this.onClose}
          visible={this.state.visible}
          bodyStyle={{ paddingBottom: 80 }}
        >
          <Form onSubmit={this.handleSubmit} style={{ padding: 30 }}>
            <FormItem label="渠道" style={{ display: 'none' }}>
              {getFieldDecorator('ChannelId', {})(
                <InputNumber />
              )}
            </FormItem>
            <FormItem label="ID" style={{ display: 'none' }}>
              {getFieldDecorator('Id', {})(
                <InputNumber />
              )}
            </FormItem>
            <FormItem {...formItemLayout} label="收件人">
              {getFieldDecorator('Email', {
                rules: [{
                  required: true,
                  message: '请输入收件人',
                }],
              })(
                <Select mode="tags" style={{ width: '100%' }} tokenSeparators={[';']} />
              )}
            </FormItem>
            <FormItem {...formItemLayout} label="抄送">
              {getFieldDecorator('CC', {
                // rules: [{
                // required: true,
                // message: '请输入抄送',
                // }],
              })(
                <Select mode="tags" style={{ width: '100%' }} tokenSeparators={[';']} />
              )}
            </FormItem>
            <FormItem {...formItemLayout} label="邮件标题">
              {getFieldDecorator('Subject', {
                rules: [{
                  required: true,
                  message: '请输入标题',
                }],
              })(
                <Input size="large" placeholder="请输入标题" />
              )}
            </FormItem>
            <FormItem {...formItemLayout} label="邮件模版" >
              {getFieldDecorator('Template', {
                rules: [{
                  // required: true,
                  // message: '选择邮件模版',
                }],
              })(
                <Select style={{ width: '100%' }} defaultValue="" onChange={this.handleTemplateChange}>
                  {
                    template.map(item=>{
                       return <Option key={item.Context} value={item.Context}>{item.Name}</Option>
                    })
                  }
                </Select>
              )}
            </FormItem>
            <FormItem {...formItemLayout} label="附件">
              {getFieldDecorator('Attachment', {
                // validateTrigger: 'onBlur',
                // rules: [{
                // required: true
                // }],
              })(
                <UploadAttachFile changeAttachFile={this.changeAttachFile} />
              )}
            </FormItem>
            <FormItem {...formItemLayout} label="邮件正文">
              {getFieldDecorator('Text', {
                validateTrigger: 'onBlur',
                rules: [{
                  required: true
                }],
              })(
                <BraftEditor
                  style={{ border: '1px solid #ddd', borderRadius: '4px' }}
                  //value={content}
                  // extendControls={extendControls}
                  onChange={this.handleChange}
                />
              )}
            </FormItem>
            <FormItem style={{ textAlign: 'center' }}>
              <Button size="large" type="primary" htmlType="submit">发送</Button>
              <Button size="large" style={{ marginLeft: 20 }} onClick={() => this.sendMail('EditMail')}>保存</Button>
            </FormItem>
          </Form>
        </Drawer>
      </div>
    )
  }
}

export default Form.create()(EmailDetail)