import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom';
import { Tabs, Card ,Breadcrumb } from 'antd'
import "./index.css"
const imgPre = "http://172.18.183.71:9600/image/"
const { TabPane } = Tabs;

class EmailDetail extends Component {
    state = {
        visible: false
        // outputHTML: '<p></p>'
    }
    componentDidMount() {
        const data = this.props.location.state
        this.setState({
            detail: data
        })
    }
    //切换tab
    callback = (key) => {
        this.setState({
            key
        })
    }
    onClose = () => {
        this.setState({
            visible: false,
        });
    };

    render() {
        const { detail = {} } = this.state
        return (
            <div>
                <Breadcrumb style={{ margin: '16px 0' }}>
                    <Breadcrumb.Item><Link to='/emailNotify/history'>历史邮件列表</Link></Breadcrumb.Item>
                    <Breadcrumb.Item>邮件详情</Breadcrumb.Item>
                </Breadcrumb>
                <Card style={{ minHeight: 400 }}>
                    <Tabs onChange={this.callback} type="card">
                        <TabPane tab="邮件正文" key="EMAIL">
                            <Card style={{ marginBottom: 15, maxHeight: '700px', overflowY: 'scroll' }}>
                                <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{detail.Subject}</p>
                                <p><span style={{ fontSize: 16 }}><strong>发件人：</strong></span>{detail.From}</p>
                                <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{detail.To}</p>
                                <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{detail.CC}</p>
                                <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                                <p dangerouslySetInnerHTML={{ __html: detail.Text }}></p>
                                <p>附件</p>
                                {
                                Array.isArray(detail.Attachment) ?
                                    detail.Attachment.map((attach, index) => (<a rel="noreferrer" style={{display: 'block'}} key={index} target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                                    : null
                                }
                            </Card>
                        </TabPane>
                        <TabPane tab="回复BU" key="BU">
                            {
                                detail.BU ? detail.BU.map((reply, index) => (
                                    <section key={index} style={{ marginTop: 20 }}>
                                        <h3>回复{index + 1}</h3>
                                        <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{reply.Subject}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{reply.Email}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{reply.CC}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                                        <p dangerouslySetInnerHTML={{ __html: reply.Text }}></p>
                                        <p>附件</p>
                                        {
                                        Array.isArray(reply.Attachment) ?
                                            reply.Attachment.map((attach, index) => ( <a rel="noreferrer" style={{display: 'block'}} key={index} target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                                            : null
                                        }
                                    </section>
                                ))
                                    : null
                            }
                        </TabPane>
                        <TabPane tab="回复客户" key="CUSTOMER">
                            {
                                detail.CUSTOMER ? detail.CUSTOMER.map((reply, index) => (
                                    <section key={index} style={{ marginTop: 20 }}>
                                        <h3>回复{index + 1}</h3>
                                        <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{reply.Subject}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{reply.Email}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{reply.CC}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                                        <p dangerouslySetInnerHTML={{ __html: reply.Text }}></p>
                                        <p>附件</p>
                                        {
                                        Array.isArray(reply.Attachment) ?
                                            reply.Attachment.map((attach, index) => (<a rel="noreferrer" style={{display: 'block'}} key={index} target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                                            : null
                                        }
                                    </section>
                                ))
                                    : null
                            }
                        </TabPane>
                        <TabPane tab="回复投诉方" key="ISP">
                            {
                                detail.ISP ? detail.ISP.map((reply, index) => (
                                    <section key={index} style={{ marginTop: 20 }}>
                                        <h3>回复{index + 1}</h3>
                                        <p><span style={{ fontSize: 16 }}><strong>邮件标题：</strong></span>{reply.Subject}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>收件人：</strong></span>{reply.Email}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>抄送人：</strong></span>{reply.CC}</p>
                                        <p><span style={{ fontSize: 16 }}><strong>邮件正文：</strong></span></p>
                                        <p dangerouslySetInnerHTML={{ __html: reply.Text }}></p>
                                        <p>附件</p>
                                        {
                                        Array.isArray(reply.Attachment) ?
                                            reply.Attachment.map((attach, index) => (<a rel="noreferrer" style={{display: 'block'}} key={index} target="_blank" href={imgPre + attach.FileId}>{attach.FileName}</a>))
                                            : null
                                        }
                                    </section>
                                ))
                                    : null
                            }
                        </TabPane>
                    </Tabs>
                </Card>
            </div>
        )
    }
}

export default EmailDetail