import { connect } from "react-redux"
import List from "./list"
import { getEmailNotifyList, getEmailNotifyType,setPagination } from "../../actionCreator/emailNotify"

const mapDispatchToProps = (dispatch) => {
    return {
        setPagination: (data) =>  dispatch(setPagination("SET_PAGINATION",data)),
        // getList: (data) => dispatch(getList(data)),
        // setLoading: (data) => dispatch(setLoading("SET_LOADING",data)),
        getEmailNotifyList: (data) => dispatch(getEmailNotifyList(data)),
        getEmailNotifyType: () => dispatch(getEmailNotifyType())
        // setHistoryPagination: (data) => dispatch(setHistoryPagination("SET_HISTORY_PAGINATION"), data)
    }
  }
const mapStateToProps = ({emailNotifyReducer}) => {
    // console.log(state)
    return {
        ...emailNotifyReducer
    }
  }
  const SealBatchContiner = connect(mapStateToProps, mapDispatchToProps)(List)
  
  export default SealBatchContiner
  