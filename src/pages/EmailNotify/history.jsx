import React, { Component } from 'react'
import { Card, Table, Icon} from 'antd'
import { connect } from "react-redux"
import { getEmailNotifyHisotry, getEmailNotifyType } from "../../actionCreator/emailNotify"
import { Link } from 'react-router-dom'
import { mailStatusDict, mailStatusIcon } from '../../utils/config'
import moment from 'moment'
import WrappedSearchForm from './form'
import "./index.css"


class EmailNotify extends Component {
  constructor(props) {
    super(props)
    this.state = {
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 30,
        total: 0
      },
      loading: false
    }
  }

  componentDidMount() {
    this.props.getEmailNotifyType()
    let { pagination } = this.state
    this.props.getEmailNotifyHisotry({
      Limit: pagination.pageSize,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Status: 6,
    })
  }
  search = (params) => {
    let { pagination } = this.state
    let { current, pageSize } = pagination
    this.setState({
      formValues: params
    }, () => {
      this.props.getEmailNotifyHisotry({
        Limit: pageSize,
        //  todo 已完成状态
        Status: 6,
        Offset: (current - 1) * pageSize,
        ...params
      })
    })
  }
  handleTableChange = (curPagination) => {
    console.log("EmailNotify -> handleTableChange -> curPagination", curPagination)
    let { formValues, pagination } = this.state
    let newPage = {
      ...pagination,
      ...curPagination
    }
    this.setState({
      pagination: newPage
    }, () => {
      this.props.getEmailNotifyHisotry({
        Limit: newPage.pageSize,
        //  todo 已完成状态
        Status: 6,
        Offset: (newPage.current - 1) * newPage.pageSize,
        ...formValues
      })
    })
  }
  render() {
    const { types, history, historyTotal,loading } = this.props
    let { pagination } = this.state
    const columns = [{
      title: '标题',
      dataIndex: 'Subject',
      key: 'Subject',
      render: (value) => <span>{value && value.length > 30 ? value.substr(0,30)+'...' : value}</span>
    }, {
      title: '发件人',
      dataIndex: 'From',
      key: 'From',
    }, {
      title: '日期',
      dataIndex: 'CreateTime',
      key: 'CreateTime',
      render:(val) => val ? moment(val * 1000).format('YYYY-MM-DD HH:mm:ss') : val
    }, {
      title: '类别',
      dataIndex: 'Type',
      key: 'Type',
      width: 100,
      // render: (value) => {
      //     types ? types.map(data => {
      //     console.log("EmailNotify -> render -> data", data, value)
      //         if (data.key === value) {
      //             return data.value
      //         }
      //     }) : null
      // }
    }, {
      title: '状态',
      dataIndex: 'Status',
      key: 'Status',
      width: 100,
      render: (val) => mailStatusDict[val]
    }, {
      title: '邮件详情',
      dataIndex: 'detail',
      width: 100,
      key: 'detail',
      render: (v, row) => <Link to={{
        pathname: "/emailNotify/history/detail",
        state: row,
      }} target="_blank">查看详情</Link>
    }, {
      title: 'BU',
      width: 80,
      dataIndex: 'BU',
      key: 'BU',
      render: (val) => {
        if (val && Array.isArray(val) && val.length>0) {
          let obj = mailStatusIcon[val[0].EmailStatus]
          return  <div className={`icon-${obj.type}`}>
          <Icon type={obj.type}  twoToneColor={obj.twoToneColor}/>
          </div>

        }
        return '无'
      }
    }, {
      title: '客户',
      width: 80,
      dataIndex: 'CUSTOMER',
      key: 'CUSTOMER',
      render: (val) => {
        if (val && Array.isArray(val) && val.length>0) {
          let obj = mailStatusIcon[val[0].EmailStatus]
          return <div className={`icon-${obj.type}`}>
          <Icon type={obj.type} theme="outlined" twoToneColor={obj.twoToneColor}/>
          </div>
        }
        return '无'
      }
    }, {
      title: '运营商',
      width: 80,
      dataIndex: 'ISP',
      key: 'ISP',
      render: (val) => {
        if (val && Array.isArray(val) && val.length>0) {
          let obj = mailStatusIcon[val[0].EmailStatus]
          return <div className={`icon-${obj.type}`}>
            <Icon type={obj.type}  theme="outlined" twoToneColor={obj.twoToneColor} />
          </div>
        }
        return '无'
      }
    }]
    pagination = {
      ...pagination,
      showSizeChanger: true,
      showQuickJumper: true
    }
    return (
      <div>
        <Card bordered={false} style={{ paddingRight: 15 }}>
          <WrappedSearchForm search={this.search} types={types} showStatus={false} />
        </Card>
        <Card bordered={false} style={{ marginTop: 15, paddingRight: 15 }}>
          <Table
            // 传入 fetchInfo供子组件在发送消息后刷新用
            // rowSelection={rowSelection}
            loading={loading}
            columns={columns}
            dataSource={history}
            onChange={this.handleTableChange}
            pagination={{...pagination, total: historyTotal}}
          />
        </Card>
      </div>
    )
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    // setPagination: (data) => dispatch(setPagination("SET_PAGINATION", data)),
    // getList: (data) => dispatch(getList(data)),
    // setLoading: (data) => dispatch(setLoading("SET_LOADING",data)),
    getEmailNotifyHisotry: (data) => dispatch(getEmailNotifyHisotry(data)),
    getEmailNotifyType: () => dispatch(getEmailNotifyType())
    // setHistoryPagination: (data) => dispatch(setHistoryPagination("SET_HISTORY_PAGINATION"), data)
  }
}
const mapStateToProps = ({ emailNotifyReducer }) => {
  // console.log(state)
  return {
    ...emailNotifyReducer
  }
}
const EmailNotifyContiner = connect(mapStateToProps, mapDispatchToProps)(EmailNotify)

export default EmailNotifyContiner
