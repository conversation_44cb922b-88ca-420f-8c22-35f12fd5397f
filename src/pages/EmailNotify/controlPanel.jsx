import React, { Component, Fragment } from 'react';
import { Input, Table, Card, Form, Popconfirm, notification, message, But<PERSON>, Tabs, Modal, Tooltip } from 'antd';
import api from '../../utils/request'
import moment from 'moment';
const { TabPane } = Tabs;
const { TextArea } = Input;

const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',');
class List extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [],
            pagination: {
                current: 1,
                pageSize: 20,
                total: 0
            },
            Context: "",
            Name: "",
            visible: false,
            loading: false,
        };
    }
    // 查询列表
    fetch(options) {
        this.setState({
            loading: true
        })
        api('GetMailTempList', options).then(
            res => {
                if (res.RetCode === 0) {
                    this.setState({
                        loading: false,
                        list: res.Rows,
                        pagination: {
                            current: options.Offset / options.Limit + 1,
                            pageSize: options.Limit,
                            total: res.TotalCount
                        },
                    })
                } else {
                    notification['error']({
                        message: 'GetMailTempList接口出错',
                        description: res.message || res.Message
                    })
                }
            }
        )
            .catch(e => {
                console.log(e.message || 'GetMailTempList失败')
            })
    }

    // 挂载前查询
    componentDidMount() {
        let { pagination } = this.state
        let options = {
            Limit: pagination.pageSize,
            Offset: (pagination.current - 1) * pagination.pageSize
        }
        this.fetch(options)
    }

    // 处理分页
    handleTableChange = (pagination, filtersArg) => {
        const filters = Object.keys(filtersArg).reduce((obj, key) => {
            obj[key] = getValue(filtersArg[key]);
            return obj;
        }, {});

        const params = {
            Offset: (pagination.current - 1) * pagination.pageSize,
            Limit: pagination.pageSize,
            ...filters,
        };
        this.fetch(params)
    }
    addWords = () => {
        this.setState({
            visible: true
        })
    }
    delete = (Id) => {
        api('DeleteMailTemplate', { Id: Id }).then(
            res => {
                if (res.RetCode === 0) {
                    window.location.reload()
                }
            }
        ).catch(e => {
            console.log(e.message || 'DeleteMailTemplate失败')
            notification['error']({
                message: 'DeleteMailTemplate接口出错',
                description: e.message || e.Message
            })
        })

    }
    AddMailTemplate = () => {
        let { Name, Context } = this.state
        if (!Name || !Context) {
            message.error("信息填写尚未完成")
            return
        }
        let option = {
            Name: Name,
            Context: Context
        }
        this.setState({
            visible: false
        })
        api('AddMailTemplate', option).then(
            res => {
                if (res.RetCode === 0) {
                    window.location.reload()
                }
            }
        ).catch(e => {
            console.log(e.message || 'AddMailTemplate失败')
            notification['error']({
                message: 'AddMailTemplate接口出错',
                description: e.message || e.Message
            })
        })
    }
    render() {
        const columns = [{
            title: '模版名称',
            dataIndex: 'Name',
        }, {
            title: '内容',
            dataIndex: 'Context',
            render: (val) => <Tooltip title={val}><span>{val && val.length > 70 ? val.substring(0, 70) + '...' : val}</span></Tooltip>
        }, {
            title: '创建时间',
            dataIndex: 'CreateTime',
            render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
        }, {
            title: '创建人',
            dataIndex: 'Operator',
        }, {
            title: '操作',
            dataIndex: 'Id',
            render: (val) => {
                return (
                    <Fragment>
                        <Popconfirm title="是否确认删除任务？" onConfirm={() => this.delete(val)}>
                            <a href="#!">删除</a>
                        </Popconfirm>
                    </Fragment>
                )
            }
        }];

        let { loading, list, pagination, visible, Context, Name } = this.state
        pagination = {
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
        }
        return (
            <Card bordered={false}>
                <div>
                    <Tabs defaultActiveKey="GetWordTemplate">
                        <TabPane tab="话术模版" key="GetWordTemplate">
                            <Button type='primary' onClick={this.addWords} style={{ marginBottom: 20 }}>新增话术</Button>
                            <Table
                                loading={loading}
                                rowKey={record => record.Id}
                                dataSource={list}
                                columns={columns}
                                pagination={pagination}
                                onChange={this.handleTableChange}
                            />
                            <Modal
                                title="新增话术"
                                visible={visible}
                                onOk={this.AddMailTemplate}
                                onCancel={() => {
                                    this.setState({
                                        visible: false,
                                        Name: "",
                                        Context: ""
                                    })
                                }}
                            >
                                <Form.Item label="模版名称" name="模版名称" rules={[{ required: true, message: '请输入模版名称!' }]}>
                                    <Input style={{ width: '100%' }} value={Name} placeholder="请输入模版名称" onChange={(e) => { this.setState({ Name: e.target.value }) }} />
                                </Form.Item>
                                <Form.Item label="正文内容" name="正文内容" rules={[{ required: true, message: '请输入正文内容!' }]}>
                                    <TextArea rows={4} style={{ width: '100%', overflowY: "scroll" }} value={Context} placeholder="请输入正文内容" onChange={(e) => { this.setState({ Context: e.target.value }) }} />
                                </Form.Item>
                            </Modal>
                        </TabPane>
                        {/* <TabPane tab="数据统计" key="GetBackupTaskList">
                         </TabPane> */}
                    </Tabs>
                </div>
            </Card>
        )
    }
}

const ControlPanel = Form.create()(List);
export default ControlPanel;
