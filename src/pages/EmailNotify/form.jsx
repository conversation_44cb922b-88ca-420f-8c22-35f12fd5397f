import React, { Component } from 'react'
import { Form, Row, Col, Input, Button, Select, DatePicker, message } from 'antd';
import { mailStatusDict } from '../../utils/config'
import './form.css'

const { RangePicker } = DatePicker  

class SearchForm extends Component {
  state = {
    expand: false,
  };

  static defaultProps = {
    showStatus: true
  }

  handleSearch = e => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if(err) {
        message.error(err)
      }
      if (values.StartTime) {
        values.EndTime = parseInt(values.StartTime[1].format('X'), 10)
        values.StartTime = parseInt(values.StartTime[0].format('X'), 10)
      }
      this.props.search(values)
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
  };

  toggle = () => {
    const { expand } = this.state;
    this.setState({ expand: !expand });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { types, showStatus } = this.props
    // const dateFormat = 'YYYY/MM/DD';s
    return (
      <Form className="ant-advanced-search-form" onSubmit={this.handleSearch}>
        <Row gutter={24}>
            <Col span={8}>
                <Form.Item label='标题'>
                    {getFieldDecorator('Title', {})(
                      <Input placeholder="请输入邮件标题" />
                    )}
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item label='发件人'>
                    {getFieldDecorator('Sender', {
                    rules: [
                        {
                        // required: true,
                        message: 'Input something!',
                        },
                    ],
                    })(<Input placeholder="请输入发件人" />)}
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item label='日期'>
                    {getFieldDecorator('StartTime', {
                    })(<RangePicker format="YYYY-MM-DD" />)}
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item label='类别'>
                    {getFieldDecorator('Type', {})(
                    <Select placeholder="请选择">
                        {
                          types ? 
                          types.map(type => 
                            <Select.Option key={type.key} value={type.key}>{type.value}</Select.Option>
                          ) : null
                        }
                    </Select>)}
                </Form.Item>
            </Col>
            {
              showStatus ? 
              <Col span={8}>
                <Form.Item label='状态'>
                    {getFieldDecorator('Status', {
                    })(
                    <Select placeholder="请选择">
                        {
                          Object.keys(mailStatusDict).map(status =>
                            <Select.Option key={status} value={parseInt(status, 10)}>{mailStatusDict[status]}</Select.Option>
                          )
                        }
                    </Select>)}
                </Form.Item>
            </Col>
            : null
            }
            <Col span={8}>
                <Form.Item label='渠道'>
                    {getFieldDecorator('ChannelId', {})(
                    <Select placeholder="请选择">
                        <Select.Option value={1}>直客</Select.Option>
                        <Select.Option value={101}>浪潮</Select.Option>
                        <Select.Option value={0}>其他</Select.Option>
                    </Select>)}
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item label='IP'>
                    {getFieldDecorator('IP', {})(
                      <Input placeholder="请输入IP" />
                    )}
                </Form.Item>
            </Col>
        </Row>
        <Row>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={this.handleSearch}>
              查询
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={this.handleReset}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
    );
  }
}
const WrappedSearchForm = Form.create({ name: 'advanced_search' })(SearchForm);
export default WrappedSearchForm