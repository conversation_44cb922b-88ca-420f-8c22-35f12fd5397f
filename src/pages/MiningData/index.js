import React, { Component, Fragment} from 'react';
import { Card,Row,Form,Col,Input, Table, Button, Tabs, notification,DatePicker } from 'antd';
const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
import { MiningDataApi } from '../../utils/request';
import exportFile from '../../components/expoertFile/index';
import { Link } from 'react-router-dom';
import moment from 'moment';
import "./index.css"

class MiningData extends Component {
    constructor(props) {
        super(props)
        this.state = {
            list: [], // 列表初始值,
            loading: false,
            // batchID: "",
            // URL: "",
            // CreatedBegin: "",
            // CreatedEnd:"",
            // IP: "",
            pagination: {
                current: 1,
                pageSize: 20,
                total: 20
            },
        };
    }
    // 挂载前查询
    componentDidMount() {
        //此处调取api获取数据
        this.DescribeMiningCheckBatch()
    }
    handleTableChange = (pagination, filters, sorter) => {
        console.log(pagination, filters, sorter)
        this.setState({
            pagination: {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: this.state.pagination.total
            },
        }, () => {
            this.DescribeMiningCheckBatch()
        })
    }
    DescribeMiningCheckBatch = ()=> {
        let { pagination } = this.state
        let options = {
            Action :'DescribeMiningCheckBatch',
            Limit: pagination.pageSize,
            Offset: pagination.pageSize * (pagination.current - 1)
        }
        this.setState({ loading: true })
        MiningDataApi('DescribeMiningCheckBatch', options)
            .then(resp => {
                let message = '查询成功'
                this.setState({
                    loading: false,
                })
                if (resp.RetCode === 0) {
                        let { current, pageSize } = this.state.pagination
                        this.setState({
                            list: resp.BatchList,
                            pagination: {
                                current: current,
                                pageSize: pageSize,
                                total: resp.Count,
                            }
                        })
                        return
                } else {
                    message = resp.Message || resp.RetCode + "查询失败"
                }
                notification.open({
                    message: message,
                });
            }).catch(err => {
                // 报错
                notification['error']({
                    message: '请求失败',
                    description: err.message || '内部错误'
                })
                return;
            })
    }
    downloadData(Id) {
        console.log(Id,'Id')
        let self = this,
            action = 'DescribeMiningBatchDetails',
            options = {
                    Id,
            }
            MiningDataApi(action, options)
                .then(resp => {
                    let message = '下载成功'
                    self.setState({
                        loading: false,
                    })
                    if (resp.RetCode === 0) {
                        if (resp.Details) {
                            let dataList = resp.Details.reduce((prev, item) => {
                                prev.push([
                                    item.Proto || '',
                                    item.DestIp || '',
                                    item.DestPort || '',
                                    item.SrcIp || '',
                                    item.SrcPort || '',
                                    moment(item.Timestamp).format('YYYY-MM-DD HH:mm:ss') || '',
                                    item.Host || '',
                                    item.IsInnerAccount?"是":"否",
                                    item.IsInnerAccount?(item.SendEmail?"是":"否"):"-"
                                ]);
                                return prev;
                                }, [["协议","目标IP","目标端口","源IP", "源端口","请求时间","源IP归属地","是否是内部账号","是否发送成功"]]);
                                let culumnWidthArray = [8, 10, 8, 10, 8, 12,12];
                                let fileName = `${moment().format('YYYY-MM-DD HH:mm:ss')}挖矿数据${Id}.xlsx`;
                                exportFile(dataList, culumnWidthArray, fileName);
                        }
                    } else {
                        message = resp.Message || resp.RetCode + "下载失败"
                    }
                    notification.open({
                        message: message,
                    });
                }).catch(err => {
                    // 报错
                    notification['error']({
                        message: '请求失败',
                        description: err.message || '内部错误'
                    })
                    return;
                })
    }
    onSearch = () => {
        this.setState({
            pagination: {
                showQuickJumper: true,
                showSizeChanger: true,
                current: 1,
                pageSize: 20,
                total: 0
            },
        },()=>{
            this.DescribeMiningCheckBatch()
        })
    };
    //上部份，获取信息
    renderAdvancedForm() {
        return (
            <Form layout="inline" className="ant-advanced-search-form">
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144444}>
                        <FormItem label="批次ID">
                            <Input style={{ width: '100%' }} value={this.state.batchID} placeholder="请输入对应的批次ID" onChange={(e) => { this.setState({ batchID: e.target.value }) }} />
                        </FormItem>
                    </Col>
                    <Col span={10} key={1555}>
                        <FormItem label="源IP">
                            <Input style={{ width: '100%' }} value={this.state.URL} placeholder="请输入对应的源IP" onChange={(e) => { this.setState({ URL: e.target.value }) }} />
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={10} key={144664}>
                    <FormItem label="时间">
                    <RangePicker
                        style={{width:'100%'}}
                        ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
                        showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
                        format="YYYY-MM-DD"
                        onChange= {(v)=>{
                            this.setState({
                                CreatedBegin: moment(v[0]?.format("YYYY-MM-DD")).unix(),
                                CreatedEnd:moment(v[1]?.format("YYYY-MM-DD")).unix(),
                            },()=>{
                                console.log(moment(v[0]?.format("YYYY-MM-DD")).unix(),moment(v[1]?.format("YYYY-MM-DD")).unix())
                            })
                        }}
                    />
                    </FormItem>
                    </Col>
                </Row>
                <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
                    <Col span={6} key={2} >
                        <FormItem style={{ width: '100%', marginLeft: "80px" }} label="">
                            <Button style={{ marginRight: "16px" }} onClick={this.onSearch} htmlType="submit">查询</Button>
                            <Button onClick={() => {
                                window.location.reload();
                            }}>重置</Button>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        );
    }
    render() {
        let { loading, list, pagination  } = this.state;
        pagination = {
            ...pagination,
            showQuickJumper: true,
            showSizeChanger: true,
        }
        const columns1 = [
            {
                title: '批次ID',
                dataIndex: 'Id',
            },
            {
                title: '批次名称',
                dataIndex: 'IndexName',
            },
            {
                title: '创建时间',
                dataIndex: 'CreateTime',
                render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
            },
            {
                title: '批次详情',
                dataIndex: 'AuthType',
                render: (val, row) => {
                    let urlAddress = "/MiningData/MiningDataDetails/?Id=" + row.Id
                    return (
                      <Fragment>
                        {
                          <Link to={urlAddress} target='_blank'>查看</Link>
                        }
                      </Fragment>
                    )
                  },
            },
            {
                title: '导出Excel',
                render: (val, row) => {
                    let id = row.Id
                    return (
                        <Fragment>
                            <Button  type="download"  data-key = {id} onClick = {(e) => this.downloadData(e.target.dataset.key)}>下载</Button>
                        </Fragment>
                    )
                },
            },
        ];
        console.log(list,'list')

        //根据数据渲染页面
        return (
        <Card bordered={false}>
            <div>
                {/* <Card title="搜索" style={{marginBottom: 24 }} bordered={false} >
                    {this.renderAdvancedForm()}
                </Card> */}
                <Tabs defaultActiveKey="TaskList">
                    <TabPane tab="结果" key="TaskList">
                        <Table
                            loading={loading}
                            dataSource={list}
                            columns= {columns1}
                            pagination={pagination}
                            onChange={this.handleTableChange}
                        />
                    </TabPane>
                </Tabs>
            </div>
        </Card>)
    }
}
export default MiningData;
