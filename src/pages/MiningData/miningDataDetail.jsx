import React, { Component } from "react";
import { Card, Form, Table, Tabs, Input, notification, Tag, Icon, Tooltip} from "antd";
import "./index.css";
import { MiningDataApi } from "../../utils/request";
import { Breadcrumb } from "antd";
import moment from "moment";
const { TabPane } = Tabs;
const { Search } = Input;

class BatchDetailsPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], // 列表初始值,
      searchList: [], //列表搜索值
      loading: false,
      batchID: this.props.location.search.split("Id=")[1],
      Status: null,
      URL: null,
      Site: null,
      Type: 0,
      pagination: {
        showQuickJumper: true,
        showSizeChanger: true,
        current: 1,
        pageSize: 20,
        // [total]: 0
      },
    };
  }
  // 挂载前查询
  componentDidMount() {
    console.log(this.props, this.props.location.search.split("Id=")[1], "id");
    this.DescribeMiningBatchDetails();
  }

  DescribeMiningBatchDetails = () => {
    let self = this,
      action = "DescribeMiningBatchDetails",
      options = {
        Action: action,
        Id: this.state.batchID,
        Limit: this.state.pagination.pageSize,
        Offset:
          this.state.pagination.pageSize * (this.state.pagination.current - 1),
      };
    self.setState({ loading: true });
    MiningDataApi(action, options)
      .then((resp) => {
        let message = "查询成功";
        self.setState({
          loading: false,
        });
        if (resp.RetCode === 0) {
          let { current, pageSize } = this.state.pagination;
          self.setState({
            list: resp.Details,
            searchList: resp.Details,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: current,
              pageSize: pageSize,
              // total: resp.Count,
            },
          });
          return;
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
        }
        notification.open({
          message: message,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };

  onSearch = (value) => {
    console.log(value, "IP");
    this.setState(
      {
        IP: value.trim(),
        pagination: {
          showQuickJumper: true,
          showSizeChanger: true,
          current: 1,
          pageSize: 20,
          // total: 0
        },
      },
      () => {
        let { list, pagination, IP } = this.state;
        if (IP && IP !== "") {
          let searchList = list.filter((ls) => ls.SrcIp === IP);
          this.setState({
            searchList,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          });
        } else {
          this.setState({
            searchList: list,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          });
        }
      }
    );
  };

  handleTableChange = (pagination, filters, sorter) => {
    console.log(pagination, filters, sorter);
    this.setState({
      pagination: {
        current: pagination.current,
        pageSize: pagination.pageSize,
        // total: this.state.pagination.total
      },
    });
  };
  render() {
    let { loading, searchList, pagination } = this.state;
    const columns1 = [
      {
        title: "协议",
        dataIndex: "Proto",
      },
      {
        title: "目标IP",
        dataIndex: "DestIp",
      },
      {
        title: "目标端口",
        dataIndex: "DestPort",
      },
      {
        title: "源IP",
        dataIndex: "SrcIp",
        render: (val) => (
          <div>
            {val}
            {/* {record.SendEmail?<Tag color="#108ee9" style={{ marginLeft: "10px" }}>内部账号(已发邮件)</Tag>:""} */}
          </div>
        ),
      },
      {
        title: "源端口",
        dataIndex: "SrcPort",
      },
      {
        title: "请求时间",
        dataIndex: "Timestamp",
        render: (val) => (
          <span>{moment(val).format("YYYY-MM-DD HH:mm:ss")}</span>
        ),
      },
      {
        title: "源IP归属地",
        dataIndex: "Host",
      },
      {
        title: "是否内部账号",
        dataIndex: "IsInnerAccount",
        render: val=>{
          if(val){
            return <Tag color="#108ee9">是</Tag>
          }else{
            return <Tag color="#f50">否</Tag>
          }
        }
      },
      {
        title: (<span>是否发送成功 <Tooltip title="内部账号将自动发送邮件给安全"><Icon type="question-circle" style={{cursor: "pointer"}}/></Tooltip></span>),
        dataIndex: "SendEmail",
        render: (val,record)=>{
          if(!record.IsInnerAccount) return "-"
          if(val){
            return <Tag color="#108ee9">是</Tag>
          }else{
            return <Tag color="#f50">否</Tag>
          }
        }
      },
    ];
    return (
      <Card bordered={true}>
        <Search
          placeholder="源IP"
          onSearch={this.onSearch}
          style={{ width: 400, display: "float", float: "right" }}
        />
        <Breadcrumb style={{ marginBottom: 30 }}>
          <Breadcrumb.Item>
            <a href="/MiningData">挖矿数据</a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>批次详情</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <Tabs defaultActiveKey="TaskList">
            <TabPane tab="批次详情" key="TaskList">
              <Table
                loading={loading}
                rowKey={(record) => record.Id}
                dataSource={searchList}
                columns={columns1}
                pagination={pagination}
                onChange={this.handleTableChange}
              />
            </TabPane>
          </Tabs>
        </div>
      </Card>
    );
  }
}

const MiningDataDetails = Form.create()(BatchDetailsPage);
export default MiningDataDetails;
