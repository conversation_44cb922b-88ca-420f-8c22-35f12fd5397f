import React, { Component } from "react";
import {
  Card,
  Form,
  Input,
  Modal,
  message,
  Table,
  Button,
  Tabs,
  Icon,
  notification,
} from "antd";
const { Search } = Input;
const { TabPane } = Tabs;
const FormItem = Form.Item;
import { MiningDataApi } from "../../utils/request";
import exportFile from "../../components/expoertFile/index";
import ip from "ip";
import Upload from "rc-upload";
import csvtojson from "csvtojson";
import "./index.css";
class AddressManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], // 列表初始值,
      loading: false,
      IP: "",
      pagination: {
        current: 1,
        pageSize: 20,
        // total: 0
      },
    };
  }
  // 挂载前查询
  componentDidMount() {
    //此处调取api获取数据`
    this.DescribeMiningPool();
  }
  onSearch = (value) => {
    console.log(value, "IP");
    value = value.trim();
    this.setState(
      {
        IP: value,
        pagination: {
          showQuickJumper: true,
          showSizeChanger: true,
          current: 1,
          pageSize: 20,
          // total: 0
        },
      },
      () => {
        let { list, pagination, IP } = this.state;
        if (IP && IP !== "") {
          let searchList = list.filter((ls) => ls.IP === IP);
          this.setState({
            searchList,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          });
        } else {
          this.setState({
            searchList: list,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: pagination.current,
              pageSize: pagination.pageSize,
            },
          });
          // this.DescribeSensitiveWordBatch()
        }
      }
    );
  };
  AddMiningPoolConfig = () => {
    let { IPAddr, Port } = this.state;

    //校验
    if (!IPAddr || !Port || IPAddr === "" || Port === "") {
      message.error("IP或端口不能为空");
    } else if (
      !ip.isV4Format(IPAddr) ||
      parseInt(Port) < 0 ||
      parseInt(Port) > 65535
    ) {
      message.error("IP或端口格式不正确");
    } else {
      let self = this,
        action = "AddMiningPoolConfig",
        options = {
          Action: action,
          Config: { [IPAddr]: Port },
        };
      console.log(options.Config, "ADD");
      MiningDataApi(action, options)
        .then((resp) => {
          let message = "添加成功";
          if (resp.RetCode !== 0) {
            message = resp.Message || resp.RetCode + "添加失败";
            notification.open({
              message: message,
            });
          }
          self.setState({
            modal: false,
          });
          self.DescribeMiningPool();
        })
        .catch((err) => {
          // 报错
          notification["error"]({
            message: "请求失败",
            description: err.message || "内部错误",
          });
          return;
        });
    }
  };
  DescribeMiningPool = () => {
    this.setState({ loading: true });
    let self = this,
      action = "DescribeMiningPool",
      options = {};
    MiningDataApi(action, options)
      .then((resp) => {
        let message = "查询成功";
        if (resp.RetCode === 0) {
          let { current, pageSize } = this.state.pagination;
          let keys = Object.keys(resp.MiningPool);
          let list = keys.map((k) => {
            return { IP: k, Port: resp.MiningPool[k] };
          });
          self.setState({
            list,
            searchList: list,
            pagination: {
              showQuickJumper: true,
              showSizeChanger: true,
              current: current,
              pageSize: pageSize,
            },
          });
        } else {
          message = resp.Message || resp.RetCode + "查询失败";
          notification.open({
            message: message,
          });
        }
        self.setState({
          loading: false,
          modal: false,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  };
  downloadData() {
    let self = this
    let action = "DescribeMiningPool";
    let options = {};
    MiningDataApi(action, options)
      .then((resp) => {
        let message = "下载成功";
        if (resp.RetCode === 0) {
          let keys = Object.keys(resp.MiningPool);

          let dataList = keys.reduce(
            (prev, item) => {
              prev.push([item || "", resp.MiningPool[item] || ""]);
              return prev;
            },
            [["IP", "Port"]]
          );
          let culumnWidthArray = [30, 30];
          let fileName = "矿池地址.xlsx";
          exportFile(dataList, culumnWidthArray, fileName);
        } else {
          message = resp.Message || resp.RetCode + "下载失败";
          notification.open({
            message: message,
          });
        }
        self.setState({
          loading: false,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        return;
      });
  }
  uploadData(batchFile) {
    let self = this;
    //文件上传，使用FileReader读文件
    let reader = new FileReader();
    reader.readAsText(batchFile, "gbk");
    reader.onloadend = function () {
      csvtojson()
        .fromString(reader.result)
        .then((res) => {
          let Config = {};
          res.forEach((r) => {
            Config[r.IP] = r.Port;
          });
          console.log(Config, "JSON");
          let action = "AddMiningPoolConfig";
          let options = {
            Config,
          };
          MiningDataApi(action, options)
            .then((resp) => {
              let message = "上传成功";
              if (resp.RetCode === 0) {
                notification.open({
                  message: message,
                });
                self.DescribeMiningPool();
              } else {
                message = resp.Message || resp.RetCode + "上传失败";
                notification.open({
                  message: message,
                });
              }
            })
            .catch((err) => {
              // 报错
              notification["error"]({
                message: "请求失败",
                description: err.message || "内部错误",
              });
              return;
            });
        });
    };
    //Prevent file uploading
    return false;
  }
  handleTableChange = (pagination, filters, sorter) => {
    console.log(pagination, filters, sorter);
    this.setState(
      {
        pagination: {
          current: pagination.current,
          pageSize: pagination.pageSize,
        //   total: this.state.pagination.total,
        },
    });
  }
  render() {
    let { loading, list, searchList, pagination } = this.state;
    pagination = {
      ...pagination,
      showQuickJumper: true,
      showSizeChanger: true,
    };
    const columns1 = [
      {
        title: "IP",
        dataIndex: "IP",
      },
      {
        title: "端口号",
        dataIndex: "Port",
      },
      // {
      //     title: '创建时间',
      //     dataIndex: 'CreateTime',
      //     render: val => <span>{moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>,
      // },
      // {
      //     title: '操作人',
      //     dataIndex: 'Operator',
      // },
      // {
      //     title: '备注',
      //     render: (val, row, index) => {
      //         let id = row.Id
      //         return (
      //             <Fragment>
      //                 <Button  type="download"  data-key = {id} onClick = {(e) => this.download(e.target.dataset.key)}>下载</Button>
      //             </Fragment>
      //         )
      //     },
      // },
      // {
      //     title: '操作',
      //     dataIndex: 'AuthType',
      //     render: (val, row, index) => {
      //         let id = row.Id
      //         return (
      //             <Fragment>
      //                 <Button  type="delete"  data-key = {id} onClick = {(e) => this.download(e.target.dataset.key)}>删除</Button>
      //             </Fragment>
      //         )
      //     },
      // },
    ];
    console.log(list, "list");

    return (
      <Card bordered={false}>
        <div>
          <Tabs defaultActiveKey="TaskList">
            <TabPane tab="矿池IP操作" key="GetIPList">
              <Search
                placeholder="输入IP"
                onSearch={this.onSearch}
                onChange={this.changeSearch}
                style={{ width: 400, display: "float", float: "right" }}
              />
              <div
                className="table-edit"
                style={{ marginTop: 40, paddingRight: 15 }}
              >
                <Button type="download" onClick={() => this.downloadData()}>
                  <Icon type="download" />
                  全量导出
                </Button>
                <Upload
                  action="temp/"
                  listType="picture-card"
                  fileList={this.state.fileList}
                  beforeUpload={this.uploadData.bind(this)}
                >
                  <Button>
                    <Icon type="upload" /> 批量上传
                  </Button>
                </Upload>
                {/* <Button onClick = {(e) => this.uploadData()}>批量上传</Button> */}
                <Button
                  style={{ marginRight: "16px" }}
                  onClick={() => {
                    this.setState({
                      modal: true,
                    });
                  }}
                  type="primary"
                >
                  添加
                </Button>
              </div>
              <Table
                loading={loading}
                dataSource={searchList}
                columns={columns1}
                pagination={pagination}
                onChange={this.handleTableChange}
              />
            </TabPane>
            {/* <TabPane tab="历史记录" key="GetHistoryList">
                            <Table
                                loading={loading}
                                dataSource={list}
                                columns= {columns1}
                                pagination={pagination}
                                onChange={this.handleTableChange}
                            />
                        </TabPane> */}
          </Tabs>
        </div>
        <Modal
          title="添加矿池地址"
          visible={this.state.modal}
          onCancel={() => {
            this.setState({ modal: false });
          }}
          footer={null}
        >
          <Form
            layout="inline"
            className="ant-advanced-search-form"
            style={{ border: "0px solid #ffffff" }}
          >
            <FormItem label="IP地址" required>
              <Input
                placeholder=""
                onChange={(e) => {
                  this.setState({ IPAddr: e.target.value });
                }}
              />
            </FormItem>
            <FormItem label="端口号" required>
              <Input
                placeholder=""
                onChange={(e) => {
                  this.setState({ Port: e.target.value });
                }}
              />
            </FormItem>

            <FormItem style={{ width: "100%", marginLeft: "130px" }} label="">
              <Button
                style={{ marginRight: "20px" }}
                onClick={() => {
                  this.setState({ modal: false });
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                onClick={() => this.AddMiningPoolConfig()}
              >
                确定
              </Button>
            </FormItem>
          </Form>
        </Modal>
      </Card>
    );
  }
}
export default AddressManage;
