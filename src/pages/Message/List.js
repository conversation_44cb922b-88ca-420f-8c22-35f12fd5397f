import React, { Component } from "react";
import {
  message,
  Row,
  Col,
  DatePicker,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Select,
} from "antd";
import MessageTable from "../../components/MessageTable";
import ProgressSlider from "../../components/ProgressSlider";
import "./List.css";
import request from "../../utils/request";
import Upload from "rc-upload";
import moment from "moment";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const Option = Select.Option;
const getValue = (obj) =>
  Object.keys(obj)
    .map((key) => obj[key])
    .join(",");
let interval = null;
//上传的文件类型，与文件名称要对应
const uploadFileTypeMap = {
  0: "未备案",
  1: "未接入",
};
class List extends Component {
  state = {
    fileList: [],
    IsNotTransfer: "0",
  };
  // 查询列表
  fetch(options = {}) {
    const { getList, setLoading } = this.props;
    // 初始化options中的分页
    options.Offset = options.Offset || 0;
    options.Limit = options.Limit || 20;
    setLoading(true);
    getList(options);
  }

  // 挂载前查询
  componentDidMount() {
    const { formValues = {}, pagination = {} } = this.props;
    const time = formValues.BeginTime
      ? [moment(formValues.BeginTime * 1000), moment(formValues.EndTime * 1000)]
      : [];
    this.props.form.setFieldsValue({ ...formValues, time });
    this.fetch({
      ...formValues,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
    });
  }

  // 处理分页
  handleMessageTableChange = (pagination, filtersArg) => {
    const { formValues } = this.props;
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      const newObj = { ...obj };
      newObj[key] = getValue(filtersArg[key]);
      return newObj;
    }, {});

    const params = {
      ...formValues,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
      ...filters,
    };
    this.props.setPagination({ ...pagination });
    this.fetch(params);
  };
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (interval === null && nextProps.showAnalyProgress) {
      interval = setInterval(() => {
        console.log("轮训");
        this.props.getAnalyProgress({ Id: this.props.list[0].Id });
      }, 20000);
    }
  }
  handleLogoUpload(batchFile) {
    let self = this;
    let { IsNotTransfer } = self.state;
    //文件上传，使用FileReader读文件
    let reader = new FileReader();
    reader.readAsDataURL(batchFile);
    console.log("batchFile", batchFile, batchFile.name.indexOf(".csv"));
    if (batchFile && batchFile.name && batchFile.name.indexOf(".csv") === -1) {
      message.info("上传的文件格式需要.csv");
      return;
    }
    if (
      batchFile.name &&
      batchFile.name.indexOf(uploadFileTypeMap[IsNotTransfer]) === -1
    ) {
      message.info("上传的文件名称与通知类型不一致");
      return;
    }
    reader.onloadend = function () {
      //处理loadend事件。该事件在读取操作结束时（要么成功，要么失败）触发
      let fileBase64 = reader.result.split(",")[1];
      const param = {
        File: fileBase64,
        Description: batchFile.name,
        IsNotTransfer: parseInt(IsNotTransfer, 10),
      };
      // param.IsNotTransfer = parseInt(IsNotTransfer)
      // console.log(param)
      //reader.result中存放Base64编码,需要对原数据做处理，导入数据格式"data:text/csv;base64,aXAs5Z+f5ZCNLA0KMS4xLjEuMSxhdmkucWlhbmp1bnllLmNvbSwNCjIuMi4yLjIsYXYucWlhbmp1bnllLmNvbSwNCg=="
      request("CreateBatch", param)
        .then((resp) => {
          if (resp.RetCode === 0) {
            message.info("上传成功");
          } else {
            message.info("上传失败" + resp.Message);
          }
        })
        .then(self.fetch())
        .catch((err) => {
          // 报错
          message.info("上传失败", err);
        });
    };
    //Prevent file uploading
    return false;
  }
  // 重置搜索框
  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();
    // this.setState({
    //   formValues: {},
    // });
    this.props.setFormValues({});
    this.fetch();
  };

  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  };

  // 搜索
  handleSearch = (e) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
      };

      if (values.time && values.time[0]) {
        // 格式转换
        values.BeginTime = parseInt(values.time[0].format("X"), 10);
        values.EndTime = parseInt(values.time[1].format("X"), 10);

        // 查一天的时间实际压力从0点到23点，做startOf与endOf处理
        if (values.BeginTime === values.EndTime) {
          values.BeginTime = values.time[0].startOf("day").format("X");
          values.EndTime = values.time[1].endOf("day").format("X");
        }

        // delete values.time
      } else {
        delete values.time;
      }
      if (values.CompanyId) {
        values.CompanyId = parseInt(values.CompanyId, 10);
      } else {
        delete values.CompanyId;
      }
      if (values.Id) {
        values.Id = parseInt(values.Id, 10);
      } else {
        delete values.Id;
      }

      const { pagination, setFormValues } = this.props;
      setFormValues(values);
      // console.log(values)
      //去搜索条件中的前后空格， 没写
      this.fetch({
        Offset: (pagination.current - 1) * pagination.pageSize,
        Limit: pagination.pageSize,
        ...values,
      });
    });
  };
  //切换通知类型
  handleChangeNoticeType = (value) => {
    this.setState({ IsNotTransfer: value });
  };
  // 面包屑
  // breadcrumb() {
  //   return (
  //       <Breadcrumb>
  //         <Breadcrumb.Item><a href="/message">首页</a></Breadcrumb.Item>
  //       </Breadcrumb>
  //   );
  // }

  //复杂搜索框
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    return (
      <Form
        onSubmit={this.handleSearch}
        layout="inline"
        className="ant-advanced-search-form"
      >
        <Row gutter={{ md: 8, lg: 24 }}>
          <Col span={8} key={1} style={{ display: "block" }}>
            <FormItem label="批次ID">
              {getFieldDecorator("Id")(<Input style={{ width: "100%" }} />)}
            </FormItem>
          </Col>
          <Col span={8} key={2} style={{ display: "block" }}>
            <FormItem label="公司ID">
              {getFieldDecorator("CompanyId")(
                <Input style={{ width: "100%" }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={3} style={{ display: "block" }}>
            <FormItem label="IP">
              {getFieldDecorator("IP")(<Input style={{ width: "100%" }} />)}
            </FormItem>
          </Col>
          <Col span={8} key={4} style={{ display: "block" }}>
            <FormItem label="域名">
              {getFieldDecorator("Domain")(<Input style={{ width: "100%" }} />)}
            </FormItem>
          </Col>
          <Col span={8} key={5} style={{ display: "block" }}>
            <FormItem label="时间">
              {getFieldDecorator("time")(
                <RangePicker
                  style={{ width: "100%" }}
                  ranges={{
                    Today: [moment().startOf("day"), moment().endOf("day")],
                    "This Month": [
                      moment().startOf("month"),
                      moment().endOf("month"),
                    ],
                  }}
                  showTime={{
                    defaultValue: [
                      moment("00:00:00", "HH:mm:ss"),
                      moment("23:59:59", "HH:mm:ss"),
                    ],
                  }}
                  format="YYYY-MM-DD"
                />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={6} style={{ display: "block" }}>
            <FormItem label="通知类型">
              {getFieldDecorator("IsNotTransfer", {
                initialValue: "0",
              })(
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Select
                    style={{ width: "70%", paddingLeft: 2 }}
                    onChange={this.handleChangeNoticeType}
                  >
                    <Option value="0">未备案</Option>
                    <Option value="1">未接入</Option>
                  </Select>
                  <Upload
                    action="temp/"
                    listType="picture-card"
                    fileList={this.state.fileList}
                    beforeUpload={this.handleLogoUpload.bind(this)}
                  >
                    <Button
                      title={
                        "1.只支持csv格式文件上传，内容包括 IP、域名两列2.单次上传 csv 文件容量可达 2 万条"
                      }
                    >
                      <Icon type="upload" /> 上传
                    </Button>
                  </Upload>
                </div>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col
            md={8}
            offset={8}
            sm={24}
            style={{ display: "flex", justifyContent: "center" }}
          >
            <span style={{ marginBottom: 24 }}>
              <Button
                style={{ marginLeft: 8 }}
                htmlType="submit"
                type="primary"
              >
                查询
              </Button>
              <Button style={{ marginLeft: 8 }} onClick={this.handleFormReset}>
                重置
              </Button>
            </span>
          </Col>
        </Row>
      </Form>
    );
  }

  // 根据是否展开获取对应的表单形式
  renderForm() {
    return this.renderAdvancedForm();
  }
  componentWillUnmount() {
    console.log("清除");
    clearInterval(interval);
    interval = null;
  }
  render() {
    const {
      loading,
      pagination,
      list,
      showAnalyProgress,
      companyProgress = 0,
      registerProgress = 0,
    } = this.props;
    return (
      <Card bordered={false}>
        <div>
          {/* <Card bordered={false}>
            {this.breadcrumb()}
          </Card> */}

          <Card title="搜索" style={{ marginBottom: 24 }} bordered={false}>
            {this.renderForm()}
          </Card>
          {showAnalyProgress &&
          (companyProgress < 100 || registerProgress < 100) ? (
            <ProgressSlider
              companyProgress={companyProgress}
              registerProgress={registerProgress}
            />
          ) : null}
          <Card title="结果" style={{ marginBottom: 24 }} bordered={false}>
            <MessageTable
              loading={loading}
              data={list}
              pagination={pagination}
              onChange={this.handleMessageTableChange}
            />
          </Card>
        </div>
      </Card>
    );
  }
}

const ListForm = Form.create()(List);
export default ListForm;
