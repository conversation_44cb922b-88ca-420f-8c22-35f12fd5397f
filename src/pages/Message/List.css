.ant-advanced-search-form {
  padding: 24px;
  background: #fbfbfb;
  border: 1px solid #d9d9d9;
  border-radius: 0px;
	margin-bottom: 24px;
}

.ant-advanced-search-form .ant-form-item {
  display: flex;
}

.ant-advanced-search-form .ant-form-item-label {
  overflow: visible;
}

.ant-advanced-search-form .ant-form-item-control-wrapper {
	width: 80%;
}

.ant-advanced-search-form .ant-form-item-label {
	width: 20%;
}
.icon-wrapper {
  position: relative;
  padding: 0px 30px;
}

.icon-wrapper .anticon {
  position: absolute;
  top: -2px;
  width: 16px;
  height: 16px;
  line-height: 1;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.25);
}

.icon-wrapper .anticon:first-child {
  left: 0;
}

.icon-wrapper .anticon:last-child {
  right: 0;
}
.ant-slider-disabled .ant-slider-track {
  background-color: #1890ff!important;
}