import React, { Component } from 'react';
import { <PERSON> } from 'react-router-dom'
import { connect } from "react-redux"
import { getBatchList, setLoading, setBatchList, setPagination, getAnalyProgress } from "../../actionCreator/batchDetail"
import { Table, Breadcrumb,Select, notification, Input, Row, Col, Card, Form, Button, Popconfirm, Modal } from 'antd';
import BatchTable from '../../components/BatchTable'
import './List.css';
import request from '../../utils/request'
import ProgressSlider from '../../components/ProgressSlider';
const Search = Input.Search;
let interval = null
const { Option } = Select;
class BatchDetail extends Component {
  constructor(props){
    super(props)
    this.state = {
      // loading: false,
      batchId: parseInt(this.props.match.params.BatchId, 10),
      // abnormalRecord: [],
      BUNotificationInfo: [],
      search:"",
      // batchStatus: "",
      expandForm: false, // 是否展开
      // rawList: [], // 所有值
      // list: [], // 列表展示值
      // pagination: {
      //   current: 1,
      //   pageSize: 20,
      //   total: 0
      // },
    };
  }
  refresh = async () => {
    await this.fetch()
    const { buIsNotified,BUNotificationInfo } = this.props
    this.setState({
      buIsNotified,
      BUNotificationInfo
    })
  }
  handleChangeSelect =(e)=>{
    this.setState({
      search:e
    })
  }
  notify(options = {}) {
    // 批量发送信息
    request('NotifyBatch', options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '消息推送成功' : resp.Message || resp.RetCode + "发送失败"

        notification.open({
          message: message,
        });
      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }


  retryNotify(options = {}) {
    // 批量发送信息
    request('RetryBatch', options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '消息推送成功' : resp.Message || resp.RetCode + "发送失败"

        notification.open({
          message: message,
        });

      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }

  notifyBU = () => {
    // 通知BU
    request('NotifyBU', {BatchId: this.state.batchId})
      .then(resp => {
        let message = resp.RetCode === 0 ? '消息推送成功' : resp.Message || resp.RetCode + "发送失败"

        this.setState({
          buIsNotified: true
        })
        notification.open({
          message: message,
        });

      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }

  finishBatch(options = {}) {

    // 批量发送信息
    request('FinishBatch', options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '结束批次成功' : resp.Message || resp.RetCode + "结束批失败"

        notification.open({
          message: message,
        });
      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '结束失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }
  // 查询列表
  fetch = (options = {}) => {
    const { getList,setLoading } = this.props
    // 统一设置分页或者报错
    // 初始化options
    options.Offset = options.Offset || 0
    options.Limit = options.Limit || 20
    options.BatchId = this.state.batchId
    setLoading(true)
    getList(options)
    return Promise.resolve()
  }
  componentDidMount() {
    this.fetch({
      BatchId: this.state.batchId,
      Offset: 0,
      Limit:20,
    })
  }
  UNSAFE_componentWillReceiveProps(nextProps){
    if(nextProps.BUNotificationInfo!==this.props.BUNotificationInfo){
      this.setState({
        buIsNotified:nextProps.buIsNotified,
        BUNotificationInfo:nextProps.BUNotificationInfo
      })
    }
    if(nextProps.showAnalyProgress!==this.props.showAnalyProgress && nextProps.showAnalyProgress){
      interval= setInterval(()=>{
        console.log("轮训")
        this.fetch()
        this.props.getAnalyProgress({Id:this.state.batchId})
      },20000)
    }
  }

  enterLoading = () => {
    this.notify({ BatchId: this.state.batchId })
  }

  enterIconLoading = () => {
    this.setState({ iconLoading: true });
  }

  // 导航
  breadcrumb() {
    const selectType = [
      {"name": "","value":"请选择搜索类型"},
      {"name":"IP","value": "IP"},
      {"name":"CompanyId","value": "公司ID"},
      {"name":"CompanyName","value": "公司名称"},
      {"name":"OrgId","value": "组织ID"},
      {"name":"Domain","value": "域名"}
    ]
    return (
      <Row>
        <Col xs={24} sm={12} md={12} lg={12}>
          <Breadcrumb>
            <Breadcrumb.Item><Link to="/message">合规通知</Link></Breadcrumb.Item>
            <Breadcrumb.Item>
              批次详情
            </Breadcrumb.Item>
          </Breadcrumb>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12}>
        <div style={{display:"flex"}}>
          <Select defaultValue="" style={{ width: 120 }} onChange={this.handleChangeSelect}>
              {
                selectType.map(item=>{
                  return <Option  key={item.name} value={item.name}>{item.value}</Option>
                })
              }
          </Select>
          <Search
            placeholder="公司名，公司ID，组织ID，IP，域名"
            onSearch={this.searchValue}
          />
        </div>
        </Col>
      </Row>
    );
  }

  showAbnormalRecordModal() {
    const columns = [{
      title: 'IP',
      dataIndex: 'IP',
      key: 'IP',
    }, {
      title: 'Domain',
      dataIndex: 'Domain',
      key: 'Domain',
    }, {
      title: 'Resource',
      dataIndex: 'Resource',
      key: 'Resource'
    }];

    const modelObj = {
      title: '找不到公司信息的IP',
      width:600,
      content: (
        <Table dataSource={this.props.abnormalRecord} columns={columns} />
      ),
      onOk: () => {
        return request('RefetchCompanyInfo', { BatchId: this.state.batchId })
          .then(() => {
            notification.open({
              message: '重新拉取数据成功',
            })
          })
          .catch(err => {
            notification['error']({
              message: '重新拉取数据失败',
              description: err.message || '内部错误'
            })
          })
      },
      okText: '重试拉取',
      cancelText: '关闭'
    }

    if (['SENDING', 'FINISHED', 'REFETCHING', 'SEND_FINISHED'].includes(this.props.batchStatus)) {
      delete modelObj.onOk;
      delete modelObj.cancelText;
      modelObj.okText = '关闭';

      Modal.warning(modelObj)
    } else {

      Modal.confirm(modelObj)
    }

  }
  RePushMQUnRegister = ()=>{
    request('RePushMQUnRegister', {BatchId:this.state.batchId})
    .then(resp => {
      let message = resp.RetCode === 0 ? '加速批次成功' : resp.Message || resp.RetCode + "加速批失败"
      notification.open({
        message: message,
      });
    })
    .catch(err => {
      // 报错
      notification['error']({
        message: '加速失败',
        description: err.message || '内部错误'
      })
      // 清空列表
      return;
    })
  }
  handleTableChange = (pagination, filtersArg)=> {
    console.log('pagination, filtersArg',pagination, filtersArg)
    let options = {
      BatchId: this.state.batchId,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
    }
    if(filtersArg.NotifyStatus && filtersArg.NotifyStatus.length > 0){
      options['SendStatus'] = filtersArg.NotifyStatus
      if(pagination.current * pagination.pageSize > pagination.total){
        options['Offset'] = 0;
        options['Limit'] = 20;
      }
    }
    this.fetch(options)
  }

  searchValue =(e)=> {
  let {search} = this.state
  let options = {
    BatchId: this.state.batchId,
    Offset: 0,
    Limit: 20,
   }
   console.log(e,search)
   if(search && e){
    options[search] = e
   }
   this.fetch(options)
  }

  reSendBUEmail({ Id }) {
    const { BUNotificationInfo } = this.state
    request('RetryNotifyBUById', { Id })
      .then((resp) => {
        if (resp.RetCode === 0) {
          const index = BUNotificationInfo.findIndex(item => item.Id === Id)
          BUNotificationInfo[index].TaskStatus = "发送中"

          this.setState({
            BUNotificationInfo: JSON.parse(JSON.stringify(BUNotificationInfo))
          })
          return
        }

        notification['error']({
          message: resp.RetCode,
          description: resp.message || '内部错误'
        })
      })
      .catch(err => {
        notification['error']({
          message: '通知失败',
          description: err.message || '内部错误'
        })
      })
  }

  async showNotifyBURecordModal() {
    await this.refresh()
    const columns = [{
      //   title: 'Bu',
      //   dataIndex: 'Bu',
      //   key: 'Bu',
      // }, {
      title: 'Email',
      dataIndex: 'Email',
      key: 'Email',
    }, {
      title: 'TaskStatus',
      dataIndex: 'TaskStatus',
      key: 'TaskStatus',
    }, {
      title: '手动重发',
      render: (val, row) => {

        if (['发送失败', '接收失败'].includes(row.TaskStatus)) {
          return (
            <div>
              <Button onClick={() => this.reSendBUEmail(row)}>重发</Button>
            </div>
          )
        }
        return (
          <div>
            <Button onClick={() => this.reSendBUEmail(row)} disabled={true}>重发</Button>
          </div>
        )
      }
    }];


    Modal.info({
      title: 'BU运营通知情况',
      width:600,
      content: (
        <Table
          dataSource={this.state.BUNotificationInfo}
          columns={columns}
        />
      )
    })
  }
  showRegisteredDomainModal = async () => {
    const columns = [{
      title: 'IP',
      dataIndex: 'IP',
      key: 'IP',
    }, {
      title: 'Domain',
      dataIndex: 'Domain',
      key: 'Domain',
    }];
    Modal.info({
      title: '已备案域名列表',
      width:600,
      content: (
        <Table
          dataSource={this.props.registeredDomainRecord}
          columns={columns}
        />
      )
    })
  }
  //如果批次状态是处理中，只可以批量发送通知、如果不是处理中，则可重发通知。
  //TODO 如果是已经完成状态禁用批量发送与批量重试
  notifyAndRetry() {
    const { rawList, loading, batchStatus } = this.props
    if (batchStatus !== "PROCESSED" && batchStatus !== "NEW") {
      let disabledStatus = ['REFETCHING', 'FINISHED'].includes(batchStatus);
      // const rawList = this.props.rawList;
      // 判断list存在
      // 单击“批量发送通知”后，仅发送失败时，“批量重发”有效
      if (Array.isArray(rawList)) {
        disabledStatus = disabledStatus || rawList.some(item => item.status === "发送失败")
      }
      return (
        <Popconfirm title="是否确认批量发送？" loading={loading} onConfirm={() => this.retryNotify({ BatchId: this.state.batchId })}>
          <Button type="primary" disabled={disabledStatus} style={{marginLeft:10}}>
            批量重发
          </Button>
        </Popconfirm>
      )
    } else {
      return (
        <Popconfirm title="是否确认批量发送？" loading={loading} onConfirm={() => this.notify({ BatchId: this.state.batchId })}>
          <Button type="primary"  style={{marginLeft:10}} disabled={batchStatus === "NEW" ? true : false}>
            批量发送通知
        </Button>
        </Popconfirm>
      )
    }
  }

  //BU通知发送与BU信息查看
  buNotifyAndSelect() {
    const { loading } = this.props
    const { BUNotificationInfo=[],buIsNotified } = this.state
    if (buIsNotified) {
      return (
        <Button type="primary" style={{marginLeft:10}} onClick={this.showNotifyBURecordModal.bind(this)}>BU运营通知情况</Button>
      )
    } else {
      return (
        <Popconfirm title="是否确认批量发送？" loading={loading} onConfirm={this.notifyBU}>
          <Button type="primary" disabled={BUNotificationInfo.length !== 0}  style={{marginLeft:10}}>
            批量通知BU运营
          </Button>
        </Popconfirm>)

    }
  }

  // 按钮行
  bulkSend() {
    const { abnormalRecord=[], batchStatus, loading, registeredDomainRecord=[],speedButtonDisabled } = this.props
    return (
      <div>
        {this.notifyAndRetry()}
        {this.buNotifyAndSelect()}
        <Button
          type="primary"
          disabled={abnormalRecord.length === 0 || ['REFETCHING'].includes(batchStatus)}
          onClick={this.showAbnormalRecordModal.bind(this)}
          style={{marginLeft:10}}
        >异常数据</Button>
        <Button
          type="primary"
          disabled={registeredDomainRecord.length === 0}
          onClick={this.showRegisteredDomainModal}
          style={{marginLeft:10}}
        >已备案域名</Button>
        <Button
          type="primary"
          disabled={speedButtonDisabled}
          onClick={this.RePushMQUnRegister}
          style={{marginLeft:10}}
        >加速</Button>
        <Popconfirm title="是否确定完成了通知？" loading={loading} onConfirm={() => this.finishBatch({ BatchId: this.state.batchId })}>
          <Button type="primary" disabled={['REFETCHING', 'FINISHED'].includes(batchStatus)}  style={{marginLeft:10}} >
            完成处理
            </Button>
        </Popconfirm>
      </div>
    )
  }

  componentWillUnmount(){
    clearInterval(interval)
  }
  render() {
    const { list, pagination, showAnalyProgress, companyProgress = 0, registerProgress = 0 } = this.props
    return (
      <Card bordered={false}>
        <div>
          <Card bordered={false}>
            {this.breadcrumb()}
          </Card>
          {
            showAnalyProgress && (companyProgress<100 ||registerProgress<100)?
            <ProgressSlider companyProgress={companyProgress} registerProgress={registerProgress}/>
            :null
          }
          <Card title="批次详情" style={{ marginBottom: 24 }} bordered={false} >
            {this.bulkSend()}
            <BatchTable
              onChange={this.handleTableChange}
              data={list}
              pagination={pagination}
              batchId={this.state.batchId}
            />
          </Card>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(BatchDetail);
const mapDispatchToProps = (dispatch) => {
  return {
      getList: (data) => dispatch(getBatchList(data)),
      setPagination: (data) =>  dispatch(setPagination("SET_BATCH_PAGINATION",data)),
      setLoading: (data) => dispatch(setLoading("SET_BATCH_LOADING",data)),
      setList: (data) => dispatch(setBatchList("SET_BATCH_LIST",data)),
      getAnalyProgress:(data) => dispatch(getAnalyProgress(data))
  }
}
const mapStateToProps = ({batchDetailReducer}) => {
  // console.log(state)
  return {
      ...batchDetailReducer
  }
}
const BatchDetailContiner = connect(mapStateToProps,mapDispatchToProps)(ListForm)

export default BatchDetailContiner
