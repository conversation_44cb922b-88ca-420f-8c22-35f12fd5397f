import { connect } from "react-redux"
import List from "./List"
import { setFormValues, setPagination, getList, setLoading, getAnalyProgress } from "../../actionCreator/message"

const mapDispatchToProps = (dispatch) => {
    return {
        setFormValues: (data) =>  dispatch(setFormValues("SET_FORM_VALUES",data)),
        setPagination: (data) =>  dispatch(setPagination("SET_PAGINATION",data)),
        getList: (data) => dispatch(getList(data)),
        setLoading: (data) => dispatch(setLoading("SET_LOADING",data)),
        getAnalyProgress:(data) => dispatch(getAnalyProgress(data))
    }
  }
const mapStateToProps = ({messageReducer}) => {
    // console.log(state)
    return {
        ...messageReducer
    }
  }
  const MessageContiner = connect(mapStateToProps,mapDispatchToProps)(List)
  
  export default MessageContiner
  