import React, { Component } from 'react';
import { Link } from 'react-router-dom'
import _ from 'lodash';
import { notification, Table, Card, Button, Breadcrumb, Modal } from 'antd';
import StaticFieldBoard from '../../components/StaticFieldBoard';
import request from '../../utils/request'
import NotifyTable from '../../components/NotifyTable'

//状态枚举将英文状态转成中文在前端显示
const statusList = {
  0: "新建",
  1: "发送中",
  2: "已经接收",
  3: "发送失败",
  4: "接收失败",
  6: "禁止发送邮件获短信",
  7: "超时未响应"
}

//信息拼接,与API获取到的通知内容拼接成完整的通知
const smsHeader = "尊敬的UCloud用户，"
const mailHeader = '<div style="text-align: center;background: #F1F8FE;color: #000;font-size: 14px;line-height: 1.5;font-family: arial,verdana,sans-serif;"><table width="650" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;padding: 0;"><tbody><tr><td style="color: #4074E1;padding-bottom: 30px; padding-top: 50px;" align="center"><img src="http://static.ucloud.cn/37848443805c01fa5f277d58c094da08.png" alt="UCloud" class="logo"></td></tr></tbody></table><table width="650" border="0" cellpadding="0" cellspacing="0" style="margin:0 auto;padding: 0;border:1px solid #D6DDE3;background: #fff"><tbody><tr><td align="left" style="padding-top: 40px;padding-bottom: 10px; font-size: 30px; padding-left: 36px; color: #333;"><span>【UCloud云计算】未备案域名告警通知</span></td></tr><tr><td align="center" style="padding: 0 36px 10px;"><div style="font-size: 16px;line-height: 32px;width: 100%;text-align: left;padding-top: 20px;text-indent: 0; color: #767A7B;"><span>尊敬的UCloud用户，您好！</span></div><br></td></tr><tr><td align="left" style="padding-left: 36px;padding-right: 36px; color:#757B7B;font-size: 16px;line-height: 32px;">'
const mailFooter = `<br><br><br></td></tr><tr><td align="right" style="padding-right: 36px;padding-bottom: 30px; color: #767A7B;font-size: 14px;">UCloud云计算团队<br><span>date</span></td></tr></tbody></table><table width="650" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;padding: 0;"><tbody><tr><td align="center" style="padding-top: 26px;"><table width="430" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto;padding: 0;"><tbody><tr><td align="left"><a href="http://www.ucloud.cn//" style="text-decoration: none;color:#767A7B;font-size: 14px;">访问UCloud官网</a></td><td align="center"><a href="http://t.cn/z8emy2d" style="text-decoration: none;color:#767A7B;font-size: 14px;">关注UCloud微博</a></td><td align="right"><a href="http://blog.ucloud.cn/" style="text-decoration: none;color:#767A7B;font-size: 14px;">关注UCloud博客</a></td></tr></tbody></table></td></tr><tr><td align="center" style="padding-top: 6px; padding-bottom: 26px;color:#767A7B;font-size: 14px;">Copyright © 2012-${new Date().getFullYear()} UCloud 优刻得科技股份有限公司</td></tr></tbody></table></div>`

class NotifyInfodetail extends Component {
  state = {
    orgId: parseInt(this.props.match.params.NotifyInfo.split("|")[0], 0),
    batchId: parseInt(this.props.match.params.NotifyInfo.split("|")[1], 0),
    stepDirection: 'horizontal',
    companyId: parseInt(this.props.match.params.companyId, 10),
    showRejectForm: false,
    rejectReason: '',
    companyInfo: {},
    blockInfo: [],
    notifyInfo: [],
    smsInfo: "",
    mailInfo: "",
    loading: false,
    modalSMSVisible: false,
    modalMailVisible: false,
    pagination:{
      current: 1,
      pageSize: 20,
      total: 0
    }
  }

  // 消息预览展示
  setModalSMSVisible(modalSMSVisible) {
    this.setState({ modalSMSVisible });
  }
  setModalMailVisible(modalMailVisible) {
    this.setState({ modalMailVisible });
  }

  //网页数据获取
  componentDidMount() {
    this.fetch()
    this.timerID = setInterval(
      () => this.fetch(),
      10000
    )
  }

  // 查询基本信息
  fetch() {
    let self = this;
    let {batchId,orgId,pagination,companyInfo} = this.state
     console.log('companyInfo',companyInfo)
    let params = {
      BatchId:batchId,
      OrgId:orgId,
      Limit: pagination.pageSize,
      Offset: (pagination.current - 1) * pagination.pageSize
    }
    Promise.all([
      request('GetRecordListV2', params)
    ])
      .then(datas => {

        // 结果不正确时报错
        for (var i in datas) {
          if (datas[i].RetCode !== 0) {
            throw new Error(datas[i].Message)
          }
        }
        let companyInfo = []
        let blockInfo = []
        let notifyInfo = []
        //整理封禁信息
        _.forEach(datas[0].RecordList, function (value) {
          if (value.OrgId === orgId) {
            companyInfo.push(value)
            blockInfo = blockInfo.concat(value.SealInfo)
          }
        });

        //整理通知状态信息，英文换成中文
        _.forEach(companyInfo[0].NotifyInfo, function (value) {
          value.EmailStatus = statusList[value.EmailStatus]
          value.SmsStatus = statusList[value.SmsStatus]
          notifyInfo.push(value)
        })
  
        //获取通知内容
        Promise.all([
          request('GetSmsInfo', { NotifyId: companyInfo[0].NotifyInfo[0].Id }),
          request('GetEmailInfo', { NotifyId: companyInfo[0].NotifyInfo[0].Id }),
        ])
          .then(datas => {
            //处理处理
            for (var i in datas) {
              if (datas[i].RetCode !== 0) {
                throw new Error(datas[i].Message)
              }
            }

            // 完成消息拼接
            this.setState({
              smsInfo: smsHeader + datas[0].SmsContent,
              mailInfo: mailHeader + datas[1].EmailContent + mailFooter,
            })

          })
          .catch(err => {
            notification['error']({
              message: '获取通知消息失败',
              description: err.message || '内部错误'
            })
          })
        let { pagination } = this.state
        this.setState({
          blockInfo: _.sortedUniq(blockInfo),
          companyInfo: companyInfo[0],
          notifyInfo: companyInfo[0].NotifyInfo,
          loading: false,
          pagination: {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: datas[0].TotalCount
          }
        })
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '获取用户信息失败',
          description: err.message || '内部错误'
        })

        self.setState({ loading: false })
      })
  }

  // 导航
  breadcrumb() {
    let batchInfoPageURL = "/message/batchdetail/" + this.state.batchId
    return (
      <div>
        <Breadcrumb>
          <Breadcrumb.Item>位置: </Breadcrumb.Item>
          <Breadcrumb.Item><Link to='/message'>首页</Link></Breadcrumb.Item>
          <Breadcrumb.Item><Link to={batchInfoPageURL}>批次详情</Link></Breadcrumb.Item>
          <Breadcrumb.Item><Link to='/'>通知详情</Link></Breadcrumb.Item>
        </Breadcrumb>
      </div>
    );
  }
  UNSAFE_componentWillUpdate(){
    clearInterval(this.timerID)
  }
  handleTableChange = (pagination, filters, sorter) => {
    console.log(pagination, filters, sorter)
    this.setState({
        pagination: {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: this.state.pagination.total
        },
    }, () => {
      this.fetch()
    })
  }
  render() {
    let { pagination } = this.state
    const loading = this.state.loading;
    const companyInfo = this.state.companyInfo;
    const blockInfoData = this.state.blockInfo

    const blockColumns = [{
      title: '封禁IP',
      dataIndex: 'Ip',
      key: 'Ip',
    }, {
      title: '封禁域名',
      dataIndex: 'Domain',
      key: 'Domain',
    }];

    const companyInfoData = {
      '客户ID': companyInfo.CompanyId,
      '客户名': companyInfo.CompanyName,
      '客户等级': companyInfo.VipLevel,
      '客户经理': companyInfo.Manager,
    }
    //企业信息卡
    const infoCard = (
      <div style={{ marginBottom: 24 }}>
        <StaticFieldBoard data={companyInfoData} />
        <br />
        <div>
        </div>
      </div>
    )

    //封禁信息卡
    const blockInfoCard = (
      <div style={{ marginBottom: 24 }}>
        <br />
        <div>
          <Button onClick={() => this.setModalSMSVisible(true)}>短信预览</Button>
          <Modal
            title="短信预览"
            visible={this.state.modalSMSVisible}
            onOk={() => this.setModalSMSVisible(false)}
            onCancel={() => this.setModalSMSVisible(false)}
          >
            <p>{this.state.smsInfo}</p>
          </Modal>
          &nbsp;
          <Button  onClick={() => this.setModalMailVisible(true)}>邮件预览</Button>
          <Modal
            title="邮件预览"
            // wrapClassName="vertical-center-modal"
            width={800}
            visible={this.state.modalMailVisible}
            onOk={() => this.setModalMailVisible(false)}
            onCancel={() => this.setModalMailVisible(false)}
          >
            <p>
              {/* 引入获取的邮件信息做为html展示 */}
              <div className="markdown" dangerouslySetInnerHTML={{ __html: this.state.mailInfo }}></div>
            </p>
            {/* {this.state.mailInfo} */}
          </Modal>
        </div>
        <div>
          <Table dataSource={blockInfoData} columns={blockColumns} />
        </div>
      </div>
    )

    //通知信息卡
    const notifyCard = (
      <div style={{ marginBottom: 24 }}>
        <NotifyTable
          // 传入 fetchInfo供子组件在发送消息后刷新用
          fetchInfo={() => this.fetch()}
          loading={this.state.loading}
          data={this.state.notifyInfo}
          batchid={this.state.batchId}
          pagination={pagination}
          onChange={this.handleTableChange}
        />
      </div>
    )

    return (
      <div>
        <div>
          <Card bordered={false}>
            {this.breadcrumb()}
          </Card>
        </div>
        <Card title="用户信息" style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {infoCard}
        </Card>
        <Card title="封禁信息" style={{ marginBottom: 24 }} bordered={false} loading={loading}>
          {blockInfoCard}
        </Card>
        <Card title="发送详情" bordered={false}>
          {notifyCard}
        </Card>
      </div>
    )
  }
}

export default NotifyInfodetail;
