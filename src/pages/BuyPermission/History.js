import React, { Component } from 'react'
import { notification, Row, Select,Col, Card, Form, Button, InputNumber } from 'antd'
import HistoryTable from '../../components/HistoryTable'
import './List.css'
import request from '../../utils/request'

const Option = Select.Option;
const _ = require('lodash')
const FormItem = Form.Item
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',')

var IsLastOpen="No"

function handleChange(value) {
  IsLastOpen=value
}

class History extends Component {
  constructor(props) {
    super(props)
    this.state = {
      list: [], // 列表初始值
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0
      },
      loading: false
    }
  }

  

  // 查询列表
  fetch(options = {}) {

    // 统一设置分页或者报错
    let self = this

    // 初始化options,为了支持分页（后端查询全部），offset\limit前端处理
    options.Offset = options.Offset || 0
    options.Limit = options.Limit || 5

    // Loading Modal 
    self.setState({ loading: true })

    request('GetHistoryList', options)
      .then(resp => {
        if (resp.RetCode !== 0) {
          throw new Error(resp.Message)
        }


        //设置total支持分页面
        self.setState({
          pagination: {
            total: resp.Count || 0
          }
        })
        let promises = []
        //查询的范围（防止遍例）
        _.forEach(resp.LogList, function (value) {
          promises.push(
            new Promise((resolve) => {
              Promise.all([
                request('GetCompanyInfo', { CompanyId: value.CompanyId }),
                request('GetUserAuthInfo', { CompanyId: value.CompanyId }),
                request('CheckBuyPermissionBit', { CompanyId: value.CompanyId }),
              ]).then(eachResp => {
                // 错误判断
                for (var i in eachResp) {
                  if (eachResp[i].RetCode !== 0) {
                    throw new Error(eachResp[i].Message)
                  }
                }

                let companyInfo = {}
                companyInfo = Object.assign(eachResp[0].CompanyInfo, _.pick(eachResp[1].AuthInfo[0], ['AuditState', 'IsUnified', 'AuthType', 'CertificateType']))

                //如果没有审核状态，则返回未认证
                if (companyInfo.AuditState === undefined ) {
                  companyInfo.AuditState="未认证"
                }
                //将结果转成可读的描述
                if (eachResp[2].CanBuy === false) {
                  companyInfo.BuyPermission = "无购买权限"
                } else {
                  companyInfo.BuyPermission = "有购买权限"
                }
                //合并二个object,将单条日志记录与公司信息拼成一个对象
                resolve( _.pick(Object.assign(companyInfo, value), [ 'CompanyId','CompanyName','Manager','Result','Remark','Operator','CreateTime','AuditState','BuyPermission']) )
              })
            }
          ))

        })
        return Promise.all(promises)

      })
      .then(logList => {
        self.setState({
          list: []
        })
        self.setState({
          list: logList,
          pagination: {
            current: options.Offset / options.Limit + 1,
            pageSize: options.Limit,
          },
          loading: false
        })
      })
      .catch(err => {
        console.log('err',err)
        // 报错
        notification['error']({
          message: '获取列表失败',
          description: err.message || '内部错误'
        })

        // 清空列表
        self.setState({
          loading: false,
          list: [],
          pagination: {
            current: 1,
            pageSize: 5,
            total: 0
          }
        })
        return
      })
  }

  // 重置搜索框
  handleFormReset() {
    const { form } = this.props
    let self = this
    form.resetFields()

    this.setState({
      formValues: {
        Offset: 0,
        Limit: 5
      },
    }, () => {
      self.fetch(self.state.formValues)
    })
  }

  // 展开、收起搜索框
  toggleForm() {
    this.setState({
      expandForm: !this.state.expandForm,
    })
  }

  // 搜索
  handleSearch(e) {
    e.preventDefault()
    const { form } = this.props

    form.validateFields((err, fieldsValue) => {
      if (err) return
      const values = {
        ...fieldsValue,
        'IsLastOpen':IsLastOpen
      }

      this.setState({
        formValues: values,
      })

      this.fetch(values)
    })
  }
  

  renderSimpleForm() {
    const { getFieldDecorator } = this.props.form
    return (
      <Form layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>

          <Col md={8} sm={24}>
            <FormItem label="公司Id">
              {getFieldDecorator('CompanyId')(
                <InputNumber style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>

           <Col md={8} sm={24}>
            <FormItem label="筛选">
            <Select defaultValue="所有" style={{ width: 120 }} onChange={handleChange}>
                <Option value="No">所有</Option>
                <Option value="Is">最新操作为开启权限</Option>
             </Select>
            </FormItem>
          </Col>

          <Col md={8} sm={24}>
            <div className="ant-row ant-form-item" style={{ marginTop: 4, display: 'inline-block', float: 'right' }}>
              <Button type="primary" onClick={this.handleSearch.bind(this)}>查询</Button>
              <Button style={{ marginLeft: 8 }} onClick={this.handleFormReset.bind(this)}>重置</Button>
            </div>
          </Col>
        </Row>
      </Form>
    )
  }

  // 根据是否展开获取对应的表单形式
  renderForm() {
    return this.renderSimpleForm()
  }

  componentDidMount() {
    this.fetch({})
  }

 
  // 处理分页
  handleHistoryTableChange = (pagination, filtersArg) => {
    const { formValues } = this.state;
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      const newObj = { ...obj };
      newObj[key] = getValue(filtersArg[key]);
      return newObj;
    }, {});

    if (formValues) {
      delete formValues.Limit
      delete formValues.Offset
    }

    const params = {
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
      ...formValues,
      ...filters,
    };
    this.fetch(params)
  }

  render() {
    return (
      <Card bordered={false}>
        <div>
          <div>
            {this.renderForm()}
          </div>
          <HistoryTable
            // 传入 fetchInfo供子组件在发送消息后刷新用
            fetchInfo={() => this.fetch(this.state.formValues)}
            loading={this.state.loading}
            data={this.state.list}
            pagination={this.state.pagination}
            onChange={this.handleHistoryTableChange}

          />
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(History)

export default ListForm
 //处理下拉选择
