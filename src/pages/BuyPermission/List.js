import React, { Component } from "react";
import {
  notification,
  Row,
  Col,
  Card,
  Form,
  Button,
  InputNumber,
  Tabs,
} from "antd";
import BuyPermissionTable from "../../components/BuyPermissionTable";
import NeedUpdateBuyPermissionTable from "../../components/NeedUpdateBuyPermissionTable";
import "./List.css";
import pick from "lodash/pick";
import request from "../../utils/request";

const FormItem = Form.Item;

const TabPane = Tabs.TabPane;

class List extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], // 列表初始值
      needUpdateList: [],
      // canDisabledIp:false,//是否可以封存ip
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
      loading: false,
    };
    this.fetch = this.fetch.bind(this);
    this.getOneCompanyInfo = this.getOneCompanyInfo.bind(this);
    this.handleFormReset = this.handleFormReset.bind(this);
    this.update = this.update.bind(this);
    this.getNeedUpdateBuyPermissionList =
      this.getNeedUpdateBuyPermissionList.bind(this);
    this.changeTab = this.changeTab.bind(this);
  }
  /**
   * 获取待处理数据
   * @param {*} options
   */
  getNeedUpdateBuyPermissionList(options = {}) {
    let self = this;
    options.Offset = options.Offset || 0;
    options.Limit = options.Limit || 5;
    request("GetNeedUpdateBuyPermissionList", options)
      .then((res) => {
        if (res.RetCode === 0) {
          let needUpdateList = res.Result;
          var promises = [];
          needUpdateList.forEach((info) => {
            promises.push(
              self.getOneCompanyInfo({ ...options, CompanyId: info.CompanyId })
            );
          });
          Promise.all(promises).then((list) => {
            list.forEach((companyInfo, index) => {
              Object.assign(needUpdateList[index], companyInfo.companyInfo[0]);
            });
            // console.log(list,needUpdateList)
            this.setState({
              needUpdateList,
              pagination: {
                current: Math.ceil(options.Offset / options.Limit) + 1,
                pageSize: options.Limit,
                total: res.Count,
              },
            });
          });
        }
      })
      .catch((err) => {
        notification["error"]({
          message: "获取待处理列表失败",
          description: err.message || "内部错误",
        });
      });
  }
  // 自定义查询列表
  fetch(options = {}) {
    // 统一设置分页或者报错
    // 初始化options
    options.Offset = options.Offset || 0;
    options.Limit = options.Limit || 5;

    // Loading Modal
    this.setState({ loading: true });

    this.getOneCompanyInfo(options)
      .then(({ companyInfo, total }) => {
        this.setState({
          list: companyInfo,
          pagination: {
            current: options.Offset / options.Limit + 1,
            pageSize: options.Limit,
            total,
          },
          loading: false,
        });
      })
      .catch((err) => {
        // 报错
        notification["error"]({
          message: "获取列表失败",
          description: err.message || "内部错误",
        });

        // 清空列表
        this.setState({
          loading: false,
          list: [],
          pagination: {
            current: 1,
            pageSize: 20,
            total: 0,
          },
        });
      });
  }
  /**
   * 根据companyId获取对应的公司信息
   * @param {*} options
   */
  getOneCompanyInfo(options) {
    return Promise.all([
      request("GetCompanyInfo", options),
      request("GetUserAuthInfo", options),
      request("CheckBuyPermissionBit", options),
    ])
      .then((resp) => {
        //错误判断
        for (var i in resp) {
          if (resp[i].RetCode !== 0) {
            throw new Error(resp[i].Message);
          }
        }
        //拼数据，talbe展示需要为数组结构,以GetCompanyInfo信息为主，加上实名认证需要的信息
        let companyInfo = [];
        companyInfo[0] = Object.assign(
          resp[0].CompanyInfo,
          pick(resp[1].AuthInfo[0], [
            "AuditState",
            "IsUnified",
            "AuthType",
            "CertificateType",
          ])
        );
        // 换成可供阅读的文字
        if (resp[2].CanBuy === false) {
          companyInfo[0].BuyPermission = "无购买权限";
        } else {
          companyInfo[0].BuyPermission = "有购买权限";
        }
        return Promise.resolve({ companyInfo, total: resp.TotalCount || 0 });
      })
      .catch((err) => {
        return Promise.reject(err);
      });
  }
  update(options = {}, type) {
    //重发消息
    let action = options.Action;
    delete options.Action;
    this.setState({ loading: true });
    request(action, options)
      .then((resp) => {
        let message =
          resp.RetCode === 0
            ? "设置成功"
            : resp.Message || resp.RetCode + "设置失败";

        notification.open({
          message: message,
        });

        this.setState({
          loading: false,
        });
      })
      .then(
        type === 1
          ? this.fetch(this.state.formValues)
          : this.getNeedUpdateBuyPermissionList
      )
      .catch((err) => {
        this.setState({
          loading: false,
        });
        // 报错
        notification["error"]({
          message: "设置失败",
          description: err.message || "内部错误",
        });
        return;
      });
  }
  // 重置搜索框
  handleFormReset() {
    const { form } = this.props;
    form.resetFields();

    this.setState(
      {
        formValues: {
          Offset: 0,
          Limit: 5,
        },
      },
      () => {
        this.fetch(this.state.formValues);
      }
    );
  }

  // 展开、收起搜索框
  toggleForm() {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  }

  // 搜索
  handleSearch(e) {
    e.preventDefault();
    const { form } = this.props;

    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const values = {
        ...fieldsValue,
      };
      this.setState({
        formValues: values,
      });

      this.fetch(values);
    });
  }

  renderSimpleForm() {
    const { getFieldDecorator } = this.props.form;
    return (
      <Form
        onSubmit={this.handleSearch.bind(this)}
        layout="inline"
        className="ant-advanced-search-form"
      >
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col md={8} sm={24}>
            <FormItem label="公司Id">
              {getFieldDecorator("CompanyId")(
                <InputNumber style={{ width: "100%" }} />
              )}
            </FormItem>
          </Col>
          <Col md={8} sm={24}>
            <div
              className="ant-row ant-form-item"
              style={{ marginTop: 4, display: "inline-block", float: "right" }}
            >
              <Button type="primary" htmlType="submit">
                查询
              </Button>
              <Button
                style={{ marginLeft: 8 }}
                onClick={this.handleFormReset.bind(this)}
              >
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
    );
  }
  changeTab(activeTab) {
    // console.log(activeTab)
    if (activeTab === "1") {
      this.getNeedUpdateBuyPermissionList();
    }
  }
  render() {
    const { formValues, loading, list, pagination, needUpdateList } =
      this.state;
    return (
      <Card bordered={false}>
        <Tabs defaultActiveKey="1" onChange={this.changeTab}>
          <TabPane tab="待处理" key="1">
            <NeedUpdateBuyPermissionTable
              // 传入 fetchInfo供子组件在发送消息后刷新用
              fetchInfo={this.getNeedUpdateBuyPermissionList}
              updateInfo={this.update}
              loading={loading}
              data={needUpdateList}
              pagination={pagination}
              // canDisabledIp={canDisabledIp}
            />
          </TabPane>
          <TabPane tab="自处理" key="2">
            <div>{this.renderSimpleForm()}</div>
            <BuyPermissionTable
              // 传入 fetchInfo供子组件在发送消息后刷新用
              fetchInfo={() => this.fetch(formValues)}
              updateInfo={this.update}
              loading={loading}
              data={list}
              pagination={pagination}
              // canDisabledIp={canDisabledIp}
            />
          </TabPane>
        </Tabs>
      </Card>
    );
  }
  componentDidMount() {
    let options = {},
      { pagination } = this.state;
    options.Offset = (pagination.current - 1) * pagination.pageSize || 0;
    options.Limit = pagination.pageSize || 5;
    this.getNeedUpdateBuyPermissionList(options);
  }
}

const ListForm = Form.create()(List);

export default ListForm;
