import React, { Component } from 'react'
import { notification, Card } from 'antd'
import RegisteredTable from '../../components/RegisteredTable'
import './List.css'
import request from '../../utils/request'


class RegisteredDomain extends Component {
	constructor(props) {
		super(props)
		this.state = {
			list: [], // 列表初始值
			pagination: {
				current: 1,
				pageSize: 20,
				total: 0
			},
			loading: false
		}
	}

	deleteRows = (ids) => {
		const set = new Set(ids)
		const list = this.state.list.filter(item => !set.has(item.id))

		this.setState({
			list,
		})
	}


	// 查询列表
	fetch = (options = {}) => {

		// 统一设置分页或者报错
		// 初始化options
		options.Pages = options.Pages || 0
		options.Limit = options.Limit || 20

		// Loading Modal 
		this.setState({ loading: true })

		request('GetRegisteredDomainList', options)
			.then(result => {
				console.log(result)

				if (result.RetCode !== 0) {

					// 报错
					notification['error']({
						message: '获取列表失败',
						description: result.Message || '内部错误'
					})
					return
				}

				const { data, total } = result.RecordList

				this.setState({
					list: data,
					pagination: {
						current: options.Pages + 1,
						pageSize: options.Limit,
						total: total || 0
					},
					loading: false
				})
			}).catch(() => {

				// 报错
				notification['error']({
					message: '获取列表失败',
					description: '内部错误'
				})
				return
			})

	}

	// 挂载前查询
	componentDidMount() {
		this.fetch({})
	}
	handleTableChange = (pagination) => {
		this.setState({
			pagination
		})
		this.fetch({
			Pages: pagination.current - 1,
			limit: pagination.pageSize
		})
	}
	render() {
		return (
			<Card bordered={false}>
				<div>
					<div>
					</div>
					<RegisteredTable
						// 传入 fetchInfo供子组件在发送消息后刷新用
						deleteRows={ids => this.deleteRows(ids)}
						loading={this.state.loading}
						data={this.state.list}
						onChange={this.handleTableChange}
						pagination={this.state.pagination}
						fetch = {this.fetch}
					/>
				</div>
			</Card>
		)
	}
}


export default RegisteredDomain
