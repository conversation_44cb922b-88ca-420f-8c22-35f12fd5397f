import React, { useState, useEffect } from "react";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Popconfirm,
  Icon,
  Modal,
  Form,
  message,
  InputNumber
} from "antd";
import request from "../../utils/request";
import moment from "moment";
const { Search } = Input;

const EmailWhiteList = (props) => {
  const { getFieldDecorator, resetFields } = props.form;
  const [dataSource, setDataSource] = useState([]);
  const [EmailSuffixLimit, setEmailSuffixLimit] = useState();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editable, setEditable] = useState(false);
  const [oldDatasource, setOldDataSource] = useState([]);
  const onChange = (newValue) => {
    setEmailSuffixLimit(newValue);
  };
  useEffect(() => {
    GetEmailSuffixWhiteList();
  }, [loading]);

  const GetEmailSuffixWhiteList = async () => {
    let res = await request("GetEmailSuffixWhiteList");
    if (res.RetCode === 0) {
      let result = []
      Object.entries(res.Data.EmailRegisterWhiteList).forEach(entry=>{
        let valueArr = entry[1].split(',')
        result.push({
          EmailSuffix: entry[0],
          CreateTime: moment(parseInt(valueArr[0])*1000).format('YYYY-MM-DD HH:mm:ss'),
          Operator: valueArr[1].replaceAll("'",""),
          Remark: valueArr[2].replaceAll("'","")
        })
      })
      setDataSource(result);
      setEmailSuffixLimit(res.Data.EmailSuffixLimit)
      setOldDataSource(result)
    }
  };
  const onSearch = (value) => {
    let findObj = dataSource.filter((item) => item.EmailSuffix.includes(value));
    setDataSource(findObj);
  };
  const handleSearchChange = (e) => {
    if (!e.target.value) {
      setDataSource(oldDatasource);
    }
  };


  const columns = [
    {
      title: "邮箱后缀",
      dataIndex: "EmailSuffix",
      key: "EmailSuffix"
    },
    {
      title: "创建时间",
      dataIndex: "CreateTime",
      key: "CreateTime"
    },
    {
      title: "操作人",
      dataIndex: "Operator",
      key: "Operator"
    },
    {
      title: "备注",
      dataIndex: "Remark",
      key: "Remark"
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <>
            <Popconfirm
              title="确定从白名单移除吗?"
              onConfirm={() => {
                deleteRow(row);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="danger" ghost>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];
  const deleteRow = async (row) => {
    setLoading(true);
    try {
      let res = await request("DeleteEmailSuffixWhiteList", {
        EmailSuffix: row.EmailSuffix,
      });
      if (res.RetCode === 0) {
        message.success("删除成功");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          let res = await request("AddEmailSuffixWhiteList", values);
          if (res.RetCode === 0) {
            if (res.Data.result === 1) {
              message.success("添加成功");
            } else {
              message.error("添加失败,请检查是否重复添加");
            }

          }
        } catch (error) {
          console.log("error", error);
        } finally {
          setVisible(false);
          setLoading(false);
          resetFields();
        }
      }
    });
  };

  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        onSubmit={handleSubmit}
      >
        <Form.Item label="邮箱后缀">
          {getFieldDecorator("EmailSuffix", {
            rules: [
              {
                required: true,
                message: "邮箱后缀不能为空",
              },
            ],
          })(<Input placeholder="请输入邮箱后缀" />)}
        </Form.Item>
        <Form.Item label="备注">
          {getFieldDecorator("Remark", {
            rules: [
              {
                required: true,
                message: "备注不能为空",
              },
            ],
          })(<Input placeholder="请输入备注" />)}
        </Form.Item>
      </Form>
    );
  };
  return (
    <Card style={{ minHeight: "80vh" }}>
      <div style={{display: 'flex',alignItems: 'center', marginBottom: '16px'}}>
        <span>24h注册邮箱后缀拉黑阈值:</span>
        <InputNumber
            min={1}
            style={{
              margin: '0 16px',
              width: '100px'
            }}
            disabled={!editable}
            value={EmailSuffixLimit}
            onChange={onChange}
          />
           {
            editable ? <>
              <Popconfirm
                title="确定修改阈值吗?"
                onConfirm={async () => {
                  let res = await request("UpdateEmailSuffixLimit", {
                    EmailSuffixLimit: EmailSuffixLimit + ''
                  });
                  if (res.RetCode === 0 && res.Data.result === 'OK') {
                    message.success('修改阈值成功')
                    setEditable(false)
                  } else {
                    message.error(res.Data?.result || '修改失败')
                  }
                }}
                onCancel={() => {
                  setEditable(false)
                }}
                okText="确定"
                cancelText="取消"
              >
                <Icon type="check-circle" theme="twoTone" twoToneColor="#52c41a" />
              </Popconfirm>
              <Icon type="close-circle" style={{marginLeft: '6px'}} onClick={() => {
                setEditable(false)
              }} />
            </> :
              <Icon type="edit" onClick={() => {
                setEditable(true)
              }} />
          }
      </div>
      <Row type="flex" justify="space-between" style={{ marginBottom: "30px" }}>
        <Col span={2}>
          <Button
            type="primary"
            onClick={() => {
              resetFields();
              setVisible(true);
            }}
          >
            添加白名单
          </Button>
        </Col>
        <Col span={4}>
          <Search
            className="searchComp"
            placeholder="请输入邮箱后缀"
            onSearch={onSearch}
            onChange={handleSearchChange}
            enterButton={<Icon type="search" />}
          />
        </Col>
      </Row>
      <Row>
        <Table dataSource={dataSource} columns={columns} rowKey="id" />
      </Row>
      <Modal
        title="添加白名单"
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
          resetFields();
        }}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleSubmit}
          >
            提交
          </Button>,
          <Button
            key="back"
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </Button>,
        ]}
      // width={500}
      >
        {addForm()}
      </Modal>
    </Card>
  );
};

export default Form.create()(EmailWhiteList);
