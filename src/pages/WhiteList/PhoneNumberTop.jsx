import React, { useState, useEffect, useRef } from "react";
import moment from "moment";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Popconfirm,
  Icon,
  Modal,
  Form,
  message,
} from "antd";
import request from "../../utils/request";
import cloneDeep from "lodash/cloneDeep";
const { Search } = Input;
const { TextArea } = Input;

const PhoneNumberBindingCount = (props) => {
  const { getFieldDecorator, resetFields } = props.form;
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [oldDatasource, setOldDataSource] = useState([]);
  const [editing, setEditing] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    GetGlobalSetting();
  }, [loading]);

  useEffect(() => {
    inputRef.current?.focus();
  }, [editing]);
  const GetGlobalSetting = async () => {
    let res = await request("GetGlobalSetting");
    if (res.RetCode === 0) {
      let dataList = Object.entries(res.PhoneRegisterWhite).map((item) => {
        let obj = JSON.parse(item[1]);
        return { ...obj, phone: item[0], updating: false, id: item[0] + obj["update_time"] };
      });
      dataList.sort((a, b) => b.update_time - a.update_time);

      setDataSource(dataList);
      setOldDataSource(dataList);
    }
  };
  const onSearch = (value) => {
    let findObj = dataSource.filter((item) => item.phone === value);
    setDataSource(findObj);
  };
  const handleSearchChange = (e) => {
    if (!e.target.value) {
      setDataSource(oldDatasource);
    }
  };

  const save = async (values) => {
    setLoading(true);
    try {
      const options = {
        Type: "PhoneRegisterWhite",
        Operate: "Add",
        Content: values.phone,
        Remark: inputRef.current.state.value,
      };
      let res = await request("ModifyGlobalSetting", options);
      if (res.RetCode === 0) {
        message.success("修改成功");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setVisible(false);
      setLoading(false);
      setEditing(false);
      resetFields();
    }
  };
  const columns = [
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
      render: (val, row, index) => {
        return row.updating ? (
          <div style={{ display: "flex" }}>
            <Input
              defaultValue={val}
              onPressEnter={() => {
                save(row);
              }}
              ref={inputRef}
            />
            <Button
              type="primary"
              onClick={() => {
                save(row);
              }}
            >
              确认
            </Button>
            <Button
              onClick={() => {
                let cloneData = cloneDeep(dataSource);
                cloneData[index].updating = false;
                setDataSource(cloneData);
                setEditing(false);
              }}
            >
              取消
            </Button>
          </div>
        ) : (
          <div>
            {val}
            <Icon
              type="edit"
              style={{ marginLeft: "8px", cursor: "pointer" }}
              onClick={() => {
                let cloneData = cloneDeep(dataSource);
                cloneData[index].updating = true;
                setEditing(true);
                setDataSource(cloneData);
              }}
            />
          </div>
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "update_time",
      key: "update_time",
      render: (val) => (
        <span>
          {val ? moment(val * 1000).format("YYYY-MM-DD HH:mm:ss") : ""}
        </span>
      ),
    },
    {
      title: "操作人",
      dataIndex: "operator",
      key: "operator",
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <>
            <Popconfirm
              title="确定从白名单移除吗?"
              onConfirm={() => {
                deleteRow(row);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="danger" ghost>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];
  const deleteRow = async (row) => {
    setLoading(true);
    try {
      const options = {
        Type: "PhoneRegisterWhite",
        Operate: "Delete",
        Content: row.phone,
        Remark: row.remark,
      };
      let res = await request("ModifyGlobalSetting", options);
      if (res.RetCode === 0) {
        message.success("删除成功");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          const options = {
            Type: "PhoneRegisterWhite",
            Operate: "Add",
            Content: values.Phone,
            Remark: values.Remark,
          };
          let res = await request("ModifyGlobalSetting", options);
          if (res.RetCode === 0) {
            message.success("添加成功");
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          setVisible(false);
          setLoading(false);
          resetFields();
        }
      }
    });
  };

  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        onSubmit={handleSubmit}
      >
        <Form.Item label="Id" style={{ display: "none" }}>
          {getFieldDecorator("Id")(<></>)}
        </Form.Item>
        <Form.Item label="手机号">
          {getFieldDecorator("Phone", {
            rules: [
              {
                required: true,
                message: "请输入手机号",
              },
            ],
          })(<Input placeholder="请输入手机号,例(86)13800088000" />)}
        </Form.Item>
        <Form.Item label="备注">
          {getFieldDecorator("Remark", {
            rules: [
              {
                required: true,
                message: "请填写备注",
              },
            ],
          })(<TextArea rows={4} />)}
        </Form.Item>
      </Form>
    );
  };
  return (
    <Card style={{ minHeight: "80vh" }}>
      <Row type="flex" justify="space-between" style={{ marginBottom: "30px" }}>
        <Col span={2}>
          <Button
            type="primary"
            onClick={() => {
              resetFields();
              setVisible(true);
            }}
          >
            添加白名单
          </Button>
        </Col>
        <Col span={4}>
          <Search
            className="searchComp"
            placeholder="请输入手机号,例(86)13800088000"
            onSearch={onSearch}
            onChange={handleSearchChange}
            enterButton={<Icon type="search" />}
          />
        </Col>
      </Row>
      <Row>
        <Table dataSource={dataSource} columns={columns} rowKey="id" />
      </Row>
      <Modal
        title="添加白名单"
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
          resetFields();
        }}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleSubmit}
          >
            提交
          </Button>,
          <Button
            key="back"
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </Button>,
        ]}
        // width={500}
      >
        {addForm()}
      </Modal>
    </Card>
  );
};

export default Form.create()(PhoneNumberBindingCount);
