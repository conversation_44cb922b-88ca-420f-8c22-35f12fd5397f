import React, { useState, useEffect } from "react";
import moment from "moment";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Popconfirm,
  Icon,
  Modal,
  Form,
  message,
} from "antd";
import request from "../../utils/request";
const { Search } = Input;
const { TextArea } = Input;

const DNSWhiteList = (props) => {
  const { getFieldDecorator, resetFields } = props.form;
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [oldDatasource, setOldDataSource] = useState([]);
  useEffect(() => {
    GetDNSWhiteList();
  }, [loading]);

  const GetDNSWhiteList = async () => {
    let res = await request("GetDNSWhiteList");
    if (res.RetCode === 0) {
      setDataSource(res.Data);
      setOldDataSource(res.Data)
    }
  };
  const onSearch = (value) => {
    let findObj = dataSource.filter((item) => item.companyId === value);
    setDataSource(findObj);
  };
  const handleSearchChange = (e) => {
    if (!e.target.value) {
      setDataSource(oldDatasource);
    }
  };


  const columns = [
    {
      title: "公司ID",
      dataIndex: "company_id",
      key: "companyId",
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
    },
    {
      title: "创建时间",
      dataIndex: "update_time",
      key: "update_time",
      render: (val) => (
        <span>
          {val ? moment(val * 1000).format("YYYY-MM-DD HH:mm:ss") : ""}
        </span>
      ),
    },
    {
      title: "操作人",
      dataIndex: "operator",
      key: "operator",
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <>
            <Popconfirm
              title="确定从白名单移除吗?"
              onConfirm={() => {
                deleteRow(row);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="danger" ghost>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];
  const deleteRow = async (row) => {
    setLoading(true);
    try {
      let res = await request("DeleteDNSWhiteList",{
        Id: row.id,
      });
      if (res.RetCode === 0) {
        message.success("删除成功");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          values.CompanyId = parseInt(values.CompanyId);
          let res = await request("AddDNSWhiteList", values);
          if (res.RetCode === 0) {
            message.success("添加成功");
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          setVisible(false);
          setLoading(false);
          resetFields();
        }
      }
    });
  };

  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        onSubmit={handleSubmit}
      >
        <Form.Item label="Id" style={{ display: "none" }}>
          {getFieldDecorator("Id")(<></>)}
        </Form.Item>
        <Form.Item label="公司ID">
          {getFieldDecorator("CompanyId", {
            rules: [
              {
                required: true,
                message: "请输入公司ID",
              },
            ],
          })(<Input placeholder="请输入公司ID" type="number"/>)}
        </Form.Item>
        <Form.Item label="备注">
          {getFieldDecorator("Remark", {
            rules: [
              {
                required: true,
                message: "请填写备注",
              },
            ],
          })(<TextArea rows={4} />)}
        </Form.Item>
      </Form>
    );
  };
  return (
    <Card style={{ minHeight: "80vh" }}>
      <Row type="flex" justify="space-between" style={{ marginBottom: "30px" }}>
        <Col span={2}>
          <Button
            type="primary"
            onClick={() => {
              resetFields();
              setVisible(true);
            }}
          >
            添加白名单
          </Button>
        </Col>
        <Col span={4}>
          <Search
            className="searchComp"
            placeholder="请输入公司ID"
            onSearch={onSearch}
            onChange={handleSearchChange}
            enterButton={<Icon type="search" />}
          />
        </Col>
      </Row>
      <Row>
        <Table dataSource={dataSource} columns={columns} rowKey="id" />
      </Row>
      <Modal
        title="添加白名单"
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
          resetFields();
        }}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleSubmit}
          >
            提交
          </Button>,
          <Button
            key="back"
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </Button>,
        ]}
        // width={500}
      >
        {addForm()}
      </Modal>
    </Card>
  );
};

export default Form.create()(DNSWhiteList);
