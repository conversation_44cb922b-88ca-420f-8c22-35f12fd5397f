import React, { useState, useEffect } from "react";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Modal,
  Form,
  message,
  Radio,
  notification
} from "antd";
import request from "../../utils/request";
import moment from "moment";
const FormItem = Form.Item;
const UniversityAuthEmail = (props) => {
  const { getFieldDecorator, resetFields, setFieldsValue } = props.form;
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [OrgName, setOrgName] = useState("");
  const [EmailSuffix, setEmailSuffix] = useState("");
  const [operateType, setOperateType] = useState("add");
  const [oldDatasource, setOldDataSource] = useState([]);
  const [oldFilters, setOldFilters] = useState({});
  const [oldSorter, setOldSorter] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showTotal: total => `共计：${total}条`,
    showQuickJumper: true,
    showSizeChanger: true,
  });
  useEffect(()=>{
    GetUniversityAuthEmails()
  },[])
  const GetUniversityAuthEmails = async () => {
    setDataLoading(true);
    let res = await request("GetUniversityAuthEmails");
    const getAllRecords = async (totalRecords) => {
      let allRecords = [];
      const totalPages = Math.ceil(totalRecords / 1000);

      for (let page = 1; page <= totalPages; page++) {
        const pageOptions = {
          Limit: 1000,
          Offset: 1000 * (page - 1),
        };

        try {
          const resp = await request(
            "GetUniversityAuthEmails",
            pageOptions
          );
          if (resp.RetCode === 0) {
            allRecords = allRecords.concat(resp.Data);
          } else {
            throw new Error(resp.Message || resp.RetCode + "查询失败");
          }
        } catch (err) {
          notification["error"]({
            message: "请求失败",
            description: err.message || "内部错误",
          });
          setLoading(false);
          return;
        }
      }
      return allRecords;
    };
    setDataLoading(false);
    setPagination({
      ...pagination,
      total: res.TotalCount,
    });
    if (res.RetCode === 0) {
      let results = await getAllRecords(res.TotalCount)
      setDataSource(results);
      setOldDataSource(results)
    }
  };
  const searchData = () => {
    return oldDatasource.filter((item) => {
      let match = true;

      if (OrgName && item.OrganizationName !== OrgName) {
        match = false;
      }

      if (EmailSuffix && item.Suffix!==(EmailSuffix)) {
        match = false;
      }
      return match;
    });
  };
  const onSearch = (filters, sorter) => {
    let data = searchData()
    Object.keys(filters).forEach((key) => {
      if (filters[key].length) {
        data = data.filter((el) => filters[key].includes(el[key]));
      }
    });
    data.sort((a, b) => {
      if (sorter.order === "descend") {
        return b[sorter["field"]] - a[sorter["field"]];
      } else if (sorter.order === "ascend") {
        return a[sorter["field"]] - b[sorter["field"]];
      }
    });
    setDataSource(data);
    setPagination({
      current: 1,
      pageSize: 20,
      total: data.length,
      showTotal: total => `共计：${total}条`,
      showQuickJumper: true
    });
  };
  const handleTableChange = (pagination, filters, sorter) => {
    const isFilterSorterChange =
      JSON.stringify(oldFilters) !== JSON.stringify(filters) ||
      JSON.stringify(oldSorter) !== JSON.stringify(sorter);
    if (isFilterSorterChange) {
      // 如果筛选或排序变化，将分页重置为第一页
      onSearch(filters, sorter);
    } else {
      // 否则更新分页器（比如翻页）
      setPagination(pagination);
    }
    setOldFilters(filters);
    setOldSorter(sorter);
  };
  const columns = [
    {
      title: "机构名称",
      dataIndex: "OrganizationName",
      key: "OrganizationName"
    },
    {
      title: "邮箱后缀",
      dataIndex: "Suffix",
      key: "Suffix"
    },
    {
      title: "机构类型",
      dataIndex: "OrganizationType",
      key: "OrganizationType",
      render: (val) => {
        return val === "University" ? "高校" : "研究所"
      },
      filters: [
        {
          text: "高校",
          value: "University",
        },
        {
          text: "研究所",
          value: "ResearchInstitute",
        },
      ],
    },
    {
      title: "所在地区",
      dataIndex: "RegionType",
      key: "RegionType",
      render: (val) => {
        let mp = {
          Mainland: "中国大陆",
          HongKongMacaoTaiwan: "港澳台",
          Oversea: "海外"
        }
        return mp[val] || val
      },
      filters: [
        {
          text: "中国大陆",
          value: "Mainland",
        },
        {
          text: "港澳台",
          value: "HongKongMacaoTaiwan",
        },
        {
          text: "海外",
          value: "Oversea",
        },
      ],
    },
    {
      title: "创建时间",
      dataIndex: "CreateTime",
      key: "CreateTime",
      sorter: true,
      render: val => {
        return moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: "更新时间",
      dataIndex: "UpdateTime",
      key: "UpdateTime",
      render: val => {
        return moment(val * 1000).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <Row>
            <Col span={12}>
              <Button type="primary" onClick={() => {
                setFieldsValue({
                  OrgName: row.OrganizationName,
                  EmailSuffix: row.Suffix,
                  OrgType: row.OrganizationType,
                  Area: row.RegionType,
                  Id: row.Id
                })
                setVisible(true);
                setOperateType("update");

              }}>
                更新
              </Button>
            </Col>
            {/* <Col span={12}>
              <Popconfirm
                title="确定从白名单移除吗?"
                onConfirm={() => {
                  deleteRow({
                    OrgName: row.org_name,
                    EmailSuffix: row.email_suffix,
                    OrgType: row.org_type,
                    Area: row.area,
                  });
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button type="danger" ghost>
                  删除
                </Button>
              </Popconfirm>
            </Col> */}

          </Row>
        );
      },
    },
  ];
  // const deleteRow = async (row) => {
  //   setLoading(true);
  //   try {
  //     let res = await request("UpdateUniversityAuthEmails", {
  //       ...row,
  //     });
  //     if (res.RetCode === 0) {
  //       message.success("删除成功");
  //       GetUniversityAuthEmails()
  //     }
  //   } catch (error) {
  //     console.log("error", error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleSubmit = async () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          let res = await request(operateType === "add" ? "AddUniversityAuthEmails" : "UpdateUniversityAuthEmails", { ...values });
          if (res.RetCode === 0) {
            message.success(operateType === "add"?"添加成功":"更新成功");
            GetUniversityAuthEmails();
            setVisible(false);
            resetFields();
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          setLoading(false);

        }
      }
    });
  };
  const reSetField = () => {
    setOrgName("");
    setEmailSuffix("");
    GetUniversityAuthEmails();
    // setPagination({
    //   current: 1,
    //   pageSize: 20,
    //   total: 0,
    // });
  };
  const renderAdvancedForm = () => {
    return (
      <Form layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={10} key={144444}>
            <FormItem label="机构名称">
              <Input
                style={{ width: "100%" }}
                value={OrgName}
                placeholder="请输入机构名称"
                onChange={(e) => {
                  setOrgName(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
          <Col span={10} key={144445}>
            <FormItem label="邮箱后缀">
              <Input
                style={{ width: "100%" }}
                value={EmailSuffix}
                placeholder="请输入邮箱后缀"
                onChange={(e) => {
                  setEmailSuffix(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={6} key={2}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                style={{ marginRight: "16px" }}
                onClick={() => {
                  onSearch(oldFilters, oldSorter);
                }}
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                onClick={reSetField}
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  };
  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        onSubmit={handleSubmit}
      >
        {getFieldDecorator("Id")(<Input type="hidden" />)}
        <Form.Item label="机构名称">
          {getFieldDecorator("OrgName", {
            rules: [
              {
                required: true,
                message: "机构名称不能为空",
              },
            ],
          })(<Input placeholder="请输入机构名称" />)}
        </Form.Item>
        <Form.Item label="邮箱后缀">
          {getFieldDecorator("EmailSuffix", {
            rules: [
              {
                required: true,
                message: "邮箱后缀不能为空",
              },
            ],
          })(<Input placeholder="请输入邮箱后缀" />)}
        </Form.Item>
        <Form.Item label="机构类型">
          {getFieldDecorator("OrgType", {
            rules: [
              {
                required: true,
                message: "机构类型不能为空",
              },
            ],
          })(
            <Radio.Group >
              <Radio value="University">高校</Radio>
              <Radio value="ResearchInstitute">研究所</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="所在地区">
          {getFieldDecorator("Area", {
            rules: [
              {
                required: true,
                message: "所在地区不能为空",
              },
            ],
          })(
            <Radio.Group >
              <Radio value="Mainland">中国大陆</Radio>
              <Radio value="HongKongMacaoTaiwan">港澳台</Radio>
              <Radio value="Oversea">海外</Radio>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    );
  };
  return (
    <>
      <Card title="搜索" style={{ marginBottom: 24 }} bordered={false}>
        {renderAdvancedForm()}
      </Card>
      <Card style={{ minHeight: "80vh" }}>
        <Row type="flex" justify="space-between" style={{ marginBottom: "30px" }}>
          <Col span={2}>
            <Button
              type="primary"
              onClick={() => {
                resetFields();
                setVisible(true);
                setOperateType("add");
              }}
            >
              新增机构
            </Button>
          </Col>
        </Row>
        <Row>
          <Table dataSource={dataSource} columns={columns} rowKey="Id" onChange={handleTableChange} loading={dataLoading} pagination={pagination} />
        </Row>
        <Modal
          title={operateType === "add" ? "新增机构" : "编辑机构"}
          visible={visible}
          onOk={handleSubmit}
          onCancel={() => {
            setVisible(false);
            resetFields();
          }}
          footer={[
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={handleSubmit}
            >
              提交
            </Button>,
            <Button
              key="back"
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>,
          ]}
        // width={500}
        >
          {addForm()}
        </Modal>
      </Card>
    </>
  );
};

export default Form.create()(UniversityAuthEmail);
