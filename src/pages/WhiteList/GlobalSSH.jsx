import React, { useState, useEffect } from "react";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Modal,
  Form,
  message,
  notification,
  Popconfirm,
  DatePicker,
  InputNumber,
} from "antd";
const { RangePicker } = DatePicker;
import request from "../../utils/request";
import moment from "moment";
const FormItem = Form.Item;
const GlobalSSH = (props) => {
  const { getFieldDecorator, resetFields } = props.form;
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [CompanyId, setCompanyId] = useState("");
  const [StartTime, setBeginTime] = useState(null);
  const [EndTime, setEndTime] = useState(null);
  const [operateType, setOperateType] = useState("add");
  const [oldDatasource, setOldDataSource] = useState([]);
  const [oldFilters, setOldFilters] = useState({});
  const [oldSorter, setOldSorter] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showTotal: (total) => `共计：${total}条`,
    showQuickJumper: true,
    showSizeChanger: true,
  });
  useEffect(() => {
    OperateGlobalSshRdpWhiteList({
      Operate: "Get",
    });
  }, []);
  const OperateGlobalSshRdpWhiteList = async (data) => {
    setDataLoading(true);
    let res = await request("OperateGlobalSshRdpWhiteList", {
      ...data,
    });
    const getAllRecords = async (totalRecords) => {
      let allRecords = [];
      const totalPages = Math.ceil(totalRecords / 1000);

      for (let page = 1; page <= totalPages; page++) {
        const pageOptions = {
          Limit: 1000,
          Offset: 1000 * (page - 1),
          Operate: "Get",
        };

        try {
          const resp = await request(
            "OperateGlobalSshRdpWhiteList",
            pageOptions
          );
          if (resp.RetCode === 0) {
            allRecords = allRecords.concat(resp.Data.globalSshRdpWhiteList);
          } else {
            throw new Error(resp.Message || resp.RetCode + "查询失败");
          }
        } catch (err) {
          notification["error"]({
            message: "请求失败",
            description: err.message || "内部错误",
          });
          setLoading(false);
          return;
        }
      }
      return allRecords.sort((a, b) => b.UpdatedTime - a.UpdatedTime);
    };
    setDataLoading(false);
    setPagination({
      ...pagination,
      total: res.TotalCount,
    });
    if (res.RetCode === 0) {
      let results = await getAllRecords(res.Data.totalRecords);
      setDataSource(results);
      setOldDataSource(results);
    }
  };
  const searchData = () => {
    return oldDatasource.filter((item) => {
      let match = true;

      if (CompanyId && item.CompanyId !== parseInt(CompanyId)) {
        match = false;
      }

      if (StartTime && EndTime) {
        const itemTime = item.UpdatedTime / 1000;
        if (itemTime >= EndTime.unix() || itemTime <= StartTime.unix()) {
          match = false;
        }
      }
      return match;
    });
  };
  const onSearch = (filters, sorter) => {
    let data = searchData();
    Object.keys(filters).forEach((key) => {
      if (filters[key].length) {
        data = data.filter((el) => filters[key].includes(el[key]));
      }
    });
    data.sort((a, b) => {
      if (sorter.order === "descend") {
        return b[sorter["field"]] - a[sorter["field"]];
      } else if (sorter.order === "ascend") {
        return a[sorter["field"]] - b[sorter["field"]];
      }
    });
    setDataSource(data);
    setPagination({
      current: 1,
      pageSize: 20,
      total: data.length,
      showTotal: (total) => `共计：${total}条`,
      showQuickJumper: true,
    });
  };
  const handleTableChange = (pagination, filters, sorter) => {
    const isFilterSorterChange =
      JSON.stringify(oldFilters) !== JSON.stringify(filters) ||
      JSON.stringify(oldSorter) !== JSON.stringify(sorter);
    if (isFilterSorterChange) {
      // 如果筛选或排序变化，将分页重置为第一页
      onSearch(filters, sorter);
    } else {
      // 否则更新分页器（比如翻页）
      setPagination(pagination);
    }
    setOldFilters(filters);
    setOldSorter(sorter);
  };
  const columns = [
    {
      title: "公司ID",
      dataIndex: "CompanyId",
      key: "CompanyId",
    },
    {
      title: "操作人",
      dataIndex: "SsoUser",
      key: "SsoUser",
    },
    {
      title: "备注",
      dataIndex: "Remark",
      key: "Remark",
    },
    {
      title: "创建时间",
      dataIndex: "CreatedTime",
      key: "CreatedTime",
      sorter: (a, b) => b.CreatedTime.length - a.CreatedTime.length,
      render: (val) => {
        return moment(val).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "更新时间",
      dataIndex: "UpdatedTime",
      key: "UpdatedTime",
      sorter: true,
      render: (val) => {
        return moment(val).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <Row>
            <Col span={12}>
              <Popconfirm
                title="确定从白名单移除吗?"
                onConfirm={() => {
                  deleteRow({
                    CompanyId: row.CompanyId,
                    Operate: "Delete",
                  });
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button type="danger" ghost>
                  删除
                </Button>
              </Popconfirm>
            </Col>
          </Row>
        );
      },
    },
  ];
  const deleteRow = async (row) => {
    setLoading(true);
    try {
      let res = await request("OperateGlobalSshRdpWhiteList", {
        ...row,
      });
      if (res.RetCode === 0) {
        message.success("删除成功");
        OperateGlobalSshRdpWhiteList({
          Operate: "Get",
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          let res = await request("OperateGlobalSshRdpWhiteList", {
            ...values,
            Operate: "Add",
          });
          if (res.RetCode === 0) {
            message.success("添加成功");
            OperateGlobalSshRdpWhiteList({
              Operate: "Get",
            });
            setVisible(false);
            resetFields();
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          setLoading(false);
        }
      }
    });
  };
  const reSetField = () => {
    setCompanyId("");
    setBeginTime(null);
    setEndTime(null);
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0,
    });
    setDataSource(oldDatasource);
  };
  const renderAdvancedForm = () => {
    return (
      <Form
        layout="inline"
        className="ant-advanced-search-form"
        style={{ marginBottom: 0 }}
      >
        <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144443}>
            <FormItem label="公司ID">
              <Input
                style={{ width: "100%" }}
                value={CompanyId}
                placeholder="请输入对应的公司ID"
                onChange={(e) => {
                  setCompanyId(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
          <Col span={12} key={144664}>
            <FormItem label="时间">
              <RangePicker
                style={{ width: "100%" }}
                ranges={{
                  Today: [moment().startOf("day"), moment().endOf("day")],
                  "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                  ],
                }}
                showTime={{
                  defaultValue: [
                    moment("00:00:00", "HH:mm:ss"),
                    moment("23:59:59", "HH:mm:ss"),
                  ],
                }}
                format="YYYY-MM-DD"
                value={[StartTime, EndTime]}
                onChange={(v) => {
                  if (v.length === 0) {
                    setBeginTime(null);
                    setEndTime(null);
                    return;
                  }
                  //选择同一天时，默认设置为0点-23点59
                  if (v[0].unix() === v[1].unix()) {
                    v[0] = v[0].startOf("day");
                    v[1] = v[1].endOf("day");
                  }
                  setBeginTime(v[0]);
                  setEndTime(v[1]);
                }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
          <Col span={6} key={2} offset={6}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                type="primary"
                style={{ marginRight: "16px" }}
                onClick={() => {
                  onSearch(oldFilters, oldSorter);
                }}
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  reSetField();
                }}
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  };
  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        onSubmit={handleSubmit}
      >
        {getFieldDecorator("CompanyId")(<Input type="hidden" />)}
        <Form.Item label="公司ID">
          {getFieldDecorator("CompanyId", {
            rules: [
              {
                required: true,
                message: "公司ID不能为空",
              },
            ],
          })(<InputNumber placeholder="请输入公司ID" />)}
        </Form.Item>
        <Form.Item label="备注">
          {getFieldDecorator("Remark", {
            rules: [
              {
                required: true,
                message: "备注不能为空",
              },
            ],
          })(<Input placeholder="请输入备注" />)}
        </Form.Item>
      </Form>
    );
  };
  return (
    <>
      <Card title="搜索" style={{ marginBottom: 24 }} bordered={false}>
        {renderAdvancedForm()}
      </Card>
      <Card style={{ minHeight: "80vh" }}>
        <Row
          type="flex"
          justify="space-between"
          style={{ marginBottom: "30px" }}
        >
          <Col span={2}>
            <Button
              type="primary"
              onClick={() => {
                resetFields();
                setVisible(true);
                setOperateType("add");
              }}
            >
              新增
            </Button>
          </Col>
        </Row>
        <Row>
          <Table
            dataSource={dataSource}
            columns={columns}
            rowKey="CompanyId"
            onChange={handleTableChange}
            loading={dataLoading}
            pagination={pagination}
          />
        </Row>
        <Modal
          title={operateType === "add" ? "新增白名单" : "编辑机构"}
          visible={visible}
          onOk={handleSubmit}
          onCancel={() => {
            setVisible(false);
            resetFields();
          }}
          footer={[
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={handleSubmit}
            >
              提交
            </Button>,
            <Button
              key="back"
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>,
          ]}
          // width={500}
        >
          {addForm()}
        </Modal>
      </Card>
    </>
  );
};

export default Form.create()(GlobalSSH);
