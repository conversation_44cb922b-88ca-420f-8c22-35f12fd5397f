import React, { useState, useEffect } from "react";
import moment from "moment";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Popconfirm,
  Icon,
  Modal,
  Form,
  Select,
  message,
  InputNumber,
} from "antd";
import request from "../../utils/request";
const { Option } = Select;
const { Search } = Input;
const { TextArea } = Input;

const PhoneNumberBindingCount = (props) => {
  const { getFieldDecorator, getFieldValue, resetFields, setFieldsValue } =
    props.form;
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [oldDatasource, setOldDataSource] = useState([]);
  const CHANNELINFO = [
    {
      ChannelID: 1,
      ChannelName: "ucloud",
      Title: "UCloud",
      Description: "UCloud",
      ZoneID: "all",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api.ucloud.cn",
      IntranetAPIDomain: "api.service.ucloud.cn",
      Support: "<EMAIL>",
      Website: "https://www.ucloud.cn",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        MainLang: "zh_CN",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.ucloud.cn",
        GeeTest: true,
        RealNameURL: "https://accountv2.ucloud.cn/authentication",
        SSOSignURL: "https://signin.ucloud.cn",
        ResetPasswordURL: "https://passport.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport.ucloud.cn/activeResult",
        ActiveSubAccountURL: "https://passport.ucloud.cn/activeSubaccount",
      },
    },
    {
      ChannelID: 11,
      ChannelName: "vip",
      Title: "UCloud企业事业部VIP",
      Description: "UCloud企业事业部VIP",
      ZoneID: "all",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api.vip.ucloud.cn",
      IntranetAPIDomain: "api.vip.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "",
        ActiveAccountURL: "",
        ActiveSubAccountURL: "",
        RealNameURL: "",
        OAuthRedirectURL: "",
      },
    },
    {
      ChannelID: 12,
      ChannelName: "poc",
      Title: "UCloud企业事业部POC",
      Description: "UCloud企业事业部POC",
      ZoneID: "all",
      ChannelDomain: "ucloud.cn",
      APIDomain: "poc-api.ucloud.cn",
      IntranetAPIDomain: "poc-api.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ActiveAccountURL: "",
        ActiveSubAccountURL: "",
        RealNameURL: "",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "",
      },
    },
    {
      ChannelID: 13,
      ChannelName: "ibu",
      Title: "UCloud互联网事业部IBU",
      Description: "UCloud互联网事业部IBU",
      ZoneID: "all",
      ChannelDomain: "ucloud.cn",
      APIDomain: "ibu-api.ucloud.cn",
      IntranetAPIDomain: "ibu-api.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        MainLang: "zh_CN",
        ResetPasswordURL: "",
        ActiveAccountURL: "",
        ActiveSubAccountURL: "",
        RealNameURL: "",
        OAuthRedirectURL: "",
        GeeTest: false,
      },
    },
    {
      ChannelID: 101,
      ChannelName: "inspur",
      Title: "浪潮云",
      Description: "浪潮云",
      ZoneID: "110100",
      ChannelDomain: "cloud.inspur.com",
      APIDomain: "api.cloud.inspur.com",
      IntranetAPIDomain: "api.service.cloud.inspur.com",
      Support: "<EMAIL>",
      Website: "http://cloud.inspur.com",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://login.cloud.inspur.com/retrieveInput",
        ActiveAccountURL: "https://login.cloud.inspur.com/cas/emailactivation",
        ActiveSubAccountURL: "https://cloud.inspur.com/activeSubaccount",
        RealNameURL: "https://cloud.inspur.com/certify.certification.dhtml",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 102,
      ChannelName: "wonders",
      Title: "万达信息",
      Description: "万达信息",
      ZoneID: "1,1001,2001,3001,3002,4001,5001,6001,7001,8001,8100,9001,9002",
      ChannelDomain: "wonderscloud.com",
      APIDomain: "api.wonderscloud.com",
      IntranetAPIDomain: "api.service.wonderscloud.com",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "",
        ActiveAccountURL: "",
        ActiveSubAccountURL: "",
        RealNameURL: "",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 103,
      ChannelName: "sanqin",
      Title: "三秦云",
      Description: "三秦云",
      ZoneID: "8001,8100",
      ChannelDomain: "ucloud.sanqincloud.com",
      APIDomain: "api.ucloud.sanqincloud.com",
      IntranetAPIDomain: "api.service.ucloud.sanqincloud.com",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        MainLang: "zh_CN",
        ResetPasswordURL: "",
        ActiveAccountURL: "",
        ActiveSubAccountURL: "",
        RealNameURL: "",
        OAuthRedirectURL: "",
        GeeTest: false,
      },
    },
    {
      ChannelID: 104,
      ChannelName: "edu",
      Title: "教育云",
      Description: "教育云",
      ZoneID:
        "1,1001,2001,3001,3002,4001,5001,6001,7001,8001,8100,8200,9001,9002,10001,10002,10003,10004,10005,10006,10007,10008,10009,10010",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-edu.ucloud.cn",
      IntranetAPIDomain: "api-edu.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport-edu.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport-edu.ucloud.cn/activeResult",
        ActiveSubAccountURL: "https://passport-edu.ucloud.cn/activeSubaccount",
        RealNameURL: "https://account-edu.ucloud.cn/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 105,
      ChannelName: "ztf",
      Title: "中通服",
      Description: "中通服",
      ZoneID: "10012",
      ChannelDomain: "ztfgzcps.com",
      APIDomain: "api.ztfgzcps.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.ztfgzcps.com/retrieveInput",
        ActiveAccountURL: "https://passport.ztfgzcps.com/activeResult",
        ActiveSubAccountURL: "https://passport.ztfgzcps.com/activeSubaccount",
        RealNameURL: "https://account.ztfgzcps.com/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 106,
      ChannelName: "demo",
      Title: "奥飞云",
      Description: "奥飞云",
      ZoneID: "8100,8200,8300,9001,9002",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-demo.ucloud.cn",
      IntranetAPIDomain: "api-demo.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport-demo.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport-demo.ucloud.cn/activeResult",
        ActiveSubAccountURL: "https://passport-demo.ucloud.cn/activeSubaccount",
        RealNameURL: "https://account-demo.ucloud.cn/authentication",
        OAuthRedirectURL: "",
      },
    },
    {
      ChannelID: 107,
      ChannelName: "fii",
      Title: "富士康",
      Description: "富士康",
      ZoneID: "8100,8200,8300,4001,5001,9001,9002",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-fii.ucloud.cn",
      IntranetAPIDomain: "api-fii.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport-fii.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport-fii.ucloud.cn/activeResult",
        ActiveSubAccountURL: "https://passport-fii.ucloud.cn/activeSubaccount",
        RealNameURL: "https://account-fii.ucloud.cn/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 108,
      ChannelName: "unisound",
      Title: "厦门云知芯",
      Description: "厦门云知芯",
      ZoneID: "7001",
      ChannelDomain: "auto-ai.com.cn",
      APIDomain: "api.auto-ai.com.cn",
      IntranetAPIDomain: "api.service.auto-ai.com.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.auto-ai.com.cn/retrieveInput",
        ActiveAccountURL: "https://passport.auto-ai.com.cn/activeResult",
        ActiveSubAccountURL: "https://passport.auto-ai.com.cn/activeSubaccount",
        RealNameURL: "https://account.auto-ai.com.cn/authentication/entry",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 109,
      ChannelName: "travelsky",
      Title: "航信云",
      Description: "航信云",
      ZoneID: "8100,8200,8300,9001,9002",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-travelsky.ucloud.cn",
      IntranetAPIDomain: "api.service.travelskycloud.com",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.travelskycloud.com/retrieveInput",
        ActiveAccountURL: "https://passport.travelskycloud.com/activeResult",
        ActiveSubAccountURL:
          "https://passport.travelskycloud.com/activeSubaccount",
        RealNameURL: "https://account.travelskycloud.com/authentication/entry",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 110,
      ChannelName: "hn06",
      Title: "专有云测试",
      Description: "专有云测试",
      ZoneID: "7101",
      ChannelDomain: "upc-poc.cn",
      APIDomain: "api.upc-poc.cn",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        RealNameURL: "https://account.upc-poc.cn/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.upc-poc.cn/retrieveInput",
        ActiveAccountURL: "https://passport.upc-poc.cn/activeResult",
        ActiveSubAccountURL: "https://passport.upc-poc.cn/activeSubaccount",
      },
    },
    {
      ChannelID: 111,
      ChannelName: "minghe",
      Title: "明合云",
      Description: "明合云",
      ZoneID: "9002,8300,7001",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-minghe.ucloud.cn",
      IntranetAPIDomain: "api-minghe.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport-minghe.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport-minghe.ucloud.cn/activeResult",
        ActiveSubAccountURL:
          "https://passport-minghe.ucloud.cn/activeSubaccount",
        RealNameURL: "https://account-minghe.ucloud.cn/authentication",
      },
    },
    {
      ChannelID: 112,
      ChannelName: "10086",
      Title: "上海移动",
      Description: "上海移动",
      ZoneID: "8100,8200,8300",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-10086.ucloud.cn",
      IntranetAPIDomain: "api-10086.service.ucloud.cn",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport-10086.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport-10086.ucloud.cn/activeResult",
        ActiveSubAccountURL:
          "https://passport-10086.ucloud.cn/activeSubaccount",
        RealNameURL: "https://account-10086.ucloud.cn/authentication",
        OAuthRedirectURL: "",
      },
    },
    {
      ChannelID: 113,
      ChannelName: "cibfintech",
      Title: "兴业数金",
      Description: "兴业数金",
      ZoneID: "10022",
      ChannelDomain: "cloudx.cibfintech.com",
      APIDomain: "api.cloudx.cibfintech.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        RealNameURL: "https://account.cloudx.cibfintech.com/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL:
          "https://passport.cloudx.cibfintech.com/retrieveInput",
        ActiveAccountURL: "https://passport.cloudx.cibfintech.com/activeResult",
        ActiveSubAccountURL:
          "https://passport.cloudx.cibfintech.com/activeSubaccount",
      },
    },
    {
      ChannelID: 114,
      ChannelName: "dxconsole",
      Title: "联云易通",
      Description: "联云易通",
      ZoneID:
        "9001,9002,8200,8300,6001,10009,3001,3002,10002,10005,10008,10004",
      ChannelDomain: "dxconsole.com",
      APIDomain: "api.dxconsole.com",
      IntranetAPIDomain: "api.service.dxconsole.com",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ActiveSubAccountURL: "https://passport.dxconsole.com/activeSubaccount",
        RealNameURL: "https://account.dxconsole.com/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.dxconsole.com/retrieveInput",
        ActiveAccountURL: "https://passport.dxconsole.com/activeResult",
      },
    },
    {
      ChannelID: 115,
      ChannelName: "cmccit",
      Title: "中国移动",
      Description: "中国移动",
      ZoneID: "10023,10024",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-cmccit.ucloud.cn",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        RealNameURL: "",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "",
        ActiveAccountURL: "",
        ActiveSubAccountURL: "",
      },
    },
    {
      ChannelID: 116,
      ChannelName: "internal",
      Title: "内网Console控制台",
      Description: "内网Console控制台",
      ZoneID: "4001,5001,9001,9002",
      ChannelDomain: "ucloudadmin.com",
      APIDomain: "api-inner.ucloudadmin.com",
      IntranetAPIDomain: "api-inner.service.ucloudadmin.com",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ActiveAccountURL: "https://passport.ucloudadmin.com/activeResult",
        ActiveSubAccountURL:
          "https://passport.ucloudadmin.com/activeSubaccount",
        RealNameURL: "https://account.ucloudadmin.com/authentication",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.ucloudadmin.com/retrieveInput",
      },
    },
    {
      ChannelID: 117,
      ChannelName: "cercloud",
      Title: "赛尔教育云",
      Description: "赛尔教育云",
      ZoneID: "3001,3002,6001,8001,8200,8300,9002,10009",
      ChannelDomain: "cercloud.net",
      APIDomain: "api.cercloud.net",
      IntranetAPIDomain: "api.service.cercloud.net",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.cercloud.net/retrieveInput",
        ActiveAccountURL: "https://passport.cercloud.net/activeResult",
        ActiveSubAccountURL: "https://passport.cercloud.net/activeSubaccount",
        RealNameURL: "https://account.cercloud.net/authentication/entry",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 118,
      ChannelName: "cmcc-iot",
      Title: "中移物联网",
      Description: "中移物联网",
      ZoneID: "all",
      ChannelDomain: "iot-10086.ucloud.cn",
      APIDomain: "api.iot-10086.ucloud.cn",
      IntranetAPIDomain: "api.service.iot-10086.ucloud.cn",
      Support: "<EMAIL>",
      Website: "https://www.ucloud.cn",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ActiveSubAccountURL:
          "https://passport.iot-10086.ucloud.cn/activeSubaccount",
        RealNameURL: "https://account.iot-10086.ucloud.cn/authentication/entry",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.iot-10086.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport.iot-10086.ucloud.cn/activeResult",
      },
    },
    {
      ChannelID: 119,
      ChannelName: "ecloud-gdu",
      Title: "移动云U",
      Description: "移动云U",
      ZoneID: "9001,9002,8200,8300,7001",
      ChannelDomain: "ecloud-gdu.com",
      APIDomain: "api.ecloud-gdu.com",
      IntranetAPIDomain: "api.service.ecloud-gdu.com",
      Support: "",
      Website: "http://www.ecloud-gdu.com",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.ecloud-gdu.com/retrieveInput",
        ActiveAccountURL: "https://passport.ecloud-gdu.com/activeResult",
        ActiveSubAccountURL: "https://passport.ecloud-gdu.com/activeSubaccount",
        RealNameURL: "https://account.ecloud-gdu.com/authentication/entry",
      },
    },
    {
      ChannelID: 120,
      ChannelName: "pre2",
      Title: "预发布2",
      Description: "新版预发布环境",
      ZoneID: "1200201",
      ChannelDomain: "pre.ucloudadmin.com",
      APIDomain: "api-pre2.pre.ucloudadmin.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        MainLang: "zh_CN",
        ResetPasswordURL:
          "https://passport-pre2.pre.ucloudadmin.com/retrieveInput",
        ActiveAccountURL:
          "https://passport-pre2.pre.ucloudadmin.com/activeResult",
        ActiveSubAccountURL:
          "https://passport-pre2.pre.ucloudadmin.com/activeSubaccount",
        RealNameURL:
          "https://account-pre2.pre.ucloudadmin.com/authentication/entry",
        OAuthRedirectURL: "",
        GeeTest: false,
      },
    },
    {
      ChannelID: 121,
      ChannelName: "scloud",
      Title: "SCloud",
      Description: "新加坡合资公司SCloud",
      ZoneID: "10009,3001,3002,10002,10005,10004,10008,10003,10018,10011,10013",
      ChannelDomain: "scloud.sg",
      APIDomain: "api.scloud.sg",
      IntranetAPIDomain: "",
      Support: "",
      Website: "http://www.scloud.sg",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.scloud.sg/retrieveInput",
        ActiveAccountURL: "https://passport.scloud.sg/activeResult",
        ActiveSubAccountURL: "https://passport.scloud.sg/activeSubaccount",
        RealNameURL: "https://account.scloud.sg/authentication/entry",
        OAuthRedirectURL: "",
        GeeTest: false,
        MainLang: "en_US",
        ConsoleURL: "https://console.scloud.sg",
        SSOSignURL: "",
      },
    },
    {
      ChannelID: 122,
      ChannelName: "cybree",
      Title: "CyBree",
      Description: "海外站",
      ZoneID: "all",
      ChannelDomain: "cybree.com",
      APIDomain: "api.cybree.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "http://www.cybree.com",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ActiveSubAccountURL: "",
        ConsoleURL: "https://console.cybree.com",
        SSOSignURL: "",
        GeeTest: true,
        MainLang: "en_US",
        PhoneRequired: false,
        ResetPasswordURL: "https://passport.cybree.com/reset",
        ActiveAccountURL: "https://passport.cybree.com/activate",
        RealNameURL: "",
        OAuthRedirectURL: "https://passport.cybree.com/oauth",
      },
    },
    {
      ChannelID: 123,
      ChannelName: "cloudwasu",
      Title: "华数云",
      Description: "华数云",
      ZoneID: "all",
      ChannelDomain: "ucloud.cn",
      APIDomain: "api-cloudwasu.ucloud.cn",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: 0,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport-cloudwasu.ucloud.cn/retrieveInput",
        ActiveAccountURL: "https://passport-cloudwasu.ucloud.cn/activeResult",
        RealNameURL:
          "https://console-cloudwasu.ucloud.cn/uaccount/authentication",
        ConsoleURL: "https://console-cloudwasu.ucloud.cn",
        MainLang: "zh_CN",
        ActiveSubAccountURL:
          "https://passport-cloudwasu.ucloud.cn/activeSubaccount",
        OAuthRedirectURL: "",
        SSOSignURL: "",
        GeeTest: true,
      },
    },
    {
      ChannelID: 124,
      ChannelName: "compshare",
      Title: "算力共享平台",
      Description: "算力共享平台",
      ZoneID: "10027,4001,9002,8100,8200,8300,7001",
      ChannelDomain: "compshare.cn",
      APIDomain: "api.compshare.cn",
      IntranetAPIDomain: "",
      Support: "",
      Website: "http://www.compshare.cn",
      CreatedAt: 0,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.compshare.cn/retrieveInput",
        RealNameURL: "https://console.compshare.cn/uaccount/authentication",
        ConsoleURL: "https://console.compshare.cn",
        GeeTest: true,
        ActiveAccountURL: "https://passport.compshare.cn/activeResult",
        ActiveSubAccountURL: "https://passport.compshare.cn/activeSubaccount",
        OAuthRedirectURL: "",
        SSOSignURL: "",
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 125,
      ChannelName: "picpik",
      Title: "PICPIK",
      Description: "PICPIK 国际站",
      ZoneID: "all",
      ChannelDomain: "picpik.ai",
      APIDomain: "api.picpik.ai",
      IntranetAPIDomain: "",
      Support: "",
      Website: "http://www.picpik.ai",
      CreatedAt: 0,
      UpdatedAt: **********,
      ExtendData: {
        ActiveSubAccountURL: "https://passport.picpik.ai/activeSubaccount",
        RealNameURL: "https://console.picpik.ai/uaccount/authentication",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.picpik.ai",
        ResetPasswordURL: "https://passport.picpik.ai/retrieveInput",
        ActiveAccountURL: "https://passport.picpik.ai/activeResult",
        SSOSignURL: "",
        GeeTest: true,
        MainLang: "en_US",
      },
    },
    {
      ChannelID: 126,
      ChannelName: "UCloud Global",
      Title: "UCloud Global",
      Description: "UCloud 海外站",
      ZoneID:
        "10001,10002,10003,10004,10005,10008,10009,10010,10011,10013,10015,10016,10017,10018,10025,10026,10028",
      ChannelDomain: "ucloud-global.com",
      APIDomain: "api.ucloud-global.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "http://www.ucloud-global.com",
      CreatedAt: 0,
      UpdatedAt: **********,
      ExtendData: {
        PhoneRequired: true,
        ResetPasswordURL: "https://passport.ucloud-global.com/retrieveInput",
        RealNameURL:
          "https://console.ucloud-global.com/uaccount/authentication",
        ConsoleURL: "https://console.ucloud-global.com",
        SSOSignURL: "",
        GeeTest: true,
        ActiveAccountURL: "https://passport.ucloud-global.com/activeResult",
        ActiveSubAccountURL:
          "https://passport.ucloud-global.com/activeSubaccount",
        OAuthRedirectURL: "",
        MainLang: "en_US",
      },
    },
    {
      ChannelID: 127,
      ChannelName: "UCDC-test",
      Title: "UCloud专属云测试",
      Description: "虚商测试控制台",
      ZoneID: "10027",
      ChannelDomain: "ucdctest.com",
      APIDomain: "api.ucdctest.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://www.ucdctest.com",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.ucdctest.com/retrieveInput",
        ActiveAccountURL: "https://passport.ucdctest.com/activeResult",
        ConsoleURL: "https://console.ucdctest.com",
        SSOSignURL: "",
        MainLang: "zh_CN",
        ActiveSubAccountURL: "https://passport.ucdctest.com/activeSubaccount",
        RealNameURL: "https://console.ucdctest.com/uaccount/authentication",
        OAuthRedirectURL: "",
        GeeTest: true,
      },
    },
    {
      ChannelID: 128,
      ChannelName: "surfercloud",
      Title: "surfercloud",
      Description: "surfercloud国际站",
      ZoneID: "",
      ChannelDomain: "surfercloud.com",
      APIDomain: "api.surfercloud.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "http://www.surfercloud.com",
      CreatedAt: 0,
      UpdatedAt: **********,
      ExtendData: {
        ResetPasswordURL: "https://passport.surfercloud.com/retrieveInput",
        ActiveAccountURL: "https://passport.surfercloud.com/activeResult",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.surfercloud.com",
        PhoneRequired: true,
        ActiveSubAccountURL:
          "https://passport.surfercloud.com/activeSubaccount",
        RealNameURL: "https://console.surfercloud.com/uaccount/authentication",
        SSOSignURL: "",
        GeeTest: true,
        MainLang: "en_US",
      },
    },
    {
      ChannelID: 129,
      ChannelName: "Xinnet",
      Title: "新网数码",
      Description: "新网数码",
      ZoneID: "",
      ChannelDomain: "xincache.cn",
      APIDomain: "api-xcloud.xincache.cn",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console-xcloud.xincache.cn",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        RealNameURL:
          "https://console-xcloud.xincache.cn/uaccount/authentication",
        OAuthRedirectURL: "",
        SSOSignURL: "",
        ResetPasswordURL: "https://passport-xcloud.xincache.cn/retrieveInput",
        ActiveAccountURL: "https://passport-xcloud.xincache.cn/activeResult",
        ActiveSubAccountURL:
          "https://passport-xcloud.xincache.cn/activeSubaccount",
        ConsoleURL: "https://console-xcloud.xincache.cn",
        GeeTest: true,
        MainLang: "zh_CN",
      },
    },
    {
      ChannelID: 130,
      ChannelName: "cosmos ucloud",
      Title: "云汉专属云体验",
      Description: "云汉专属云体验",
      ZoneID: "",
      ChannelDomain: "cosmos-ucloud.com.cn",
      APIDomain: "api.cosmos-ucloud.com.cn",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.cosmos-ucloud.com.cn",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        OAuthRedirectURL: "",
        GeeTest: true,
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.cosmos-ucloud.com.cn/retrieveInput",
        ActiveAccountURL: "https://passport.cosmos-ucloud.com.cn/activeResult",
        ActiveSubAccountURL:
          "https://passport.cosmos-ucloud.com.cn/activeSubaccount",
        RealNameURL:
          "https://console.cosmos-ucloud.com.cn/uaccount/authentication",
        ConsoleURL: "https://console.cosmos-ucloud.com.cn",
        SSOSignURL: "",
      },
    },
    {
      ChannelID: 131,
      ChannelName: "picpikai",
      Title: "PICPIK 中国站",
      Description: "PICPIK 中国站",
      ZoneID: "",
      ChannelDomain: "picpikai.com",
      APIDomain: "api.picpikai.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.picpikai.com",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        ActiveAccountURL: "https://passport.picpikai.com/activeResult",
        ActiveSubAccountURL: "https://passport.picpikai.com/activeSubaccount",
        RealNameURL: "https://console.picpikai.com/uaccount/authentication",
        GeeTest: true,
        ResetPasswordURL: "https://passport.picpikai.com/retrieveInput",
        ConsoleURL: "https://console.picpikai.com",
        SSOSignURL: "",
        MainLang: "zh_CN",
        OAuthRedirectURL: "",
      },
    },
    {
      ChannelID: 132,
      ChannelName: "Guangzhou Touchpoint Information Technology Co., Ltd.",
      Title: "点动科技",
      Description: "广州点动信息科技股份有限公司",
      ZoneID: "",
      ChannelDomain: "cloud.dd1010.com",
      APIDomain: "api.cloud.dd1010.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.cloud.dd1010.com",
      CreatedAt: **********,
      UpdatedAt: 0,
      ExtendData: {
        RealNameURL: "https://console.cloud.dd1010.com/uaccount/authentication",
        ConsoleURL: "https://console.cloud.dd1010.com",
        GeeTest: true,
        SSOSignURL: "",
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.cloud.dd1010.com/retrieveInput",
        ActiveAccountURL: "https://passport.cloud.dd1010.com/activeResult",
        ActiveSubAccountURL:
          "https://passport.cloud.dd1010.com/activeSubaccount",
        OAuthRedirectURL: "",
      },
    },
    {
      ChannelID: 133,
      ChannelName: "ucdctest-intl",
      Title: "ucdctest-intl",
      Description: "海外专属云测试",
      ZoneID: "",
      ChannelDomain: "ucdctest-intl.com",
      APIDomain: "api.ucdctest-intl.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.ucdctest-intl.com",
      CreatedAt: **********,
      UpdatedAt: 0,
      ExtendData: {
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.ucdctest-intl.com",
        GeeTest: true,
        MainLang: "en_US",
        ActiveSubAccountURL:
          "https://passport.ucdctest-intl.com/activeSubaccount",
        RealNameURL:
          "https://console.ucdctest-intl.com/uaccount/authentication",
        SSOSignURL: "",
        ResetPasswordURL: "https://passport.ucdctest-intl.com/retrieveInput",
        ActiveAccountURL: "https://passport.ucdctest-intl.com/activeResult",
      },
    },
    {
      ChannelID: 134,
      ChannelName: "SCloud USD",
      Title: "SCloud USD",
      Description: "SCloud USD",
      ZoneID: "",
      ChannelDomain: "altuscloud.sg",
      APIDomain: "api.altuscloud.sg",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.altuscloud.sg",
      CreatedAt: **********,
      UpdatedAt: 0,
      ExtendData: {
        ResetPasswordURL: "https://passport.altuscloud.sg/retrieveInput",
        ActiveSubAccountURL: "https://passport.altuscloud.sg/activeSubaccount",
        RealNameURL: "https://console.altuscloud.sg/uaccount/authentication",
        SSOSignURL: "",
        GeeTest: true,
        ActiveAccountURL: "https://passport.altuscloud.sg/activeResult",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.altuscloud.sg",
        MainLang: "en_US",
      },
    },
    {
      ChannelID: 135,
      ChannelName: "ZZCloud",
      Title: "北京蛛蛛信息技术有限公司",
      Description: "北京蛛蛛信息技术有限公司 专属云",
      ZoneID: "",
      ChannelDomain: "bjzzxxjs.com",
      APIDomain: "api.bjzzxxjs.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.bjzzxxjs.com",
      CreatedAt: **********,
      UpdatedAt: 0,
      ExtendData: {
        ActiveAccountURL: "https://passport.bjzzxxjs.com/activeResult",
        RealNameURL: "https://console.bjzzxxjs.com/uaccount/authentication",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.bjzzxxjs.com",
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.bjzzxxjs.com/retrieveInput",
        ActiveSubAccountURL: "https://passport.bjzzxxjs.com/activeSubaccount",
        SSOSignURL: "",
        GeeTest: true,
      },
    },
    {
      ChannelID: 136,
      ChannelName: "Suanova",
      Title: "上海算丰信息有限公司",
      Description: "上海算丰信息有限公司 专属云",
      ZoneID: "",
      ChannelDomain: "usuanova.com",
      APIDomain: "api.usuanova.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.usuanova.com",
      CreatedAt: **********,
      UpdatedAt: **********,
      ExtendData: {
        RealNameURL: "https://console.usuanova.com/uaccount/authentication",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.usuanova.com",
        GeeTest: true,
        ActiveAccountURL: "https://passport.usuanova.com/activeResult",
        ActiveSubAccountURL: "https://passport.usuanova.com/activeSubaccount",
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.usuanova.com/retrieveInput",
        SSOSignURL: "",
      },
    },
    {
      ChannelID: 137,
      ChannelName: "SigcalCloud",
      Title: "希凱雲國際（香港）有限公司",
      Description: "希凱雲國際（香港）有限公司 专属云",
      ZoneID: "",
      ChannelDomain: "sigcalcloud.com",
      APIDomain: "api.sigcalcloud.com",
      IntranetAPIDomain: "",
      Support: "",
      Website: "https://console.sigcalcloud.com",
      CreatedAt: **********,
      UpdatedAt: 0,
      ExtendData: {
        MainLang: "zh_CN",
        ResetPasswordURL: "https://passport.sigcalcloud.com/retrieveInput",
        SSOSignURL: "",
        RealNameURL: "https://console.sigcalcloud.com/uaccount/authentication",
        OAuthRedirectURL: "",
        ConsoleURL: "https://console.sigcalcloud.com",
        GeeTest: true,
        PhoneRequired: true,
        ActiveAccountURL: "https://passport.sigcalcloud.com/activeResult",
        ActiveSubAccountURL:
          "https://passport.sigcalcloud.com/activeSubaccount",
      },
    },
    {
      ChannelID: 666,
      ChannelName: "pre03",
      Title: "预发布03",
      Description: "预发布03渠道",
      ZoneID: "*********",
      ChannelDomain: "pre.ucloudadmin.com",
      APIDomain: "",
      IntranetAPIDomain: "",
      Support: "",
      Website: "",
      CreatedAt: **********,
      UpdatedAt: 0,
      ExtendData: {},
    },
  ];
  let channelMap = {};
  CHANNELINFO.forEach((el) => {
    channelMap[el.ChannelID] = el.Description;
  });
  useEffect(() => {
    GetPhoneWhiteList();
  }, [loading]);

  const GetPhoneWhiteList = async () => {
    let res = await request("GetPhoneWhiteList");
    if (res.RetCode === 0) {
      setDataSource(res.Data);
      setOldDataSource(res.Data);
    }
  };
  const onSearch = (value) => {
    let findObj = dataSource.filter((item) => item.phone === value);
    setDataSource(findObj);
  };
  const handleSearchChange = (e) => {
    if (!e.target.value) {
      setDataSource(oldDatasource);
    }
  };
  const columns = [
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "渠道",
      dataIndex: "channel_id",
      key: "CompanyName",
      render: (val) => channelMap[val],
    },
    {
      title: "可绑定主账号上限数",
      dataIndex: "max_count",
      key: "max_count",
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
    },
    {
      title: "更新时间",
      dataIndex: "update_time",
      key: "update_time",
      defaultSortOrder: "descend",
      sorter: (a, b) => a.update_time - b.update_time,
      render: (val) => (
        <span>
          {val ? moment(val * 1000).format("YYYY-MM-DD HH:mm:ss") : ""}
        </span>
      ),
    },
    {
      title: "操作人",
      dataIndex: "operator",
      key: "operator",
    },
    {
      title: "操作",
      dataIndex: "operate",
      key: "operate",
      render: (_, row) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: "10px" }}
              onClick={() => {
                setFieldsValue({
                  Phone: row.phone,
                  Channel: row.channel_id,
                  MaxCount: row.max_count,
                  Remark: row.remark,
                  Id: row.id,
                });
                setUpdating(true);
                setVisible(true);
              }}
            >
              更新
            </Button>
            <Popconfirm
              title="确定从白名单移除吗?"
              onConfirm={() => {
                deleteRow(row);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="danger" ghost>删除</Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];
  const deleteRow = async (row) => {
    setLoading(true);
    try {
      let res = await request("DeletePhoneWhiteList", {
        Id: row.id,
      });
      if (res.RetCode === 0) {
        message.success("删除成功");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleOk = () => {
    handleSubmit();
  };

  const handleSubmit = async () => {
    props.form.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          let res = await request(
            updating ? "UpdatePhoneWhiteList" : "AddPhoneWhiteList",
            values
          );
          if (res.RetCode === 0) {
            message.success(updating ? "更新成功" : "添加成功");
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          setVisible(false);
          setLoading(false);
          resetFields();
        }
      }
    });
  };

  const addForm = () => {
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        onSubmit={handleSubmit}
      >
        <Form.Item label="Id" style={{ display: "none" }}>
          {getFieldDecorator("Id")(<></>)}
        </Form.Item>
        <Form.Item label="手机号">
          {getFieldDecorator("Phone", {
            rules: [
              {
                required: true,
                message: "请输入手机号",
              },
            ],
          })(!updating ? <Input placeholder="请输入手机号,例(86)13800088000"/> : <div>{getFieldValue("Phone")}</div>)}
        </Form.Item>
        <Form.Item label="渠道">
          {getFieldDecorator("Channel", {
            rules: [
              {
                required: true,
                message: "请选择渠道",
              },
            ],
          })(
            <Select disabled={updating} showSearch  filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }>
              {CHANNELINFO.map((channel) => {
                return (
                  <Option value={channel.ChannelID} key={channel.ChannelID}>
                    {channel.Description}
                  </Option>
                );
              })}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="可绑定主账号上限数">
          {getFieldDecorator("MaxCount", {
            rules: [
              {
                required: true,
              },
            ],
            initialValue: 5,
          })(<InputNumber min={1}/>)}
        </Form.Item>
        <Form.Item label="备注">
          {getFieldDecorator("Remark", {
            rules: [
              {
                required: true,
                message: "请填写备注",
              },
            ],
          })(<TextArea rows={4} />)}
        </Form.Item>
      </Form>
    );
  };
  return (
    <Card style={{ minHeight: "80vh" }}>
      <Row type="flex" justify="space-between" style={{ marginBottom: "30px" }}>
        <Col span={2}>
          <Button
            type="primary"
            onClick={() => {
              resetFields();
              setVisible(true);
              setUpdating(false);
            }}
          >
            添加白名单
          </Button>
        </Col>
        <Col span={4}>
          <Search
            className="searchComp"
            placeholder="请输入手机号,例(86)13800088000"
            onSearch={onSearch}
            onChange={handleSearchChange}
            enterButton={<Icon type="search" />}
          />
        </Col>
      </Row>
      <Row>
        <Table dataSource={dataSource} columns={columns} rowKey="id" />
      </Row>
      <Modal
        title="添加白名单"
        visible={visible}
        onOk={handleOk}
        onCancel={() => {
          setVisible(false);
          resetFields();
        }}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleOk}
          >
            提交
          </Button>,
          <Button
            key="back"
            onClick={() => {
              setVisible(false);
            }}
          >
            取消
          </Button>,
        ]}
        // width={500}
      >
        {addForm()}
      </Modal>
    </Card>
  );
};

export default Form.create()(PhoneNumberBindingCount);
