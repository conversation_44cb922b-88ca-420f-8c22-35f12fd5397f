// 校验ssh和rdp
import React from "react";
import { Form, Input, InputNumber, Modal, Spin } from "antd";
import { SensitiveApi } from "../../utils/request";
class VerifyForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      result: null,
    };
  }
  handleSubmit = () => {
    this.props.form.validateFields(async (err, values) => {
      if (!err) {
        this.setState({ loading: true });
        let res = await SensitiveApi("CheckSshRdpProto", {
          Action: "CheckSshRdpProto",
          ...values,
        });
        if (res.RetCode === 0) {
          this.setState({ loading: false, result: res.returnResult });
        }
      }
    });
  };
  render() {
    let { result, loading } = this.state;
    const { getFieldDecorator } = this.props.form;
    return (
      <Modal
        title="检查sshrdp端口"
        visible={this.props.visible}
        okText="检查"
        onOk={this.handleSubmit}
        destroyOnClose
        confirmLoading={loading}
        onCancel={() => {
          this.props.setVisible(false);
        }}
      >
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 12 }}>
          <Form.Item label="Host" name="host">
            {getFieldDecorator("Host", {
              rules: [{ required: true, message: "请输入主机名" }],
            })(<Input placeholder="请输入主机名" />)}
          </Form.Item>
          <Form.Item label="Port" name="port">
            {getFieldDecorator("Port", {
              rules: [{ required: true, message: "请输入端口号" }],
            })(<InputNumber placeholder="请输入端口号" min={0} max={65535} />)}
          </Form.Item>
          {(result || loading) && (
            <Spin spinning={loading}>
              <Form.Item label="rdp检查结果">
                {result?.rdpCheckMessage}
              </Form.Item>
              <Form.Item label="ssh检查结果">
                {result?.sshCheckMessage}
              </Form.Item>
            </Spin>
          )}
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(VerifyForm);
