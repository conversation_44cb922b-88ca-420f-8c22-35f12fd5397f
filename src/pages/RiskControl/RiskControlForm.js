import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  Row,
  Switch,
  Icon,
  Col,
  Tooltip,
  Select,
  notification,
} from "antd";
import { SensitiveApi } from "../../utils/request";

const RiskControlForm = (props) => {
  const [email, setEmail] = useState([]);
  const [phone, setPhone] = useState([]);
  const [companyId, setCompanyId] = useState("");
  const [remark, setRemark] = useState("");
  const [auth, setAuth] = useState([]);
  const [relevanceCompanyId, setRelevanceCompanyId] = useState([]);
  const [ipList, setIpList] = useState([]);
  const [showAuthInput, setShowAuthInput] = useState(false);
  const [showIpInput, setShowIpInput] = useState(false);

  const handleSubmit = async (event) => {
    event.preventDefault();
    const processedAuth = showAuthInput
      ? auth.map((auth) => auth.split(":")[1].trim())
      : [];
    if (!remark) {
      notification.error({
        message: "提交失败",
        description: "备注不能为空",
      });
      return;
    }

    const data = {
      IdNumber: processedAuth,
      Email: email,
      Phone: phone,
      CompanyId: parseInt(companyId),
      Remark: remark,
      RelevanceCompanyId: relevanceCompanyId,
      IP: showIpInput ? ipList.map((item) => item.ip) : [], // Only submit the IPs, not the location
    };

    props.createRiskBlockInfo(data);
  };

  const handleSearch = async () => {
    try {
      const response = await SensitiveApi("GetCompanyInfoForRiskControl", {
        CompanyId: parseInt(companyId),
      });
      if (response.RetCode === 0) {
        setIpList(
          response.IpList.map((ip) => ({
            ip,
            location: response.IpLocationMap[ip],
          }))
        );
        setEmail(
          response.Contacts.map((contact) => contact.UserEmail).filter(
            (email) => email
          )
        );
        setPhone(
          response.Contacts.map((contact) => contact.UserPhone).filter(
            (phone) => phone
          )
        );
        setAuth(
          response.AuthInfo.map((info) => `${info.AuthName}: ${info.IdNumber}`)
        );
        setRelevanceCompanyId(
          response.RelevanceCompanyIdList.filter((id) => id)
        );
      }
    } catch (error) {
      console.error(error);
      notification.error({
        message: "查询失败",
        description: `原因：${error.message || "未知"}`,
      });
    }
  };

  return (
    <Form onSubmit={handleSubmit} layout={"horizontal"}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            colon={false}
            label={
              <span>
                公司ID<span style={{ color: "red" }}>*</span>
              </span>
            }
            rules={[{ required: true, message: "请填写公司ID" }]}
          >
            <Input
              style={{ width: "40%" }}
              value={companyId}
              onChange={(e) => setCompanyId(e.target.value)}
            />
            <Button
              type="primary"
              style={{ marginLeft: "60px" }}
              onClick={handleSearch}
            >
              查询
            </Button>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="邮箱地址" colon={false}>
            <Select
              mode="tags"
              style={{ width: "90%" }}
              value={email}
              onChange={(e) => setEmail(e)}
              tokenSeparators={[","]}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="手机号码" colon={false}>
            <Select
              mode="tags"
              style={{ width: "90%" }}
              value={phone}
              onChange={(e) => setPhone(e)}
              tokenSeparators={[","]}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            colon={false}
            label={
              <span style={{ display: "flex", alignItems: "center" }}>
                实名信息
                <Tooltip title="系统会自动取出下面信息中的证件号，实名屏蔽会以证件号为维度，请谨慎操作">
                  <Icon
                    type="exclamation-circle"
                    style={{ color: "red", marginLeft: "10px" }}
                  />
                </Tooltip>
                <Switch
                  style={{ marginLeft: "10px" }}
                  checked={showAuthInput}
                  onChange={setShowAuthInput}
                />
              </span>
            }
          >
            {showAuthInput && (
              <Input
                style={{ width: "90%", transform: "translateY(-20px)" }}
                value={auth.join(",")}
                onChange={(e) => setAuth(e.target.value.split(","))}
              />
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            colon={false}
            label={
              <span style={{ display: "flex", alignItems: "center" }}>
                关联IP
                <Switch
                  style={{ marginLeft: "10px" }}
                  checked={showIpInput}
                  onChange={setShowIpInput}
                />
              </span>
            }
          >
            {showIpInput && (
              <Select
                mode="tags"
                style={{ width: "90%", transform: "translateY(-20px)" }}
                value={ipList.map((item) => `${item.ip} (${item.location})`)}
                onChange={(e) =>
                  setIpList(e.map((ip) => ({ ip, location: "" })))
                } // You might want to handle this differently
                tokenSeparators={[","]}
              />
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={20}>
          <Form.Item
            colon={false}
            label={
              <span>
                备注<span style={{ color: "red" }}>*</span>
              </span>
            }
            rules={[{ required: true, message: "请填写备注" }]}
          >
            <Input.TextArea
              style={{ width: "90%" }}
              value={remark}
              onChange={(e) => setRemark(e.target.value)}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={props.loading}>
              提交
            </Button>
            <Button style={{ marginLeft: "10px" }} onClick={props.onCancel}>
              取消
            </Button>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

const RiskControlFormWrapper = Form.create()(RiskControlForm);
export default RiskControlFormWrapper;
