// 校验ssh和rdp
import React from "react";
import { Form, Modal, message, Select } from "antd";
import { SensitiveApi } from "../../utils/request";
class BlockForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }
  handleSubmit = () => {
    this.props.form.validateFields(async (err, values) => {
      if (!err) {
        this.setState({ loading: true });
        let res = await SensitiveApi("BlockGlobalSshRdp", {
          Action: "BlockGlobalSshRdp",
          ...values,
        });
        if (res.RetCode === 0) {
          message.success("封禁成功");
          this.setState({ loading: false });
          this.props.setVisible(false);
        }
      }
    });
  };
  render() {
    let { loading } = this.state;
    const { getFieldDecorator } = this.props.form;
    return (
      <Modal
        title="一键封禁"
        confirmLoading={loading}
        visible={this.props.visible}
        okText="封禁"
        onOk={this.handleSubmit}
        destroyOnClose
        onCancel={() => {
          this.props.setVisible(false);
        }}
      >
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 12 }}>
          {/* <Form.Item label="公司ID" name="CompanyId">
            {getFieldDecorator("CompanyId", {
              rules: [{ required: true, message: "请输入公司ID" }],
            })(<InputNumber placeholder="请输入公司ID" />)}
          </Form.Item> */}
          <Form.Item label="资源ID" name="UgaaIds">
            {getFieldDecorator("UgaaIds", {
              rules: [{ required: true, message: "请输入资源ID" }],
            })(<Select placeholder="请输入资源ID" mode="tags" />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(BlockForm);
