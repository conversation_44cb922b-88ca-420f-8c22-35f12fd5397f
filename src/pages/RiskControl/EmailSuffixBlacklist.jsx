import React, { useState, useEffect } from "react";
import {
  Button,
  Table,
  Input,
  Row,
  Col,
  Card,
  Popconfirm,
  Modal,
  Form,
  message,
  Select,
  DatePicker,
} from "antd";
import moment from "moment";
import request from "../../utils/request";
const { RangePicker } = DatePicker;
const FormItem = Form.Item;
// 搜索表单组件
const SearchForm = Form.create()((props) => {
  const { form, channelList, onSearch, onReset, onAdd } = props;
  const { getFieldDecorator } = form;
  return (
    <Form
      layout="inline"
      className="ant-advanced-search-form"
      style={{ marginBottom: 0 }}
    >
      <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
        <Col span={8} key="channelId">
          <FormItem label="渠道ID">
            {getFieldDecorator("searchChannelId")(
              <Select
                placeholder="请选择渠道ID"
                allowClear
                style={{ width: "100%" }}
                showSearch
              >
                {Object.keys(channelList).map((id) => (
                  <Select.Option key={id} value={parseInt(id, 10)}>
                    {channelList[id]}
                  </Select.Option>
                ))}
              </Select>
            )}
          </FormItem>
        </Col>
        <Col span={8} key="emailSuffix">
          <FormItem label="邮箱后缀">
            {getFieldDecorator("searchEmailSuffix")(
              <Input placeholder="请输入邮箱后缀" allowClear />
            )}
          </FormItem>
        </Col>
        <Col span={8} key="dateRange">
          <FormItem label="时间范围">
            {getFieldDecorator("searchTimeRange")(
              <RangePicker
                style={{ width: "100%" }}
                ranges={{
                  Today: [moment().startOf("day"), moment().endOf("day")],
                  "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                  ],
                }}
                format="YYYY-MM-DD"
              />
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <Button type="primary" onClick={onAdd}>
            添加黑名单
          </Button>
        </Col>
        <Col span={12} style={{ textAlign: "right", marginTop: 24 }}>
          <Button
            type="primary"
            onClick={() => {
              props.form.validateFields((err, values) => {
                if (!err) {
                  onSearch(values);
                }
              });
            }}
            style={{ marginRight: 8 }}
          >
            查询
          </Button>
          <Button
            onClick={() => {
              props.form.resetFields();
              onReset();
            }}
          >
            重置
          </Button>
        </Col>
      </Row>
    </Form>
  );
});

// 添加表单组件
const AddFormModal = Form.create()((props) => {
  const { form, visible, channelList, onCancel, onOk } = props;
  const { getFieldDecorator } = form;

  return (
    <Modal
      title="添加黑名单"
      visible={visible}
      onOk={() => {
        form.validateFields((err, values) => {
          if (!err) {
            onOk(values);
          }
        });
      }}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
    >
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        <FormItem label="邮箱后缀">
          {getFieldDecorator("EmailSuffix", {
            rules: [{ required: true, message: "请输入邮箱后缀" }],
          })(<Input placeholder="请输入邮箱后缀" />)}
        </FormItem>
        <FormItem label="渠道ID">
          {getFieldDecorator("ChannelId", {
            rules: [{ required: true, message: "请选择渠道ID" }],
          })(
            <Select
              placeholder="请选择渠道ID,支持关键字搜索"
              style={{ width: "100%" }}
              showSearch
            >
              {Object.keys(channelList).map((channelId) => (
                <Select.Option key={channelId} value={parseInt(channelId, 10)}>
                  {channelList[channelId]}
                </Select.Option>
              ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="备注">
          {getFieldDecorator("Remark")(
            <Input.TextArea placeholder="请输入备注" />
          )}
        </FormItem>
      </Form>
    </Modal>
  );
});

const EmailSuffixBlacklist = () => {
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total) => `共 ${total} 条记录`,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "50", "100"],
  });
  const [channelList, setChannelList] = useState({}); // 添加渠道列表状态

  useEffect(() => {
    getChannelInfo(); // 获取渠道信息
  }, []);
  useEffect(() => {
    getBlackList();
  }, [pagination.current, pagination.pageSize]);

  // 添加获取渠道信息方法
  const getChannelInfo = () => {
    request("GetChannelInfo")
      .then((resp) => {
        if (resp.ChannelInfo) {
          let channels = {};
          resp.ChannelInfo.forEach((channel) => {
            channels[channel.ChannelID] = channel.Title;
          });
          setChannelList(channels);
        }
      })
      .catch(() => {
        setChannelList({});
      });
  };

  const getBlackList = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        Limit: pagination.pageSize,
        Offset: (pagination.current - 1) * pagination.pageSize,
        ...params,
      };

      // 处理时间范围
      if (params.timeRange && params.timeRange.length === 2) {
        queryParams.StartTime = params.timeRange[0].startOf("day").unix();
        queryParams.EndTime = params.timeRange[1].endOf("day").unix();
        delete queryParams.timeRange;
      }

      const res = await request("GetBlackEmailSuffixList", queryParams);
      if (res.RetCode === 0) {
        setDataSource(res.blackEmailSuffixList);
        setPagination({
          ...pagination,
          total: res.totalRecords,
        });
      }
    } catch (error) {
      console.error(error);
      message.error("获取列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (values) => {
    const searchParams = {};

    if (values.searchChannelId) {
      searchParams.ChannelId = values.searchChannelId;
    }

    if (values.searchEmailSuffix) {
      searchParams.EmailSuffix = values.searchEmailSuffix;
    }

    if (values.searchTimeRange && values.searchTimeRange.length === 2) {
      searchParams.timeRange = values.searchTimeRange;
    }

    setPagination({
      ...pagination,
      current: 1,
    });

    getBlackList(searchParams);
  };

  const handleReset = () => {
    setPagination({
      ...pagination,
      current: 1,
    });
    getBlackList();
  };

  const handleDelete = async (record) => {
    try {
      const res = await request("DeleteBlackEmailSuffix", {
        EmailSuffix: record.email_suffix,
        ChannelId: record.channel_id,
      });
      if (res.RetCode === 0) {
        message.success("删除成功");
        getBlackList();
      }
    } catch (error) {
      console.error(error);
      message.error("删除失败");
    }
  };

  const handleSubmit = async (values) => {
    try {
      const res = await request("CreateBlackEmailSuffix", {
        EmailSuffix: values.EmailSuffix,
        ChannelId: values.ChannelId,
        Remark: values.Remark || "",
      });
      if (res.RetCode === 0) {
        message.success("添加成功");
        setVisible(false);
        getBlackList();
      }
    } catch (error) {
      console.error(error);
      message.error("添加失败");
    }
  };

  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  const columns = [
    {
      title: "邮箱后缀",
      dataIndex: "email_suffix",
      key: "email_suffix",
    },
    {
      title: "渠道ID",
      dataIndex: "channel_id",
      key: "channel_id",
      render: (text) => channelList[text] || text,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      render: (text) => moment(text * 1000).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: "操作人",
      dataIndex: "operator",
      key: "operator",
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Popconfirm
          title="确定要删除该记录吗?"
          onConfirm={() => handleDelete(record)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="danger" ghost>
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  return (
    <>
      <Card title="搜索" style={{ marginBottom: 24 }} bordered={false}>
        <SearchForm
          channelList={channelList}
          onSearch={handleSearch}
          onReset={handleReset}
          onAdd={() => setVisible(true)}
        />
      </Card>
      <Card style={{ minHeight: "80vh" }}>
        <Table
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          loading={loading}
        />
        <AddFormModal
          visible={visible}
          channelList={channelList}
          onCancel={() => setVisible(false)}
          onOk={handleSubmit}
        />
      </Card>
    </>
  );
};

export default EmailSuffixBlacklist;
