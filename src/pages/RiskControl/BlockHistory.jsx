import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Form,
  Col,
  Input,
  Button,
  DatePicker,
  Table,
  notification,
} from "antd";
import moment from "moment";
import { SensitiveApi } from "../../utils/request";
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const GlobalSSHList = () => {
  const [CompanyId, setCompanyId] = useState("");
  const [Ugaaid, setUgaaId] = useState("");
  const [StartTime, setBeginTime] = useState(null);
  const [EndTime, setEndTime] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showTotal: (total) => `共计：${total}条`,
    showQuickJumper: true,
    showSizeChanger: true,
  });
  const [loading, setLoading] = useState(false);
  const [abnormalList, setAbnormalList] = useState([]);
  const onSearch = ()=>{
    let body = {
      Limit: pagination.pageSize,
      Offset: 0,
    }
    GetBlockGlobalSshRdpHistory(body)
  }
  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };
  const GetBlockGlobalSshRdpHistory = (body) => {
    let options = {
      Action: "GetBlockGlobalSshRdpHistory",
      CompanyId: CompanyId,
      Ugaaid: Ugaaid,
      StartTime: StartTime ? StartTime.unix() : null,
      EndTime: EndTime ? EndTime.unix() : null,
      Limit: pagination.pageSize,
      Offset: pagination.pageSize * (pagination.current - 1),
      ...body
    };
    if (!CompanyId) {
      delete options.CompanyId;
    }
    if (!Ugaaid) {
      delete options.Ugaaid;
    }
    if (!StartTime) {
      delete options.StartTime;
    }
    if (!EndTime) {
      delete options.EndTime;
    }
    setLoading(true);

    // const getAllRecords = async (totalRecords) => {
    //   const allRecords = [];
    //   const totalPages = Math.ceil(totalRecords / 200);

    //   for (let page = 1; page <= totalPages; page++) {
    //     const pageOptions = {
    //       ...options,
    //       Offset: 200 * (page - 1),
    //     };

    //     try {
    //       const resp = await SensitiveApi(
    //         "GetBlockGlobalSshRdpHistory",
    //         pageOptions
    //       );
    //       if (resp.RetCode === 0) {
    //         allRecords.push(...resp.unblockRecods);
    //       } else {
    //         throw new Error(resp.Message || resp.RetCode + "查询失败");
    //       }
    //     } catch (err) {
    //       notification["error"]({
    //         message: "请求失败",
    //         description: err.message || "内部错误",
    //       });
    //       setLoading(false);
    //       return;
    //     }
    //   }
    //   return allRecords.sort((a, b) => b.UpdatedTime - a.UpdatedTime);
    // };

    SensitiveApi("GetBlockGlobalSshRdpHistory", options)
      .then(async (resp) => {
        if (resp.RetCode === 0) {
          const totalRecords = resp.totalRecords;
          // const allRecords = await getAllRecords(totalRecords);
          setAbnormalList(resp.unblockRecods);
          // setOriginData(allRecords);
          pagination.total = totalRecords;
          setPagination(pagination);
          setLoading(false);
          notification.success({
            message: "查询成功",
          });
        } else {
          throw new Error(resp.Message || resp.RetCode + "查询失败");
        }
      })
      .catch((err) => {
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        setLoading(false);
      });
  };
  const renderAdvancedForm = () => {
    return (
      <Form
        layout="inline"
        className="ant-advanced-search-form"
        style={{ marginBottom: 0 }}
      >
        <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144443}>
            <FormItem label="公司ID">
              <Input
                style={{ width: "100%" }}
                value={CompanyId}
                placeholder="请输入对应的公司ID"
                onChange={(e) => {
                  setCompanyId(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
          <Col span={12} key={144444}>
            <FormItem label="资源ID">
              <Input
                style={{ width: "100%" }}
                value={Ugaaid}
                placeholder="请输入对应的资源ID"
                onChange={(e) => {
                  setUgaaId(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144664}>
            <FormItem label="时间">
              <RangePicker
                style={{ width: "100%" }}
                ranges={{
                  Today: [moment().startOf("day"), moment().endOf("day")],
                  "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                  ],
                }}
                showTime={{
                  defaultValue: [
                    moment("00:00:00", "HH:mm:ss"),
                    moment("23:59:59", "HH:mm:ss"),
                  ],
                }}
                format="YYYY-MM-DD"
                value={[StartTime, EndTime]}
                onChange={(v) => {
                  if (v.length === 0) {
                    setBeginTime(null);
                    setEndTime(null);
                    return;
                  }
                  //选择同一天时，默认设置为0点-23点59
                  if (v[0].unix() === v[1].unix()) {
                    v[0] = v[0].startOf("day");
                    v[1] = v[1].endOf("day");
                  }
                  setBeginTime(v[0]);
                  setEndTime(v[1]);
                }}
              />
            </FormItem>
          </Col>
          <Col span={6} key={2} offset={6}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                type="primary"
                style={{ marginRight: "16px" }}
                onClick={() => {
                  onSearch();
                }}
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  reSetField();
                }}
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  };

  const reSetField = () => {
    setCompanyId("");
    setUgaaId("");
    setBeginTime(null);
    setEndTime(null);
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0,
      showTotal: (total) => `共计：${total}条`,
      showQuickJumper: true,
      showSizeChanger: true,
    });
    GetBlockGlobalSshRdpHistory();
  };
  useEffect(() => {
    GetBlockGlobalSshRdpHistory();
  }, [pagination.current, pagination.pageSize]);

  const columns = [
    { key: "Ugaaid", title: "资源ID", dataIndex: "Ugaaid" },

    { key: "CompanyId", title: "公司ID", dataIndex: "CompanyId" },
    {
      key: "CompanyName",
      title: "公司名称",
      dataIndex: "CompanyName",
      render: (val) => (val ? val : "-"),
    },
    {
      key: "AuthType",
      title: "认证类型",
      // filters: Array.from(new Set(originData.map((el) => el.AuthType)))
      //   .filter((el) => el)
      //   .map((el) => ({ text: el, value: el })),
      dataIndex: "AuthType",
      render: (val) => (val ? val : "-"),
    },
    {
      key: "VIPLevel",
      title: "客户等级",
      // filters: Array.from(new Set(originData.map((el) => el.VIPLevel)))
      //   .filter((el) => el)
      //   .map((el) => ({ text: el, value: el }))
      //   .sort((a, b) => a.text.localeCompare(b.text)),
      dataIndex: "VIPLevel",
      render: (val) => (val ? val : "-"),
    },
    {
      key: "UpdatedTime",
      title: "封禁时间",
      dataIndex: "UpdatedTime",
      // sorter: true,
      render: (val) => <span>{moment(val).format("YYYY-MM-DD HH:mm:ss")}</span>,
    },
    { key: "Cname", title: "Cname", dataIndex: "Cname" },
    { key: "Port", title: "端口", dataIndex: "Port" },
    {
      key: "SsoUser",
      title: "操作人",
      dataIndex: "SsoUser",
      render: (val) => (val ? val : "-"),
    },
  ];

  return (
    <>
      {renderAdvancedForm()}
      <Card
        title="结果"
        style={{ marginBottom: 24 }}
        bordered={false}
      >
        <Table
          loading={loading}
          dataSource={abnormalList}
          columns={columns}
          pagination={pagination}
          onChange={handleTableChange}
          rowKey="_id"
        />
      </Card>
    </>
  );
};

export default GlobalSSHList;
