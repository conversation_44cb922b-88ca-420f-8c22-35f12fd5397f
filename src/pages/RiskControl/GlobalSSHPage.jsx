import React, { Component } from "react";
import { Tabs, <PERSON>, Button } from "antd";
import "./index.css";
import GlobalSSH from "./GlobalSSH";
import BlockHistory from "./BlockHistory";
import UnBlockHistory from "./UnBlockHistory";
import VerifyModalForm from "./VerifyModalForm";
import BlockModalForm from "./BlockModalForm";
import UnBlockModalForm from "./UnBlockModalForm";
const { TabPane } = Tabs;
class GlobalSSHPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      verifyModalVisible: false,
      blockModalVisible: false,
      unblockModalVisible: false,
    };
  }
  render() {
    let { verifyModalVisible, blockModalVisible, unblockModalVisible } =
      this.state;
    return (
      <Card>
        <div
          style={{
            width: "300px",
            padding: "10px",
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
            gap: "5px",
          }}
        >
          <Button
            onClick={() => this.setState({ verifyModalVisible: true })}
            icon="search"
          >
            核查
          </Button>
          <Button
            type="danger"
            onClick={() => this.setState({ blockModalVisible: true })}
            icon="stop"
          >
            封禁
          </Button>
          <Button
            type="primary"
            onClick={() => this.setState({ unblockModalVisible: true })}
            icon="check"
          >
            解封
          </Button>
        </div>

        <VerifyModalForm
          visible={verifyModalVisible}
          setVisible={(visible) => {
            this.setState({ verifyModalVisible: visible });
          }}
        />
        <BlockModalForm
          visible={blockModalVisible}
          setVisible={(visible) => {
            this.setState({ blockModalVisible: visible });
          }}
        />
        <UnBlockModalForm
          visible={unblockModalVisible}
          setVisible={(visible) => {
            this.setState({ unblockModalVisible: visible });
          }}
        />
        <Tabs defaultActiveKey="1">
          <TabPane tab="拦截结果" key="1">
            <GlobalSSH isBYPassCustomer={false} isAlreadyBan={true} />
          </TabPane>
          <TabPane tab="绕过情况" key="2">
            <GlobalSSH isBYPassCustomer={true} isAlreadyBan={false} />
          </TabPane>
          <TabPane tab="封禁历史记录" key="3">
            <BlockHistory />
          </TabPane>
          <TabPane tab="解封历史记录" key="4">
            <UnBlockHistory />
          </TabPane>
        </Tabs>
      </Card>
    );
  }
}
export default GlobalSSHPage;
