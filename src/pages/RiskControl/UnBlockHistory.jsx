import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Form,
  Col,
  Input,
  Button,
  DatePicker,
  Table,
  notification,
  Icon,
} from "antd";
import moment from "moment";
import exportFile from "../../components/expoertFile";
import { SensitiveApi } from "../../utils/request";
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const GlobalSSHList = (props) => {
  const [CompanyId, setCompanyId] = useState("");
  const [Ugaaid, setUgaaId] = useState("");
  const [StartTime, setBeginTime] = useState(null);
  const [EndTime, setEndTime] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showTotal: (total) => `共计：${total}条`,
    showQuickJumper: true,
    showSizeChanger: true,
  });
  const [oldFilters, setOldFilters] = useState({});
  const [oldSorter, setOldSorter] = useState({});
  const [loading, setLoading] = useState(false);
  const [abnormalList, setAbnormalList] = useState([]);
  const [originData, setOriginData] = useState([]);
  const searchData = () => {
    return originData.filter((item) => {
      let match = true;

      if (CompanyId && item.CompanyId !== parseInt(CompanyId)) {
        match = false;
      }

      if (Ugaaid && !item.Ugaaid.includes(Ugaaid)) {
        match = false;
      }

      if (StartTime && EndTime) {
        const itemTime = item.UnBlockTime;
        if (itemTime >= EndTime.unix() || itemTime <= StartTime.unix()) {
          match = false;
        }
      }
      return match;
    });
  };
  const onSearch = (filters, sorter) => {
    let data = searchData();
    Object.keys(filters).forEach((key) => {
      if (filters[key].length) {
        data = data.filter((el) => filters[key].includes(el[key]));
      }
    });
    data.sort((a, b) => {
      if (sorter.order === "descend") {
        return b[sorter["field"]] - a[sorter["field"]];
      } else if (sorter.order === "ascend") {
        return a[sorter["field"]] - b[sorter["field"]];
      }
    });
    setAbnormalList(data);
    setPagination({
      current: 1,
      pageSize: 20,
      total: data.length,
      showTotal: (total) => `共计：${total}条`,
      showQuickJumper: true,
      showSizeChanger: true,
    });
  };
  const GetUnBlockSshRdpRecord = () => {
    let options = {
      Action: "GetUnBlockSshRdpRecord",
      isBYPassCustomer: props.isBYPassCustomer,
      isAlreadyBan: props.isAlreadyBan,
      Limit: 200,
      Offset: pagination.pageSize * (pagination.current - 1),
    };
    if (!CompanyId) {
      delete options.CompanyId;
    }
    if (!Ugaaid) {
      delete options.Ugaaid;
    }
    if (!StartTime) {
      delete options.StartTime;
    }
    if (!EndTime) {
      delete options.EndTime;
    }
    setLoading(true);

    const getAllRecords = async (totalRecords) => {
      const allRecords = [];
      const totalPages = Math.ceil(totalRecords / 200);

      for (let page = 1; page <= totalPages; page++) {
        const pageOptions = {
          ...options,
          Offset: 200 * (page - 1),
        };

        try {
          const resp = await SensitiveApi(
            "GetUnBlockSshRdpRecord",
            pageOptions
          );
          if (resp.RetCode === 0) {
            allRecords.push(...resp.unblockRecods);
          } else {
            throw new Error(resp.Message || resp.RetCode + "查询失败");
          }
        } catch (err) {
          notification["error"]({
            message: "请求失败",
            description: err.message || "内部错误",
          });
          setLoading(false);
          return;
        }
      }
      return allRecords.sort((a, b) => b.UpdatedTime - a.UpdatedTime);
    };

    SensitiveApi("GetUnBlockSshRdpRecord", options)
      .then(async (resp) => {
        if (resp.RetCode === 0) {
          const totalRecords = resp.totalRecords;
          const allRecords = await getAllRecords(totalRecords);
          setAbnormalList(allRecords);
          setOriginData(allRecords);
          pagination.total = totalRecords;
          setPagination(pagination);
          setLoading(false);
          notification.success({
            message: "查询成功",
          });
        } else {
          throw new Error(resp.Message || resp.RetCode + "查询失败");
        }
      })
      .catch((err) => {
        notification["error"]({
          message: "请求失败",
          description: err.message || "内部错误",
        });
        setLoading(false);
      });
  };
  const handleTableChange = (pagination, filters, sorter) => {
    const isFilterSorterChange =
      JSON.stringify(oldFilters) !== JSON.stringify(filters) ||
      JSON.stringify(oldSorter) !== JSON.stringify(sorter);
    if (isFilterSorterChange) {
      // 如果筛选或排序变化，将分页重置为第一页
      onSearch(filters, sorter);
    } else {
      // 否则更新分页器（比如翻页）
      setPagination(pagination);
    }
    setOldFilters(filters);
    setOldSorter(sorter);
  };
  const renderAdvancedForm = () => {
    return (
      <Form
        layout="inline"
        className="ant-advanced-search-form"
        style={{ marginBottom: 0 }}
      >
        <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144443}>
            <FormItem label="公司ID">
              <Input
                style={{ width: "100%" }}
                value={CompanyId}
                placeholder="请输入对应的公司ID"
                onChange={(e) => {
                  setCompanyId(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
          <Col span={12} key={144444}>
            <FormItem label="资源ID">
              <Input
                style={{ width: "100%" }}
                value={Ugaaid}
                placeholder="请输入对应的资源ID"
                onChange={(e) => {
                  setUgaaId(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 12, sm: 16, md: 24, lg: 32 }}>
          <Col span={12} key={144664}>
            <FormItem label="时间">
              <RangePicker
                style={{ width: "100%" }}
                ranges={{
                  Today: [moment().startOf("day"), moment().endOf("day")],
                  "This Month": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                  ],
                }}
                showTime={{
                  defaultValue: [
                    moment("00:00:00", "HH:mm:ss"),
                    moment("23:59:59", "HH:mm:ss"),
                  ],
                }}
                format="YYYY-MM-DD"
                value={[StartTime, EndTime]}
                onChange={(v) => {
                  if (v.length === 0) {
                    setBeginTime(null);
                    setEndTime(null);
                    return;
                  }
                  //选择同一天时，默认设置为0点-23点59
                  if (v[0].unix() === v[1].unix()) {
                    v[0] = v[0].startOf("day");
                    v[1] = v[1].endOf("day");
                  }
                  setBeginTime(v[0]);
                  setEndTime(v[1]);
                }}
              />
            </FormItem>
          </Col>
          <Col span={6} key={2} offset={6}>
            <FormItem style={{ width: "100%", marginLeft: "80px" }} label="">
              <Button
                type="primary"
                style={{ marginRight: "16px" }}
                onClick={() => {
                  onSearch(oldFilters, oldSorter);
                }}
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  reSetField();
                }}
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    );
  };

  const reSetField = () => {
    setCompanyId("");
    setUgaaId("");
    setBeginTime(null);
    setEndTime(null);
    setPagination({
      current: 1,
      pageSize: 20,
      total: 0,
    });
    setAbnormalList(originData);
  };
  useEffect(() => {
    GetUnBlockSshRdpRecord();
  }, []);

  const columns = [
    { key: "Ugaaid", title: "资源ID", dataIndex: "Ugaaid" },
    { key: "CompanyId", title: "公司ID", dataIndex: "CompanyId" },
    { key: "CompanyName", title: "公司名称", dataIndex: "CompanyName", render: (val) => (val ? val : "-"), },
    {
      key: "UpdatedTime",
      title: "解封时间",
      dataIndex: "UpdatedTime",
      sorter: true,
      render: (val) => <span>{moment(val).format("YYYY-MM-DD HH:mm:ss")}</span>,
    },

    {
      key: "SsoUser",
      title: "操作人",
      dataIndex: "SsoUser",
      render: (val) => (val ? val : "-"),
    },
  ];

  const downloadData = () => {
    if (abnormalList.length === 0) {
      notification["error"]({
        message: "无下载内容",
        description: "无下载内容",
      });
      return;
    }
    let dataList = abnormalList.reduce(
      (prev, item) => {
        let flagTime = item.UpdatedTime;
        let time = moment(flagTime).format("YYYY-MM-DD HH:mm:ss") + "";
        prev.push([
          item.Ugaaid || "",
          item.CompanyId|| "",
          item?.CompanyName || "",
          time || "",
          item.SsoUser,
        ]);
        return prev;
      },
      [["资源ID", "公司ID", "公司名称", `解封时间`, "操作人"]]
    );

    let culumnWidthArray = [14, 12, 22, 16, 12, 20, 10];
    let fileName = `${moment().format(
      "YYYY-MM-DD HH:mm:ss"
    )}${"解封记录.xlsx"}`;
    exportFile(dataList, culumnWidthArray, fileName);
  };
  return (
    <>
      {renderAdvancedForm()}
      <Card
        title="结果"
        extra={
          <Button type="download" onClick={downloadData}>
            <Icon type="download" />
            全量导出
          </Button>
        }
        style={{ marginBottom: 24 }}
        bordered={false}
      >
        <Table
          loading={loading}
          dataSource={abnormalList}
          columns={columns}
          pagination={pagination}
          onChange={handleTableChange}
          rowKey="_id"
        />
      </Card>
    </>
  );
};

export default GlobalSSHList;
