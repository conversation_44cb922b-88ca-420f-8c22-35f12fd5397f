import React, { Component } from "react";
import {
  Card,
  Row,
  Form,
  Col,
  Input,
  Table,
  Button,
  notification,
  Modal,
} from "antd";
import "./index.css";
import { SensitiveApi } from "../../utils/request";
import moment from "moment";
import RiskControlFormWrapper from "./RiskControlForm"; // 引入新的表单组件
const FormItem = Form.Item;
import RiskDetailFormWrapper from "./RiskDetail";

class RiskControl extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [], // 列表初始值,
      loading: false,
      CompanyId: "",
      RelevanceCompanyId: "",
      Phone: "",
      Email: "",
      IP: "",
      detailData: null,
      detailModalVisible: false,
      IdNumber: "",
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      modalVisible: false, // 新增
      formLoading: false, // 新增
    };
  }

  componentDidMount() {
    this.GetRiskBlockList();
  }

  onSearch = () => {
    this.setState(
      {
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
      },
      () => {
        this.GetRiskBlockList();
      }
    );
  };
  fetchDetailData = async (Id) => {
    const response = await SensitiveApi("GetRiskBlockInfo", { RiskId: Id });
    if (response.RetCode === 0) {
      this.setState({ detailData: response });
    }
  };
  createRiskBlockInfo = async (data) => {
    this.setState({ formLoading: true });
    try {
      const response = await SensitiveApi("CreateRiskBlockInfo", data);
      if (response.RetCode === 0) {
        notification.success({
          message: "创建成功",
        });
        this.setState({ modalVisible: false });
        this.GetRiskBlockList();
      } else {
        notification.error({
          message: "创建失败",
          description: `原因：${response.Message || "未知"}`,
        });
      }
    } catch (error) {
      notification.error({
        message: "创建失败",
        description: `原因：${error.message || "未知"}`,
      });
    }
    this.setState({ formLoading: false });
  };
  GetRiskBlockList = () => {
    let { CompanyId, RelevanceCompanyId, Phone, Email, IP, IdNumber, pagination } =
      this.state;
    let options = {
      Action: "GetRiskBlockList",
      ...(CompanyId && { CompanyId }),
      ...(RelevanceCompanyId && { RelevanceCompanyId }),
      ...(Phone && { Phone }),
      ...(Email && { Email }),
      ...(IP && { IP }),
      ...(IdNumber && { IdNumber }),
      Limit: pagination.pageSize,
      Offset: pagination.pageSize * (pagination.current - 1),
    };

    SensitiveApi("GetRiskBlockList", options)
      .then((res) => {
        if (res.RetCode === 0) {
          this.setState({
            list: res.Rows,
            loading: false,
            pagination: {
              ...this.state.pagination,
              total: res.TotalCount,
            },
          });
        } else {
          notification["error"]({
            message: "查询失败",
            description: res.Message,
          });
        }
      })
      .catch((err) => {
        this.setState({ loading: false });
        notification["error"]({
          message: "API请求失败",
          description: err.message || "内部错误",
        });
      });
  };

  handleTableChange = (pagination) => {
    const pager = { ...this.state.pagination };
    pager.current = pagination.current;
    this.setState(
      {
        pagination: pager,
      },
      () => {
        this.GetRiskBlockList();
      }
    );
  };

  handleInvalidate = (RiskId) => {
    Modal.confirm({
      title: "你确定要失效这个风控行为吗？",
      onOk: () => {
        const data = {
          RiskId: RiskId,
          Status: 0,
        };
        SensitiveApi("UpdateRiskBlock", data)
          .then((res) => {
            if (res.RetCode === 0) {
              notification["success"]({
                message: "风险已失效",
              });
              this.GetRiskBlockList();
            } else {
              notification["error"]({
                message: "操作失败",
                description: res.Message,
              });
            }
          })
          .catch((err) => {
            notification["error"]({
              message: "API请求失败",
              description: err.message || "内部错误",
            });
          });
      },
    });
  };

  createRiskBlockInfo = (data) => {
    this.setState({ formLoading: true });
    SensitiveApi("CreateRiskBlockInfo", data)
      .then((res) => {
        if (res.RetCode === 0) {
          notification["success"]({
            message: "风险信息已提交",
          });
          this.setState({ modalVisible: false });
        } else {
          notification["error"]({
            message: "提交失败",
            description: res.Message,
          });
        }
      })
      .catch((err) => {
        notification["error"]({
          message: "API请求失败",
          description: err.message || "内部错误",
        });
      })
      .finally(() => {
        this.setState({ formLoading: false });
      });
  };

  render() {
    let { list, loading, pagination } = this.state;
    const columns = [
      {
        title: "风险ID",
        dataIndex: "Id",
        key: "Id",
      },
      {
        title: "公司ID",
        dataIndex: "CompanyId",
        key: "CompanyId",
      },
      {
        title: "关联公司ID",
        dataIndex: "RelevanceCompanyId",
        key: "RelevanceCompanyId",
      },

      {
        title: "状态",
        dataIndex: "Status",
        key: "Status",
        render: (text) => (text === 0 ? "失效" : "封禁中"),
      },
      {
        title: "备注",
        dataIndex: "Remark",
        key: "Remark",
      },
      {
        title: "创建时间",
        dataIndex: "CreateTime",
        key: "CreateTime",
        render: (text) => moment(text * 1000).format("YYYY-MM-DD HH:mm:ss"),
      },
      {
        title: "更新时间",
        dataIndex: "UpdateTime",
        key: "UpdateTime",
        render: (text) => moment(text * 1000).format("YYYY-MM-DD HH:mm:ss"),
      },
      {
        title: "操作",
        key: "action",
        render: (text, record) => (
          <span>
            <Button
              onClick={() => {
                this.fetchDetailData(record.Id);
                this.setState({ detailModalVisible: true });
              }}
            >
              详情
            </Button>
            <Button disabled={record.Status === 0} onClick={() => this.handleInvalidate(record.Id)}>
              失效
            </Button>
          </span>
        ),
      },
    ];

    return (
      <Card bordered={false}>
        <div className="tableList">
          <Card bordered={false}>
            <Form layout="inline" className="ant-advanced-search-form">
              <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col md={8} sm={24}>
                  <FormItem
                    label="公司ID"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    <Input
                      placeholder="请输入"
                      value={this.state.CompanyId}
                      onChange={(e) =>
                        this.setState({ CompanyId: e.target.value })
                      }
                    />
                  </FormItem>
                </Col>
                <Col md={8} sm={24}>
                  <FormItem
                    label="手机号码"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    <Input
                      placeholder="例：(86)13800138000"
                      value={this.state.Phone}
                      onChange={(e) => this.setState({ Phone: e.target.value })}
                    />
                  </FormItem>
                </Col>{" "}
                <Col md={8} sm={24}>
                  <FormItem
                    label="邮箱地址"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    <Input
                      placeholder="请输入"
                      value={this.state.Email}
                      onChange={(e) => this.setState({ Email: e.target.value })}
                    />
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col md={8} sm={24}>
                  <FormItem
                    label="IP地址"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    <Input
                      placeholder="请输入"
                      value={this.state.IP}
                      onChange={(e) => this.setState({ IP: e.target.value })}
                    />
                  </FormItem>
                </Col>
                <Col md={8} sm={24} xl={{span:10}} xxl={{span:8}}>
                  <FormItem
                    label="实名证件号"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    <Input
                      placeholder="请输入"
                      value={this.state.IdNumber}
                      onChange={(e) => this.setState({ IdNumber: e.target.value })}
                    />
                  </FormItem>
                </Col>
              </Row>
              <Row>
              <Col span={24}  style={{ textAlign: "center" }}>
                  <Button type="primary" onClick={this.onSearch}>
                    查询
                  </Button>
                  <Button
                    type="primary"
                    style={{ marginLeft: "10px" }}
                    onClick={() => this.setState({ modalVisible: true })}
                  >
                    创建信息
                  </Button>
                </Col>
              </Row>
            </Form>
          </Card>
          <Table
            loading={loading}
            dataSource={list}
            columns={columns}
            pagination={pagination}
            onChange={this.handleTableChange}
          />
        </div>
        <Modal
          visible={this.state.modalVisible}
          title="风险信息登记"
          footer={null}
          width="600px"
          onCancel={() => this.setState({ modalVisible: false })} // 在这里添加处理函数
        >
          <RiskControlFormWrapper
            createRiskBlockInfo={this.createRiskBlockInfo}
            onCancel={() => this.setState({ modalVisible: false })}
            loading={this.state.formLoading}
          />
        </Modal>
        <Modal
          visible={this.state.detailModalVisible}
          title="风险详情"
          footer={null}
          width="900px"
          onCancel={() => {
            this.setState({ detailModalVisible: false });
            this.GetRiskBlockList();
          }}
        >
          <RiskDetailFormWrapper
            data={this.state.detailData}
            fetchDetailData={this.fetchDetailData} // 将fetchDetailData函数传递给RiskDetail组件
          />
        </Modal>
      </Card>
    );
  }
}

const RiskControlPage = Form.create()(RiskControl);

export default RiskControlPage;
