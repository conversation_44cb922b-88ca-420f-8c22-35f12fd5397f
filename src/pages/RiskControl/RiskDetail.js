import React, { useState, useEffect } from "react";
import {
  Descriptions,
  Card,
  Row,
  Col,
  Typography,
  Switch,
  Tooltip,
  notification,
  Input,
  message,
  Button,
} from "antd";
import { SensitiveApi } from "../../utils/request";
import "./RiskDetail.css";

const { Text } = Typography;

const RiskDetail = ({ data, fetchDetailData }) => {
  const [detailData, setDetailData] = useState(data);
  const [remark, setRemark] = useState(data ? data.Remark : "");
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    setDetailData(data);
    setRemark(data ? data.Remark : "");
  }, [data]);

  const handleUpdate = async (params) => {
    try {
      const response = await Sensitive<PERSON><PERSON>("UpdateRiskBlock", params);
      if (response.RetCode === 0) {
        fetchDetailData(params.RiskId);
      } else {
        message.error(`更新失败：${response.Message}`);
      }
    } catch (error) {
      notification.error({
        message: "更新失败",
        description: `出错原因：${error.message || "未知"}`,
      });
    }
  };

  const handleStatusChange = (item, index, type) => {
    if (detailData.Status === 0) {
      message.warning("当前封禁的主体状态为失效，无法单独修改单独封禁项的状态");
      return;
    }
    const params = {
      RiskId: detailData.Id,
      [type]: detailData[type].map((dataItem, i) =>
        i === index
          ? {
              Id: dataItem[type + "Id"],
              Status: dataItem.Status === 1 ? 0 : 1,
            }
          : dataItem
      ),
    };
    handleUpdate(params);
  };

  const handleRemarkChange = (e) => {
    setRemark(e.target.value);
  };

  const handleRemarkSave = () => {
    const params = {
      RiskId: detailData.Id,
      Remark: remark,
    };
    handleUpdate(params);
    setIsEditing(false);
  };

  return (
    <Card>
      <Descriptions
        title="风险详情"
        column={2}
        bordered
        style={{ borderWidth: "1.5px" }}
      >
        <Descriptions.Item label="公司ID">
          {detailData && detailData.CompanyId}
        </Descriptions.Item>
        <Descriptions.Item label="关联公司ID">
          {detailData && detailData.RelevanceCompanyId}
        </Descriptions.Item>
        <Descriptions.Item label="备注" span={2}>
          <Row gutter={16}>
            <Col span={18}>
              <Input
                className={isEditing ? "" : "borderless-input"}
                value={remark}
                onChange={handleRemarkChange}
                readOnly={!isEditing}
              />
            </Col>
            <Col span={6} style={{ textAlign: "right" }}>
              {isEditing ? (
                <Button type="primary" onClick={handleRemarkSave}>
                  确定
                </Button>
              ) : (
                <Button type="primary" onClick={() => setIsEditing(true)}>
                  编辑
                </Button>
              )}
            </Col>
          </Row>
        </Descriptions.Item>
        {detailData &&
          detailData.Email.map((item, index) => (
            <Descriptions.Item key={index} label={`邮箱${index + 1}`}>
              <Text type={item.Status === 1 ? "default" : "secondary"}>
                {item.Email}
              </Text>{" "}
              <span style={{ marginRight: "2ch" }} />
              <Tooltip
                title={item.Status === 1 ? "封禁中" : "封禁失效"}
              ></Tooltip>{" "}
              <Switch
                checked={item.Status === 1}
                onChange={() => handleStatusChange(item, index, "Email")}
                checkedChildren="封禁中"
                unCheckedChildren="失效中"
                className={item.Status === 1 ? "ant-switch-checked" : ""}
              />
            </Descriptions.Item>
          ))}
        {detailData &&
          detailData.Phone.map((item, index) => (
            <Descriptions.Item key={index} label={`电话${index + 1}`}>
              <Text type={item.Status === 1 ? "default" : "secondary"}>
                {item.Phone}
              </Text>{" "}
              <span style={{ marginRight: "2ch" }} />
              <Tooltip
                title={item.Status === 1 ? "封禁中" : "封禁失效"}
              ></Tooltip>{" "}
              <Switch
                checked={item.Status === 1}
                onChange={() => handleStatusChange(item, index, "Phone")}
                checkedChildren="封禁中"
                unCheckedChildren="失效中"
                className={item.Status === 1 ? "switch-red" : ""}
              />
            </Descriptions.Item>
          ))}
        {detailData &&
          detailData.IP.map((item, index) => (
            <Descriptions.Item key={index} label={`IP${index + 1}`}>
              <Text type={item.Status === 1 ? "default" : "secondary"}>
                {item.IP} ({item.Address})
              </Text>{" "}
              <span style={{ marginRight: "2ch" }} />
              <Tooltip
                title={item.Status === 1 ? "封禁中" : "封禁失效"}
              ></Tooltip>{" "}
              <Switch
                checked={item.Status === 1}
                onChange={() => handleStatusChange(item, index, "IP")}
                checkedChildren="封禁中"
                unCheckedChildren="失效中"
                className={item.Status === 1 ? "switch-red" : ""}
              />
            </Descriptions.Item>
          ))}
        {detailData &&
          detailData.AuthInfo.map((item, index) => (
            <Descriptions.Item key={index} label={`实名信息${index + 1}`}>
              <Text type={item.Status === 1 ? "default" : "secondary"}>
                {item.IdNumber}
              </Text>{" "}
              <span style={{ marginRight: "2ch" }} />
              <Tooltip
                title={item.Status === 1 ? "封禁中" : "封禁失效"}
              ></Tooltip>{" "}
              <Switch
                checked={item.Status === 1}
                onChange={() => handleStatusChange(item, index, "AuthInfo")}
                checkedChildren="封禁中"
                unCheckedChildren="失效中"
                className={item.Status === 1 ? "switch-red" : ""}
              />
            </Descriptions.Item>
          ))}
      </Descriptions>
    </Card>
  );
};

export default RiskDetail;
