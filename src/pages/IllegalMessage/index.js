import { connect } from "react-redux"
import List from "./List"
import { setFormValues, setPagination, getIllegalList, setLoading, getIllegalAnalyProgress } from "../../actionCreator/message"

const mapDispatchToProps = (dispatch) => {
    return {
      setFormValues: (data) =>  dispatch(setFormValues("SET_ILLEGAL_FORM_VALUES",data)),
      setPagination: (data) =>  dispatch(setPagination("SET_ILLEGAL_PAGINATION",data)),
      getList: (data) => dispatch(getIllegalList(data)),
      setLoading: (data) => dispatch(setLoading("SET_ILLEGAL_LOADING",data)),
      getIllegalAnalyProgress:(data) => dispatch(getIllegalAnalyProgress(data))
    }
  }
const mapStateToProps = ({illegalMessageReducer}) => {
    return {
      ...illegalMessageReducer
    }
  }
  const IllegalMessageContiner = connect(mapStateToProps,mapDispatchToProps)(List)
  
  export default IllegalMessageContiner
  