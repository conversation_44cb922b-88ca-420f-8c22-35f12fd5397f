import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom'
import { connect } from "react-redux"
import { Table, Breadcrumb, notification, Select,Input, Row, Col, Card, Form, Button, Popconfirm, Modal } from 'antd';
import { getIllegalBatchList, setLoading, setIllegalBatchList, setPagination, getIllegalAnalyProgress } from "../../actionCreator/batchDetail"
import IllegalBatchTable from '../../components/IllegalBatchTable'
import './List.css';
import request from '../../utils/request'
import ProgressSlider from '../../components/ProgressSlider';
const Search = Input.Search;
let interval = null
const { Option } = Select;
class BatchDetail extends Component {
  state = {
    // loading: false,
    batchId: parseInt(this.props.match.params.BatchId, 10),
    // abnormalRecord: [],
    BUNotificationInfo: [],
    illegalBatchTableLoading:false,
    search:"",
    // batchStatus: "",
    expandForm: false, // 是否展开
    // rawList: [], // 所有值
    // list: [], // 列表展示值
    // pagination: {
    //   current: 1,
    //   pageSize: 20,
    //   total: 0
    // },
  };
  refresh = async () => {
    await this.fetch()
    const { buIsNotified,BUNotificationInfo } = this.props
    this.setState({
      buIsNotified,
      BUNotificationInfo
    })
  }

  notify(options = {}) {
    // 批量发送信息
    request('NotifyIllegalBatch', options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '消息推送成功' : resp.Message || resp.RetCode + "发送失败"

        notification.open({
          message: message,
        });
      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }


  retryNotify(options = {}) {
    // 批量发送信息
    request('RetryIllegalBatch', options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '消息推送成功' : resp.Message || resp.RetCode + "发送失败"

        notification.open({
          message: message,
        });

      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }

  notifyBU = ()=> {
    // 通知BU
    request('NotifyBUAboutIllegal', {BatchId: this.state.batchId})
      .then(resp => {
        let message = resp.RetCode === 0 ? '消息推送成功' : resp.Message || resp.RetCode + "发送失败"

        this.setState({
          buIsNotified: true
        })
        notification.open({
          message: message,
        });

      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '发送失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }

  finishBatch(options = {}) {
    // 批量发送信息
    request('FinishIllegalBatch', options)
      .then(resp => {
        let message = resp.RetCode === 0 ? '结束批次成功' : resp.Message || resp.RetCode + "结束批失败"

        notification.open({
          message: message,
        });
      })
      .then(async ()=>{
        await this.refresh()
      })
      .catch(err => {
        // 报错
        notification['error']({
          message: '结束失败',
          description: err.message || '内部错误'
        })
        // 清空列表
        return;
      })
  }
  // 查询列表
  fetch = (options = {}) => {

    const { getList,setLoading } = this.props
    // 统一设置分页或者报错
    // 初始化options
    options.Offset = options.Offset || 0
    options.Limit = options.Limit || 20
    options.BatchId = this.state.batchId
    setLoading(true)
    getList(options)
    return Promise.resolve()
  }

  // 挂载前查询
  componentDidMount() {
    const { pagination = {} } = this.props
    this.fetch({
      BatchId: this.state.batchId,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
    })
  }
  UNSAFE_componentWillReceiveProps(nextProps) {
    if(nextProps.BUNotificationInfo!==this.props.BUNotificationInfo){
      this.setState({
        buIsNotified:nextProps.buIsNotified,
        BUNotificationInfo:nextProps.BUNotificationInfo
      })
    }
    if(nextProps.showAnalyProgress!==this.props.showAnalyProgress && nextProps.showAnalyProgress){
      interval= setInterval(()=>{
        console.log("轮训")
        this.fetch()
        this.props.getIllegalAnalyProgress({Id:this.state.batchId})
      },20000)
    }
  }

  enterLoading = () => {
    this.notify({ BatchId: this.state.batchId })
  }

  enterIconLoading = () => {
    this.setState({ iconLoading: true });
  }
  handleChangeSelect =(e)=>{
    this.setState({
      search:e
    })
  }
  // 导航
  breadcrumb() {
    const selectType = [
      {"name": "","value":"请选择搜索类型"},
      {"name":"IP","value": "IP"},
      {"name":"CompanyId","value": "公司ID"},
      {"name":"CompanyName","value": "公司名称"},
      {"name":"OrgId","value": "组织ID"},
      {"name":"URL","value": "URL"}
    ]
    return (
      <Row>
        <Col xs={24} sm={12} md={12} lg={12}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/IllegalMessage">违规信息通知</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              批次详情
            </Breadcrumb.Item>
          </Breadcrumb>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12}>
          <div style={{display:"flex"}}>
          <Select defaultValue="" style={{ width: 120 }} onChange={this.handleChangeSelect}>
              {
                selectType.map(item=>{
                  return <Option key={item.name} value={item.name}>{item.value}</Option>
                })
              }
          </Select>
          <Search
            placeholder="公司名，公司ID，组织ID，IP，Url"
            onSearch={this.searchValue}
          />
          </div>

        </Col>
      </Row>
    );
  }

  showAbnormalRecordModal() {
    const columns = [{
      title: 'IP',
      dataIndex: 'IP',
      key: 'IP',
    }, {
      title: 'Url',
      dataIndex: 'Url',
      key: 'Url',
      render:(value)=><p style={{wordBreak:'break-all'}}>{value}</p>
    }, {
      title: 'Resource',
      dataIndex: 'Resource',
      key: 'Resource'
    }];

    const modelObj = {
      title: '找不到公司信息的IP',
      width:600,
      content: (
        <Table dataSource={this.props.abnormalRecord} columns={columns} />
      ),
      onOk: () => {
        return request('RefetchCompanyInfoForIllegal', { BatchId: this.state.batchId })
          .then(() => {
            notification.open({
              message: '重新拉取数据成功',
            })
          })
          .catch(err => {
            notification['error']({
              message: '重新拉取数据失败',
              description: err.message || '内部错误'
            })
          })
      },
      okText: '重试拉取',
      cancelText: '关闭'
    }
    if (['SENDING', 'FINISHED', 'REFETCHING', 'SEND_FINISHED'].includes(this.props.batchStatus)) {
      delete modelObj.onOk;
      delete modelObj.cancelText;
      modelObj.okText = '关闭';

      Modal.warning(modelObj)
    } else {

      Modal.confirm(modelObj)
    }

  }

  handleRealnameTableChange = (pagination)=> {
    this.fetch({
      BatchId: this.state.batchId,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
    })
  }

  searchValue = (e)=> {
  let {search} = this.state
  let options = {
    BatchId: this.state.batchId,
    Offset: 0,
    Limit: 20,
   }
   console.log(e,search)
   if(search && e){
    options[search] = e
   }
   this.fetch(options)
  }

  reSendBUEmail({ Id }) {
    const { BUNotificationInfo } = this.state
    request('RetryIllegalNotifyBUById', { Id })
      .then((resp) => {
        if (resp.RetCode === 0) {
          const index = BUNotificationInfo.findIndex(item => item.Id === Id)
          BUNotificationInfo[index].TaskStatus = "发送中"

          this.setState({
            BUNotificationInfo: JSON.parse(JSON.stringify(BUNotificationInfo))
          })

          notification['open']({
            message: '发送中',
            description: '已经发送成功，状态会在刷新之后改变'
          })
          return
        }

        notification['error']({
          message: resp.RetCode,
          description: resp.message || '内部错误'
        })
      })
      .catch(err => {
        notification['error']({
          message: '通知失败',
          description: err.message || '内部错误'
        })
      })
  }
  async showNotifyBURecordModal() {
    await this.refresh()
    const columns = [{
      //   title: 'Bu',
      //   dataIndex: 'Bu',
      //   key: 'Bu',
      // }, {
      title: 'Email',
      dataIndex: 'Email',
      key: 'Email',
    }, {
      title: 'TaskStatus',
      dataIndex: 'TaskStatus',
      key: 'TaskStatus',
    }, {
      title: '手动重发',
      render: (val, row) => {

        if (['发送失败', '接收失败'].includes(row.TaskStatus)) {
          return (
            <div>
              <Button onClick={() => this.reSendBUEmail(row)}>重发</Button>
            </div>
          )
        }
        return (
          <div>
            <Button onClick={() => this.reSendBUEmail(row)} disabled={true}>重发</Button>
          </div>
        )
      }
    }];

    Modal.info({
      title: 'BU运营通知情况',
      width:600,
      content: (
        <Table dataSource={this.state.BUNotificationInfo} columns={columns} />
      )
    })
  }


  //如果批次状态是处理中，只可以批量发送通知、如果不是处理中，则可重发通知。
  //TODO 如果是已经完成状态禁用批量发送与批量重试
  notifyAndRetry() {
    const { rawList, loading, batchStatus } = this.props
    if (batchStatus !== "PROCESSED") {
      let disabledStatus = ['REFETCHING', 'FINISHED'].includes(batchStatus);
      // const rawList = this.props.rawList;
      // 判断list存在
      // 单击“批量发送通知”后，仅发送失败时，“批量重发”有效
      if (Array.isArray(rawList)) {
        disabledStatus = disabledStatus || rawList.some(item => item.status === "发送失败")
      }

      return (
        <Popconfirm title="是否确认批量发送？" loading={loading} onConfirm={() => this.retryNotify({ BatchId: this.state.batchId })}>
          <Button type="primary" disabled={disabledStatus} style={{marginLeft:10}}>
            批量重发
          </Button>
        </Popconfirm>
      )
    } else {
      return (
        <Popconfirm title="是否确认批量发送？" loading={loading} onConfirm={() => this.notify({ BatchId: this.state.batchId })}>
          <Button type="primary"  style={{marginLeft:10}}>
            批量发送通知
        </Button>
        </Popconfirm>
      )
    }
  }

  //BU通知发送与BU信息查看
  buNotifyAndSelect() {
    const { loading } = this.props
    const { BUNotificationInfo=[], buIsNotified } = this.state
    if (buIsNotified) {
      return (
        <Button type="primary" style={{marginLeft:10}} onClick={this.showNotifyBURecordModal.bind(this)}>BU运营通知情况</Button>
      )
    } else {
      return (
        <Popconfirm title="是否确认批量发送？" loading={loading} onConfirm={this.notifyBU}>
          <Button type="primary" disabled={BUNotificationInfo.length !== 0}  style={{marginLeft:10}}>
            批量通知BU运营
          </Button>
        </Popconfirm>)
    }
  }

  // 按钮行
  bulkSend() {
    const { abnormalRecord=[], batchStatus, loading } = this.props
    return (
      <div>
        {this.notifyAndRetry()}
        {this.buNotifyAndSelect()}
        <Button
          type="primary"
          disabled={abnormalRecord.length === 0 || ['REFETCHING'].includes(batchStatus)}
          onClick={this.showAbnormalRecordModal.bind(this)}
          style={{marginLeft:10}}
        >异常数据</Button>
        <Popconfirm title="是否确定完成了通知？" loading={loading} onConfirm={() => this.finishBatch({ BatchId: this.state.batchId })}>
          <Button type="primary" disabled={['REFETCHING', 'FINISHED'].includes(batchStatus)}  style={{marginLeft:10}} >
            完成处理
          </Button>
        </Popconfirm>
      </div>
    )
  }

  componentWillUnmount(){
    clearInterval(interval)
  }
  render() {
    const { list, pagination, showAnalyProgress, companyProgress = 0  } = this.props
    let { illegalBatchTableLoading } = this.state
  return (
    <Card bordered={false}>
      <div>
        <div>
          <Card bordered={false}>
            {this.breadcrumb()}
          </Card>
          {
            showAnalyProgress && companyProgress < 100 ?
            <ProgressSlider companyProgress={companyProgress}  showRegisterProgress={false}/>
            :null
          }
          <Card title="批次详情" style={{ marginBottom: 24 }} bordered={false} >
            {this.bulkSend()}
            <IllegalBatchTable
              onChange={this.handleRealnameTableChange}
              data={list}
              pagination={pagination}
              batchId={this.state.batchId}
              loading={illegalBatchTableLoading}
            />
          </Card>
        </div>
      </div>
    </Card>
    )
  }
}

BatchDetail.propTypes = {
  setList: PropTypes.func,
  rawList: PropTypes.array,
  abnormalRecord: PropTypes.array,
  list: PropTypes.array,
  setPagination: PropTypes.func,
  batchStatus: PropTypes.string,
  loading: PropTypes.boolean,
  pagination: PropTypes.object
}
const ListForm = Form.create()(BatchDetail);
const mapDispatchToProps = (dispatch) => {
  return {
      getList: (data) => dispatch(getIllegalBatchList(data)),
      setPagination: (data) =>  dispatch(setPagination("SET_ILLEGAL_BATCH_PAGINATION",data)),
      setLoading: (data) => dispatch(setLoading("SET_ILLEGAL_BATCH_LOADING",data)),
      setList: (data) => dispatch(setIllegalBatchList("SET_ILLEGAL_BATCH_LIST",data)),
      getIllegalAnalyProgress:(data) => dispatch(getIllegalAnalyProgress(data))
  }
}
const mapStateToProps = ({illegalBatchDetailReducer}) => {
  return {
      ...illegalBatchDetailReducer
  }
}
const IllegalBatchDetailContiner = connect(mapStateToProps,mapDispatchToProps)(ListForm)

export default IllegalBatchDetailContiner
