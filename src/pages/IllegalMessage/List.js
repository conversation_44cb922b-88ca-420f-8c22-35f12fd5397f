import React, { Component } from 'react';
import { message, Row, Col, DatePicker, Card, Form, Input, Icon, Button } from 'antd';
import IllegalMessageTable from '../../components/IllegalMessageTable'
import './List.css';
import request from '../../utils/request'
import ProgressSlider from '../../components/ProgressSlider';
import Upload from 'rc-upload';
import moment from 'moment';
import { parseInt } from 'lodash';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
let interval = null;
const getValue = obj => Object.keys(obj).map(key => obj[key]).join(',');

//状态枚举,将英文状态转成中文在前端显示
// const statusList = {
//   NEW: "解析中",
//   PROCESSED: "已解析",
//   SENDING: "发送中",
//   SEND_FINISHED: "发送完成",
//   FINISHED:"已完成",
//   REFETCHING:"重新获取中",
// }

class List extends Component {
  constructor(props){
    super(props)
    this.state = {
      // list: [], // 列表初始值
      // pagination: {
      //   current: 1,
      //   pageSize: 20,
      //   total: 0
      // },
      // loading: false,
      fileList: [],
      // formValues: {}
    };
  }

  // 查询列表
  fetch(options = {}) {

    const { getList,setLoading } = this.props
    // 初始化options中的分页
    options.Offset = options.Offset || 0
    options.Limit = options.Limit || 20
    setLoading(true)
    getList(options)
  }

  // 挂载前查询
  componentDidMount() {
    const { formValues={},pagination={} } = this.props
    const time = formValues.BeginTime?[moment(formValues.BeginTime*1000),moment(formValues.EndTime*1000)]:[]
    this.props.form.setFieldsValue({...formValues,time})
    this.fetch({...formValues,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps){
    if(nextProps.showAnalyProgress!==this.props.showAnalyProgress && nextProps.showAnalyProgress){
      interval= setInterval(()=>{
        console.log("轮训")
        this.props.getIllegalAnalyProgress({Id:this.props.list[0].Id})
      },20000)
    }
  }
  // 处理分页
  handleMessageTableChange = (pagination, filtersArg) => {
    const { formValues } = this.props;
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      const newObj = { ...obj };
      newObj[key] = getValue(filtersArg[key]);
      return newObj;
    }, {});

    const params = {
      ...formValues,
      Offset: (pagination.current - 1) * pagination.pageSize,
      Limit: pagination.pageSize,
      ...filters,
    };
    this.props.setPagination({Offset:params.Offset,Limit:params.Limit})

    this.fetch(params)
  }

  handleLogoUpload(batchFile) {
    let self = this
    //文件上传，使用FileReader读文件
    let reader = new FileReader();
    reader.readAsDataURL(batchFile);
    reader.onloadend = function () {
      //处理loadend事件。该事件在读取操作结束时（要么成功，要么失败）触发
      let fileBase64 = reader.result.split(",")[1]
      //reader.result中存放Base64编码,需要对原数据做处理，导入数据格式"data:text/csv;base64,aXAs5Z+f5ZCNLA0KMS4xLjEuMSxhdmkucWlhbmp1bnllLmNvbSwNCjIuMi4yLjIsYXYucWlhbmp1bnllLmNvbSwNCg=="
      request('CreateIllegalBatch', { File: fileBase64, Description: batchFile.name })
        .then(resp => {
          if (resp.RetCode === 0) {
            message.info("上传成功")
          } else {
            message.info("上传失败" + resp.Message)
          }
        })
        .then(
          self.fetch()
        )
        .catch(err => {
          // 报错
          message.info("上传失败", err)
        })
    };
    //Prevent file uploading
    return false;
  }
  // 重置搜索框
  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();

    this.props.setFormValues({})
    this.fetch()
  }

  // 展开、收起搜索框
  toggleForm = () => {
    this.setState({
      expandForm: !this.state.expandForm,
    });
  }

  // 搜索
  handleSearch = (e) => {
    e.preventDefault();
    const { form } = this.props;
    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
      };
      if (values.time && values.time[0]) {
        // 格式转换
        values.BeginTime = parseInt(moment(values.time[0]._d).format("X"), 10)
        values.EndTime = parseInt(moment(values.time[1]._d).format("X"), 10)

        // 查一天的时间实际压力从0点到23点，做startOf与endOf处理
        if (values.BeginTime === values.EndTime) {
          values.BeginTime = moment(values.time[0]._d).startOf('day').format("X")
          values.EndTime = moment(values.time[1]._d).endOf('day').format("X")
        }
      } else {
        delete values.time
      }
      if(values.CompanyId){
        values.CompanyId = parseInt(values.CompanyId, 10)
      }else{
        delete values.CompanyId
      }
      if(values.Id) {
        values.Id = parseInt(values.Id, 10)
      }else{
        delete values.Id
      }
      this.props.setFormValues(values)
      //去搜索条件中的前后空格， 没写
      const { pagination } = this.props
      this.fetch({
        Offset: (pagination.current - 1) * pagination.pageSize,
        Limit: pagination.pageSize,
        ...values
      })
    });
  }

  //复杂搜索框
  renderAdvancedForm() {
    const { getFieldDecorator } = this.props.form;
    return (
      <Form onSubmit={this.handleSearch} layout="inline" className="ant-advanced-search-form">
        <Row gutter={{ md: 8, lg: 24 }}>
          <Col span={8} key={1} style={{ display: 'block' }}>
            <FormItem label="批次ID">
              {getFieldDecorator('Id')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={2} style={{ display: 'block' }}>
            <FormItem label="公司ID">
              {getFieldDecorator('CompanyId')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={3} style={{ display: 'block' }}>
            <FormItem label="IP">
              {getFieldDecorator('IP')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          <Col span={8} key={4} style={{ display: 'block' }}>
            <FormItem label="网址">
              {getFieldDecorator('Url')(
                <Input style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>

          <Col span={8} key={5} style={{ display: 'block' }}>
            <FormItem label="时间">
              {getFieldDecorator('time')(
                <RangePicker
                  style={{width:'100%'}}
                  ranges={{ Today: [moment().startOf('day'), moment().endOf('day')], 'This Month': [moment().startOf('month'), moment().endOf('month')] }}
                  showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }}
                  format="YYYY-MM-DD"
                />)}
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col md={8} sm={24} offset={8}>
            <span style={{ textAlign: 'center', display:'block' }}>
              <Upload
                action="temp/"
                listType="picture-card"
                fileList={this.state.fileList}
                beforeUpload={this.handleLogoUpload.bind(this)}
              >
                <Button>
                  <Icon type="upload" /> 上传
                </Button>
              </Upload>
              <Button style={{ marginLeft: 8 }} htmlType="submit">查询</Button>
              <Button style={{ marginLeft: 8 }} onClick={this.handleFormReset}>重置</Button>
            </span>
          </Col>
        </Row>
      </Form>
    );
  }

  // 根据是否展开获取对应的表单形式
  renderForm() {
    return this.renderAdvancedForm()
  }
  componentWillUnmount(){
    console.log("清除")
    clearInterval(interval)
  }
  render() {

    const { loading, pagination, list, showAnalyProgress, companyProgress = 0 } = this.props
    return (
      <Card bordered={false}>
        <div>
          {/* <Card bordered={false}>
            {this.breadcrumb()}
          </Card> */}

          <Card title="搜索" style={{ marginBottom: 24 }} bordered={false} >
            {this.renderForm()}
          </Card>

          {
            showAnalyProgress && (companyProgress<100)?
            <ProgressSlider companyProgress={companyProgress} showRegisterProgress={false}/>
            :null
          }
          <Card title="结果" style={{ marginBottom: 24 }} bordered={false} >
            <IllegalMessageTable
              loading={loading}
              data={list}
              pagination={pagination}
              onChange={this.handleMessageTableChange}
            />
          </Card>
        </div>
      </Card>
    )
  }
}

const ListForm = Form.create()(List);
export default ListForm;
