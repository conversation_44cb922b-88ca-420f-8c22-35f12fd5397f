{"name": "he<PERSON><PERSON>-frontend", "version": "2.0.0", "main": "index.js", "scripts": {"dev": "webpack serve --config config/webpack.dev.js --mode development", "build": "webpack --config config/webpack.prod.js  --mode production"}, "author": "qianhua.xiong", "license": "ISC", "keywords": [], "description": "", "dependencies": {"@ant-design/icons": "^2.1.1", "@antv/data-set": "^0.11.8", "@babel/plugin-proposal-class-properties": "^7.17.0", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/polyfill": "^7.12.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.4", "antd": "3.26.19", "axios": "^1.2.2", "babel-eslint": "^10.1.0", "babel-preset-env": "^1.7.0", "base64-js": "^1.5.1", "bizcharts": "^3.5.10", "braft-editor": "^2.3.9", "braft-extensions": "^0.1.1", "braft-utils": "^3.0.12", "case-sensitive-paths-webpack-plugin": "^2.4.0", "clean-webpack-plugin": "^4.0.0", "css": "^3.0.0", "css-loader": "^6.6.0", "csvtojson": "^2.0.10", "dgram": "^1.0.1", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "eslint-loader": "^4.0.2", "eslint-plugin-react-native": "^4.0.0", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "file-saver": "^2.0.5", "fs": "0.0.1-security", "html-loader": "^3.1.0", "ip": "^1.1.5", "jszip": "^3.7.1", "less": "^4.1.2", "less-loader": "^10.2.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.6.0", "moment": "^2.29.1", "net": "^1.0.2", "node-polyfill-webpack-plugin": "^1.1.4", "object-assign": "^4.1.1", "optimize-css-assets-webpack-plugin": "^6.0.1", "path": "^0.12.7", "polyfill": "^0.1.0", "postcss-loader": "^6.2.1", "postcss-preset-env": "^7.4.2", "promise": "^8.1.0", "prop-types": "^15.8.1", "raf": "^3.4.1", "rc-texty": "^0.2.0", "react": "^18.2.0", "react-dev-utils": "^12.0.0", "react-dom": "^18.2.0", "react-redux": "^7.2.6", "react-refresh": "^0.11.0", "react-resizable": "^3.0.4", "react-router-dom": "^4.3.1", "react-viewer": "^3.2.2", "redux": "^4.1.2", "redux-immutable-state-invariant": "^2.1.0", "redux-thunk": "^2.4.1", "request": "^2.88.2", "sass": "^1.49.9", "sass-loader": "^12.6.0", "style-loader": "^3.3.1", "sw-precache-webpack-plugin": "^0.11.4", "thread-loader": "^3.0.4", "tls": "0.0.1", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^4.1.1", "uuid": "^8.3.2", "uuid-v4": "^0.1.0", "webpack": "^5.69.1", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.2", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.8.0", "webpack-parallel-uglify-plugin": "^2.0.0", "whatwg-fetch": "^3.6.2", "workbox-webpack-plugin": "^6.5.1", "xlsx": "^0.18.3"}, "babel": {"presets": [["@babel/preset-env"]], "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-proposal-class-properties", "syntax-dynamic-import"]}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "ajv": "^7.2.4", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.3", "babel-plugin-syntax-dynamic-import": "^6.18.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.3.0", "html-webpack-plugin": "^5.5.0", "speed-measure-webpack-plugin": "^1.5.0", "webpack-dev-server": "^4.7.4"}, "sideEffects": ["*.css", "*.less"], "eslintConfig": {"extends": "airbnb-base", "parser": "babel-es<PERSON>"}}