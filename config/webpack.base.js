const {
    CleanWebpackPlugin
} = require('clean-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');
const eslintFormatter = require('react-dev-utils/eslintFormatter');
const InterpolateHtmlPlugin = require('react-dev-utils/InterpolateHtmlPlugin');
const webpack = require('webpack');
const getClientEnvironment = require('./env');
const env = getClientEnvironment('');
const os = require('os')
// cpu核数
const threads = os.cpus().length
// const {
//     BundleAnalyzerPlugin
// } = require('webpack-bundle-analyzer');
const paths = require('./paths');
const path = require('path');

// 复用loader加载器
const commonCssLoader = [
    MiniCssExtractPlugin.loader,
    'css-loader',
];
module.exports = {
    entry: [require.resolve('./polyfills'), paths.appIndexJs,],
    module: {
        rules: [
            {
                test: /\.(js|jsx|mjs)$/,
                enforce: 'pre',
                use: [
                    {
                        options: {
                            formatter: eslintFormatter,
                            eslintPath: require.resolve('eslint'),

                        },
                        loader: require.resolve('eslint-loader'),
                    },
                ],
                include: paths.src,
                exclude: /node_modules/,
            },
            {
                oneOf: [{
                    test: /\.(css)$/,
                    use: [...commonCssLoader],
                },
                {
                    test: /\.(less)$/,
                    exclude: /node_modules/,
                    use: [
                        ...commonCssLoader,
                        {
                            loader: 'less-loader',
                            options: {
                                lessOptions: {
                                    javascriptEnabled: true,
                                }
                            }
                        }
                    ]
                },
                {
                    test: /\.(sass)$/,
                    exclude: /node_modules/,
                    use: [...commonCssLoader, 'sass-loader'],
                },
                {
                    test: /\.(png|svg|jpg|jpeg|gif|bmp)$/,
                    type: 'asset/resource',
                    generator:{
                        filename: 'static/image/[contenthash:10].[ext]',
                    },
                },
                {
                    test: /\.(js|jsx|ts|tsx)$/,
                    include: paths.src,
                    exclude: /node_modules/,
                    use: [{
                        loader: 'babel-loader',
                        options: {
                            presets: ['@babel/preset-react', '@babel/preset-env'],
                            plugins: ['@babel/transform-runtime', '@babel/plugin-proposal-class-properties'],
                            // 开启babel缓存
                            // 第二次构建时，会读取之前的缓存
                            cacheDirectory: true,
                            cacheCompression: false // 缓存文件不要压缩
                        }
                    },{
                        loader: 'thread-loader',//开启多进程
                        options: {
                          workers: threads //数量
                        }
                    }]
                }]
            },
            //处理其他文件
            {
                exclude: [/\.(js|jsx|ts|tsx|html|css|less|sass|png|svg|jpg|jpeg|gif|ico|bmp|mjs|json)$/],
                use: [{
                    loader: require.resolve('file-loader'),
                    options: {
                        name: 'static/file/[contenthash:10].[ext]' //[hash:10]对文件重命名，取hash前10位 [ext]取文件原来的扩展名
                    }
                }]
            }
        ],
    },
    resolve: {
        extensions: ['.ts', '.tsx', '.js', '.json', '.jsx', '.css', '.less', '.sass', '.web.jsx', '.web.js', '.mjs'],
        alias: {
            'react-native': 'react-native-web',
            '@': path.resolve(__dirname, '../src/')
        },
        fallback: {
            fs: false
        },
    },
    plugins: [
        new ESLintPlugin({
            extensions: ['jsx', 'js', 'mjs', 'ts', 'tsx'],
            // 指定检查文件的根目录
            context: paths.src,
            exclude: 'node_modules',// 默认值
            cache: true, // 开启缓存
            cacheLocation: path.resolve(__dirname, '../node_modules/.cache/.eslintcache'), // 缓存目录
            threads,//开启多进程
        }),
        new MiniCssExtractPlugin({ filename: 'static/css/[name].[contenthash:10].css' }),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: paths.appHtml,
            favicon: 'public/favicon.ico',
            minify: {
                collapseWhitespace: true, //去掉空格
                removeComments: true, //删除注释
                removeAttributeQuotes: true, //删除双引号
                removeEmptyAttributes: true, //删除声明了但是没赋值的属性 let a;
                minifyCSS: true,
                minifyJS: true,
                minifyURLs: true,
                removeTagWhitespace: true
            }
        }),
        new InterpolateHtmlPlugin(HtmlWebpackPlugin, env.raw),
        new webpack.ProvidePlugin({
            "React": "react",
        }),
        new NodePolyfillPlugin(),
        new webpack.HotModuleReplacementPlugin(),
        new webpack.IgnorePlugin({
            resourceRegExp: /^\.\/locale$/,   // 忽略content设置的库中的某个文件夹
            contextRegExp: /moment$/, // 要被忽略某部分内容的库
        }),
        new CleanWebpackPlugin(),
       //new BundleAnalyzerPlugin(),
    ],
    node: {
        global: true,
        __filename: true,
        __dirname: true,
    },
    performance: {
        hints: false,
    },
}
