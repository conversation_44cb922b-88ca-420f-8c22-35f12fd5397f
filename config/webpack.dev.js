//开发环境
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const { HotModuleReplacementPlugin } = require('webpack');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const paths = require('./paths');
const { merge } = require('webpack-merge');
const common = require('./webpack.base');
const { DefinePlugin } = require('webpack');
const devConfig = {
    mode: 'development',
    devtool: 'cheap-module-source-map',
    output: {
      filename: 'build/static/js/[name].chunk.js',
      path: paths.build,
      publicPath: '/',
    },
    devServer: {
        static: '../build',
        open: true,
        hot: true,
        historyApiFallback: true,
        port: 8000,
        proxy: {
          // "/": {
          //   target: 'http://4050.hegui-backend.prj-crd.svc.c1.u4',
          //   secure: false,
          //   bypass: function(req, res, proxyOptions) {
          //    if (req.headers.accept.indexOf('html') !== -1) {
          //     console.log('Skipping proxy for browser request.');
          //     return '/index.html';
          //    }
          //   }
          // },
          "/api": {  // 发送请求的时候 react会自动去找这个api，匹配这个路径，然后去发送对的请求
            target: 'http://6262.new-auth-gray-test.prj-crd.svc.c1.u4',
            changeOrigin: true, //控制服务器接收到的请求头中host字段的值
            pathRewrite: {'^/api': ''}, // 跟上面匹配，这个api只是找这个路径用的，实际接口中没有api，所以找个目标地址后，要把api给替换成空
            secure: false
          }
        }
    },
    target: 'web',
    plugins: [
       new HotModuleReplacementPlugin(),
       new ReactRefreshWebpackPlugin(),
       new DefinePlugin({
        'process.env': {
          'NODE_ENV': JSON.stringify('development'),
        }
      }),
      new CaseSensitivePathsPlugin(),
    ],
};

module.exports = merge(common, devConfig);
