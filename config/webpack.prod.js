//生产环境
const { merge } = require('webpack-merge');
const common = require('./webpack.base');
const { DefinePlugin } = require('webpack');
const OptimizeCssAssetsWebpackPlugin = require('optimize-css-assets-webpack-plugin');
const UglifyjsWebpackPlugin = require('uglifyjs-webpack-plugin');
const { WebpackManifestPlugin } = require('webpack-manifest-plugin');
// const SpeedMeasureWebpackPlugin = require('speed-measure-webpack-plugin')
// const smp = new SpeedMeasureWebpackPlugin();
// smp.wrap()
const paths = require('./paths');
const path = require('path');
const os = require('os')
// cpu核数
const threads = os.cpus().length;
const publicUrl = paths.publicURLOrPath;
const prodConfig = {
    mode: 'production',
    devtool: false,
    // devtool: 'cheap-module-source-map',
    output: {
      path: paths.build,
      publicPath: publicUrl || 'auto',
      filename: 'static/js/[name].[contenthash:10].js',
      devtoolModuleFilenameTemplate: info =>
      path
        .relative(paths.src, info.absoluteResourcePath)
        .replace(/\\/g, '/'),
    },
    plugins: [
        new DefinePlugin({
          'process.env': {
            'NODE_ENV': JSON.stringify('production'),
          }
        }),
        new WebpackManifestPlugin({
          fileName: 'asset-manifest.json',
        }),
    ],
    optimization: {
      splitChunks: {
        chunks: 'all', // 必须三选一： "initial" | "all"(默认就是all) | "async"
        minSize: 0, // 最小尺寸，默认0
        minChunks: 1, // 最小 chunk ，默认1
        name: () => {}, // 名称，此选项可接收 function
        cacheGroups: { // 这里开始设置缓存的 chunks
          vendor: { // key 为entry中定义的 入口名称
            test: /node_modules/, // 正则规则验证，如果符合就提取 chunk
            name: 'vendor', // 要缓存的 分隔出来的 chunk 名称
            minSize: 0,
            minChunks: 1,
          },
          default:{
            name:'common',
            test:/(utils|libs)/,
            minSize: 3 * 1024,
            minChunks:1
          }
        },
      },
      //tree shaking
      usedExports:true,
      minimize: true,
      minimizer: [
          // 压缩css(产生问题 js压缩不在压缩)
          new OptimizeCssAssetsWebpackPlugin(),
          // 压缩js
          new UglifyjsWebpackPlugin({
              cache: true, //是否使用缓存
              parallel: threads, //是否并发编译,
              // sourceMap: true, //是否生成map文件
          }),
      ]
  },
};
module.exports = merge(common, prodConfig);
